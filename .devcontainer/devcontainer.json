// For format details, see https://aka.ms/devcontainer.json. For config options, see the README at:
// https://github.com/microsoft/vscode-dev-containers/tree/v0.233.0/containers/rust
{
  "name": "Git, <PERSON>er, Node, Rust",
  "build": {
    "dockerfile": "Dockerfile",
    "args": {
      // Use the VARIANT arg to pick a Debian OS version: buster, bullseye
      // Use bullseye when on local on arm64/Apple Silicon.
      "VARIANT": "buster"
    }
  },
  "runArgs": ["--cap-add=SYS_PTRACE", "--security-opt", "seccomp=unconfined"],

  // Set *default* container specific settings.json values on container create.
  "settings": {
    "lldb.executable": "/usr/bin/lldb",
    // VS Code don't watch files under ./target
    "files.watcherExclude": {
      "**/target/**": true
    },
    "rust-analyzer.checkOnSave.command": "clippy",
    "teminal.integrated.shell.linux": "/bin/bash"
  },

  // Add the IDs of extensions you want installed when the container is created.
  "extensions": [
    "vadimcn.vscode-lldb",
    "mutantdino.resourcemonitor",
    "rust-lang.rust-analyzer",
    "tamasfe.even-better-toml",
    "serayuzgur.crates"
  ],

  // Use 'forwardPorts' to make a list of ports inside the container available locally.
  // "forwardPorts": [],

  // Use 'postCreateCommand' to run commands after the container is created.
  // "postCreateCommand": "rustc --version",

  // Comment out to connect as root instead. More info: https://aka.ms/vscode-remote/containers/non-root.
  // "remoteUser": "vscode",
  "features": {
    "docker-in-docker": "latest",
    "git": "latest",
    "homebrew": "latest",
    "node": "lts"
  }
}
