#[cfg(test)]
mod tests {
    use auth_contracts::api_key::ApiKeyManager;
    use auth_contracts::password::PasswordHasher;
    
    use crate::bcrypt_password::BcryptPasswordHasher;
    use crate::prefixed_api_key::PrefixedApiKeyManager;

    #[test]
    fn test_api_key_generation() {
        let manager = PrefixedApiKeyManager;
        let api_key = manager.generate("test").unwrap();
        
        assert_eq!(api_key.prefix(), "test");
        assert!(!api_key.short_token().is_empty());
        assert!(!api_key.long_token_hash().is_empty());
        
        // Test splitting
        let api_key_str = api_key.api_key().to_string();
        let split_key = manager.split(&api_key_str).unwrap();
        
        assert_eq!(split_key.prefix(), api_key.prefix());
        assert_eq!(split_key.short_token(), api_key.short_token());
        assert_eq!(split_key.long_token_hash(), api_key.long_token_hash());
    }
    
    #[test]
    fn test_password_hasher() {
        let hasher = BcryptPasswordHasher::new(4); // Low cost for testing
        let password = "test_password";
        
        let hashed = hasher.hash(password).unwrap();
        assert!(hasher.verify(password, &hashed).is_ok());
        assert!(hasher.verify("wrong_password", &hashed).is_err());
    }
}
