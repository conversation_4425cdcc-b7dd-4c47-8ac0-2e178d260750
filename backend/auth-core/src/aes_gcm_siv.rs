use aes_gcm_siv::{
    aead::{Aead, KeyInit},
    Aes256GcmSiv, Key, Nonce,
};
use anyhow::Context;
use auth_contracts::encryption::{DataEncryptor, EncryptionError};
use base64::{engine::general_purpose::URL_SAFE_NO_PAD, Engine as _};
use rand::{rngs::OsRng, RngCore};

const NONCE_SIZE: usize = 12;

#[derive(Clone)]
pub struct AesGcmSivEncryptor {
    cipher: Aes256GcmSiv,
}

impl DataEncryptor for AesGcmSivEncryptor {
    fn encrypt(&self, plaintext: &[u8]) -> Result<Vec<u8>, EncryptionError> {
        let mut nonce_bytes = [0u8; NONCE_SIZE];
        OsRng.fill_bytes(&mut nonce_bytes);
        let nonce = Nonce::from_slice(&nonce_bytes);

        let ciphertext = self
            .cipher
            .encrypt(nonce, plaintext)
            .map_err(|e| EncryptionError::EncryptionFailed(e.to_string()))?;

        let mut result = Vec::with_capacity(NONCE_SIZE + ciphertext.len());
        result.extend_from_slice(&nonce_bytes);
        result.extend_from_slice(&ciphertext);

        Ok(result)
    }

    fn decrypt(&self, ciphertext: &[u8]) -> Result<Vec<u8>, EncryptionError> {
        if ciphertext.len() < NONCE_SIZE {
            return Err(EncryptionError::DecryptionFailed(
                "Ciphertext is too short".to_string(),
            ));
        }

        let nonce = Nonce::from_slice(&ciphertext[..NONCE_SIZE]);
        let actual_ciphertext = &ciphertext[NONCE_SIZE..];

        self.cipher
            .decrypt(nonce, actual_ciphertext)
            .map_err(|e| EncryptionError::DecryptionFailed(e.to_string()))
    }

    fn encrypt_to_string(&self, plaintext: &[u8]) -> Result<String, EncryptionError> {
        let encrypted = self.encrypt(plaintext)?;
        Ok(URL_SAFE_NO_PAD.encode(encrypted))
    }

    fn decrypt_from_string(&self, encoded: &str) -> Result<Vec<u8>, EncryptionError> {
        let decoded = URL_SAFE_NO_PAD
            .decode(encoded)
            .context("Failed to base64 decode the encrypted data")
            .map_err(|e| EncryptionError::DecryptionFailed(e.to_string()))?;

        self.decrypt(&decoded)
    }
}

impl AesGcmSivEncryptor {
    /// Creates a new `AesGcmSivEncryptor` from a base64-encoded key.
    pub fn new(key: &str) -> Result<Self, EncryptionError> {
        let key_bytes = URL_SAFE_NO_PAD
            .decode(key)
            .context("Failed to base64 decode the key")
            .map_err(|e| EncryptionError::InvalidKey(e.to_string()))?;

        if key_bytes.len() != 32 {
            return Err(EncryptionError::InvalidKey(format!(
                "Decoded key must be 32 bytes, got {}",
                key_bytes.len()
            )));
        }

        let key = Key::<Aes256GcmSiv>::from_slice(&key_bytes);
        let cipher = Aes256GcmSiv::new(key);

        Ok(Self { cipher })
    }
}
