use anyhow::Context;
use auth_contracts::password::{PasswordError, PasswordHasher};
use bcrypt::{hash, verify, BcryptError};

#[derive(Clone)]
pub struct BcryptPasswordHasher {
    cost: u32,
}

impl PasswordHasher for BcryptPasswordHasher {
    fn verify(&self, input: &str, hashed: &str) -> Result<(), PasswordError> {
        let result = verify(input, hashed).context("Internal error in password verify")?;

        if !result {
            return Err(PasswordError::InvalidPassword);
        }

        Ok(())
    }

    fn hash(&self, password: &str) -> Result<String, PasswordError> {
        let result = hash(password, self.cost);

        match result {
            Ok(hash) => Ok(hash),
            Err(BcryptError::CostNotAllowed(val)) => Err(PasswordError::InvalidSaltCost(format!(
                "Salt cost should be between 1 and 31, received: {val}",
            ))),
            Err(BcryptError::InvalidCost(val)) => Err(PasswordError::InvalidSaltCost(val)),
            err => err
                .context("Internal error in password verify")
                .map_err(Into::into),
        }
    }
}

impl BcryptPasswordHasher {
    pub fn new(cost: u32) -> Self {
        Self { cost }
    }
}
