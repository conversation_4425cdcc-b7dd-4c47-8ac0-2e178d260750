use anyhow::Result;
use auth_contracts::api_key::{<PERSON><PERSON><PERSON><PERSON>, <PERSON>pi<PERSON>eyManager};
use prefixed_api_key::{
    sha2::{Digest, Sha256},
    PrefixedApiKey, PrefixedApiKeyController,
};

pub struct PrefixedApiKeyManager;

impl ApiKeyManager for PrefixedApiKeyManager {
    fn generate(&self, prefix: &str) -> Result<ApiKey> {
        let builder = PrefixedApiKeyController::configure()
            .prefix(prefix.to_owned())
            .seam_defaults()
            .finalize();

        let mut controller = builder.unwrap();

        let (keys, hash_long) = controller.generate_key_and_hash();

        Ok(ApiKey::new(
            keys.prefix().to_string(),
            keys.short_token().to_string(),
            hash_long,
            keys.to_string(),
        ))
    }

    fn split(&self, api_key: &str) -> Result<ApiKey> {
        let keys = PrefixedApiKey::from_string(api_key)?;
        let mut digest = Sha256::new();
        let long_token_hash = keys.long_token_hashed(&mut digest);

        Ok(ApiKey::new(
            keys.prefix().to_string(),
            keys.short_token().to_string(),
            long_token_hash,
            keys.to_string(),
        ))
    }
}
