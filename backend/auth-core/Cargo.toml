[package]
name = "auth-core"
version = "0.1.0"
edition = "2021"

[dependencies]
auth-contracts = { path = "../auth-contracts" }
aes-gcm-siv = "0.11"
anyhow = "1.0"
async-trait = "0.1"
base64 = "0.21"
bcrypt = "0.13"
chrono = "0.4"
isahc = "1.7"
jsonwebtoken = "8.1"
prefixed-api-key = { version = "0.4", features = ["sha2"] }
rand = "0.8"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
sha2 = "0.10"
