#[derive(Debug, thiserror::Error)]
pub enum EncryptionError {
    #[error("Failed to encrypt data: {0}")]
    EncryptionFailed(String),
    #[error("Failed to decrypt data: {0}")]
    DecryptionFailed(String),
    #[error("Invalid key or nonce: {0}")]
    InvalidKey(String),
    #[error("Internal encryption error: {0}")]
    InternalError(#[from] anyhow::Error),
}

pub trait DataEncryptor: Send + Sync {
    /// Encrypts data using AES-GCM-SIV algorithm
    fn encrypt(&self, plaintext: &[u8]) -> Result<Vec<u8>, EncryptionError>;

    /// Decrypts data that was encrypted with AES-GCM-SIV algorithm
    fn decrypt(&self, ciphertext: &[u8]) -> Result<Vec<u8>, EncryptionError>;

    /// Encrypts data and returns it as a base64-encoded string
    fn encrypt_to_string(&self, plaintext: &[u8]) -> Result<String, EncryptionError>;

    /// Decrypts a base64-encoded string that was encrypted with AES-GCM-SIV algorithm
    fn decrypt_from_string(&self, encoded: &str) -> Result<Vec<u8>, EncryptionError>;
}
