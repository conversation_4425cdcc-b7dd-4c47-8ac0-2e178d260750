use mockall::automock;
use serde::{Deserialize, Serialize};
use uuid::Uuid;

#[derive(Debu<PERSON>, <PERSON><PERSON>, PartialEq)]
pub struct AuthenticatedUser {
    user_id: Uuid,
    organisation_id: Uuid,
    team_ids: Vec<Uuid>,
    permissions: Vec<i32>,
}

impl AuthenticatedUser {
    pub fn new(
        user_id: Uuid,
        organisation_id: Uuid,
        team_ids: Vec<Uuid>,
        permissions: Vec<i32>,
    ) -> Self {
        Self {
            user_id,
            organisation_id,
            team_ids,
            permissions,
        }
    }

    pub fn user_id(&self) -> Uuid {
        self.user_id
    }

    pub fn organisation_id(&self) -> Uuid {
        self.organisation_id
    }

    pub fn team_ids(&self) -> &[Uuid] {
        &self.team_ids
    }

    pub fn permissions(&self) -> &[i32] {
        &self.permissions
    }
}

#[automock]
pub trait JwtEncoder: Send + Sync {
    fn encode_invitation(&self, claims: &InvitationToken) -> anyhow::Result<String>;

    fn decode_invitation(&self, token: &str) -> anyhow::Result<InvitationToken>;

    fn encode_activation(&self, claims: &ActivationToken) -> anyhow::Result<String>;

    fn decode_activation(&self, token: &str) -> anyhow::Result<ActivationToken>;

    fn encode_access(
        &self,
        user: &AuthenticatedUser,
        token_lifetime_seconds: u32,
    ) -> anyhow::Result<String>;

    fn decode_access(&self, token: &str) -> anyhow::Result<AccessToken>;

    fn encode_authentication(
        &self,
        user: &AuthenticatedUser,
        persona_id: &str,
    ) -> anyhow::Result<String>;

    fn decode_authentication(&self, token: &str) -> anyhow::Result<GuestToken>;

    fn decode_app(&self, token: &str) -> anyhow::Result<AppToken>;
}

#[derive(Serialize, Deserialize)]
pub struct InvitationToken {
    pub invited_provider_id: Uuid,
}

#[derive(Serialize, Deserialize)]
pub struct ActivationToken {
    pub user_id: Uuid,
    pub exp: i64,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AccessToken {
    pub user_id: Uuid,
    pub organisation_id: Uuid,
    pub team_ids: Vec<Uuid>,
    pub exp: i64,
    pub permissions: Vec<i32>,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct AppToken {
    // The user type, e.g. "booking"
    pub sub: String,
    // The org name, e.g. for evuhus.leviosa.is the org would be evuhus
    pub org: String,
    // the token expiration, set by the app
    pub exp: i64,
}

#[derive(Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct GuestToken {
    // The user type, e.g. "**********"
    pub sub: String,
    // The org id,
    pub org: Uuid,
    // the token expiration, set by the app
    pub exp: i64,
}
