#[derive(Debug, thiserror::Error)]
pub enum PasswordError {
    #[error("Invalid password")]
    InvalidPassword,
    #[error("Failed to hash password because of an invalid salt cost: {0}")]
    InvalidSaltCost(String),
    #[error("Failed to hash or verify password: {0}")]
    InternalError(#[from] anyhow::Error),
}

pub trait PasswordHasher: Send + Sync {
    fn verify(&self, input: &str, hashed: &str) -> Result<(), PasswordError>;
    fn hash(&self, password: &str) -> Result<String, PasswordError>;
}
