use anyhow::Result;

pub trait ApiKeyManager: Send + Sync {
    fn generate(&self, prefix: &str) -> Result<ApiKey>;

    fn split(&self, api_key: &str) -> Result<ApiKey>;
}

/// The api key has 3 parts, the prefix, the short token and the long token.
/// A hashed version of the long token should be stored along with the prefix and short token.
/// The api_key field stores the full api key and should be retured to the user. It should not be stored.
pub struct ApiKey {
    api_key: String,
    prefix: String,
    short_token: String,
    long_token_hash: String,
}

impl ApiKey {
    pub fn new(
        prefix: String,
        short_token: String,
        long_token_hash: String,
        api_key: String,
    ) -> Self {
        Self {
            prefix,
            short_token,
            long_token_hash,
            api_key,
        }
    }

    pub fn api_key(&self) -> &str {
        &self.api_key
    }

    pub fn prefix(&self) -> &str {
        &self.prefix
    }

    pub fn short_token(&self) -> &str {
        &self.short_token
    }

    pub fn long_token_hash(&self) -> &str {
        &self.long_token_hash
    }
}
