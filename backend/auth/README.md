# Leviosa Authentication Modules

This directory contains two crates that handle authentication for the Leviosa application:

1. `auth-contracts`: Contains interfaces and data structures for authentication
2. `auth-core`: Contains implementations of these interfaces

## Key Features

- **API Key Management**: Generate, validate, and split API keys
- **JWT Token Handling**: Encode and decode various types of JWT tokens
- **Password Hashing**: Securely hash and verify passwords
- **Data Encryption**: Encrypt and decrypt sensitive data
- **Electronic ID Authentication**: Authenticate using the Icelandic electronic ID system

## Getting Started

### Configuration

The auth module no longer reads environment variables directly. Instead, configuration is injected through configuration structs. Your main application should handle environment variable parsing and create the appropriate configuration structs.

### Basic Usage

The authentication modules are used through the `AuthFactory` with configuration injection:

```rust
use anyhow::Result;
use auth_core::AuthFactory;
use auth_contracts::config::{JwtConfig, PasswordConfig, ElectronicIdConfig, EncryptionConfig, ApiKeyConfig};
use uuid::Uuid;

fn main() -> Result<()> {
    // Initialize the auth factory
    let factory = AuthFactory::new();

    // Create configuration structs (these would come from your app's environment parsing)
    let jwt_config = JwtConfig {
        private_key: "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----".to_string(),
        public_key: "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----".to_string(),
        token_expiration_seconds: 300,
    };

    let password_config = PasswordConfig {
        salt_cost: 12,
    };

    let api_key_config = ApiKeyConfig {
        organization_id: Some(Uuid::new_v4()),
    };

    // Create the authentication components with configuration
    let jwt_encoder = factory.create_jwt_encoder(jwt_config);
    let password_hasher = factory.create_password_hasher(password_config);
    let api_key_manager = factory.create_api_key_manager(api_key_config);

    // Optional components (if configuration is available)
    if let Some(encryption_config) = get_encryption_config() {
        let data_encryptor = factory.create_data_encryptor(encryption_config)?;
    }

    if let Some(electronic_id_config) = get_electronic_id_config() {
        let electronic_id = factory.create_icelandic_electronic_id(electronic_id_config);
    }

    // Now you can use these components for authentication

    Ok(())
}

fn get_encryption_config() -> Option<EncryptionConfig> {
    // Your app's logic to create encryption config from environment
    None
}

fn get_electronic_id_config() -> Option<ElectronicIdConfig> {
    // Your app's logic to create electronic ID config from environment
    None
}
```

See the `examples` directory for more detailed usage examples.

## Development Keys

For development and testing, you can use the provided development keys:

- `dev.private.key`: RSA private key for JWT signing
- `dev.public.pem`: RSA public key for JWT verification

**Warning**: Never use these keys in production!

## Testing

Run the tests for both crates with:

```bash
cd auth-contracts
cargo test

cd ../auth-core
cargo test
```

## Examples

Run the usage example with:

```bash
cd auth
cargo run --example usage_example
```
