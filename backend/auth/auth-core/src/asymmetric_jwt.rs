use anyhow::Context;
use auth_contracts::config::JwtConfig;
use auth_contracts::jwt::{
    AccessToken, ActivationToken, AppToken, AuthenticatedUser, GuestToken, InvitationToken,
    JwtEncoder,
};
use chrono::Utc;
use jsonwebtoken::{decode, encode, Al<PERSON><PERSON>m, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Val<PERSON><PERSON>};
use serde::{Deserialize, Serialize};

type JwtError = jsonwebtoken::errors::Error;

#[derive(Clone)]
pub struct AsymmetricJwtEncoder {
    private_key: String,
    public_key: String,
    token_expiration_seconds: u32,
}

impl JwtEncoder for AsymmetricJwtEncoder {
    fn encode_invitation(&self, claims: &InvitationToken) -> anyhow::Result<String> {
        self.encode(claims).map_err(Into::into)
    }

    fn decode_invitation(&self, token: &str) -> anyhow::Result<InvitationToken> {
        self.decode(token).map_err(Into::into)
    }

    fn encode_activation(&self, claims: &ActivationToken) -> anyhow::Result<String> {
        self.encode(claims).map_err(Into::into)
    }

    fn decode_activation(&self, token: &str) -> anyhow::Result<ActivationToken> {
        self.decode(token).map_err(Into::into)
    }

    fn encode_access(
        &self,
        user: &AuthenticatedUser,
        token_lifetime_seconds: u32,
    ) -> anyhow::Result<String> {
        let exp = Utc::now()
            .checked_add_signed(chrono::Duration::seconds(i64::from(token_lifetime_seconds)))
            .context("Access token expiration time is invalid")?
            .timestamp();

        let claims = AccessToken {
            user_id: user.user_id(),
            organisation_id: user.organisation_id(),
            team_ids: user.team_ids().to_vec(),
            permissions: user.permissions().to_vec(),
            exp,
        };

        self.encode(&claims).context(format!(
            "failed to encode access token for user {}",
            user.user_id()
        ))
    }

    fn decode_access(&self, token: &str) -> anyhow::Result<AccessToken> {
        self.decode(token).map_err(Into::into)
    }

    fn encode_authentication(
        &self,
        user: &AuthenticatedUser,
        persona_id: &str,
    ) -> anyhow::Result<String> {
        let claims = GuestToken {
            sub: persona_id.to_string(),
            org: user.organisation_id(),
            exp: Utc::now()
                .checked_add_signed(chrono::Duration::hours(1))
                .unwrap()
                .timestamp(),
        };

        self.encode(&claims).context(format!(
            "failed to encode authentication token for persona id {persona_id}"
        ))
    }

    fn decode_authentication(&self, token: &str) -> anyhow::Result<GuestToken> {
        self.decode(token).map_err(Into::into)
    }

    fn decode_app(&self, token: &str) -> anyhow::Result<AppToken> {
        self.decode(token).map_err(Into::into)
    }
}

impl AsymmetricJwtEncoder {
    pub fn new(config: JwtConfig) -> Self {
        Self {
            private_key: config.private_key.replace("\\n", "\n"),
            public_key: config.public_key.replace("\\n", "\n"),
            token_expiration_seconds: config.token_expiration_seconds,
        }
    }

    /// Get the default token expiration time in seconds
    pub fn default_token_expiration_seconds(&self) -> u32 {
        self.token_expiration_seconds
    }

    pub fn encode<T: Serialize>(&self, claims: &T) -> Result<String, JwtError> {
        encode(
            &Header::new(Algorithm::RS256),
            claims,
            &EncodingKey::from_rsa_pem(self.private_key.as_bytes()).unwrap(),
        )
    }

    fn decode<T: for<'de> Deserialize<'de>>(&self, token: &str) -> Result<T, JwtError> {
        let mut validation = Validation::new(Algorithm::RS256);
        validation.set_required_spec_claims(&[""]);
        let claims = decode::<T>(
            token,
            &DecodingKey::from_rsa_pem(self.public_key.as_bytes()).unwrap(),
            &validation,
        )?
        .claims;
        Ok(claims)
    }
}
