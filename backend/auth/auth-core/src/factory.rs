use anyhow::Result;
use std::sync::Arc;

use crate::{
    aes_gcm_siv::AesGcmSivEncryptor, asymmetric_jwt::AsymmetricJwtEncoder,
    bcrypt_password::BcryptPasswordHasher, icelandic_electronic_id::IcelandicElectronicId,
    prefixed_api_key::PrefixedApiKeyManager,
};

use auth_contracts::{
    api_key::ApiKeyManager,
    config::{ApiKeyConfig, ElectronicIdConfig, EncryptionConfig, JwtConfig, PasswordConfig},
    electronic_id::ElectronicId,
    encryption::DataEncryptor,
    jwt::JwtEncoder,
    password::PasswordHasher,
};

/// Factory for creating authentication components
#[derive(Clone)]
pub struct AuthFactory;

impl AuthFactory {
    /// Create a new AuthFactory
    pub fn new() -> Self {
        Self
    }

    /// Create a JWT encoder using the provided configuration
    pub fn create_jwt_encoder(&self, config: JwtConfig) -> Arc<dyn JwtEncoder> {
        Arc::new(AsymmetricJwtEncoder::new(config))
    }

    /// Create a password hasher using the provided configuration
    pub fn create_password_hasher(&self, config: PasswordConfig) -> Arc<dyn PasswordHasher> {
        Arc::new(BcryptPasswordHasher::new(config.salt_cost))
    }

    /// Create an API key manager using the provided configuration
    pub fn create_api_key_manager(&self, _config: ApiKeyConfig) -> Arc<dyn ApiKeyManager> {
        Arc::new(PrefixedApiKeyManager)
    }

    /// Create a data encryptor using the provided configuration
    pub fn create_data_encryptor(
        &self,
        config: EncryptionConfig,
    ) -> Result<Arc<dyn DataEncryptor>> {
        let encryptor = AesGcmSivEncryptor::new(config)?;
        Ok(Arc::new(encryptor))
    }

    /// Create an Icelandic electronic ID client using the provided configuration
    pub fn create_icelandic_electronic_id(
        &self,
        config: ElectronicIdConfig,
    ) -> Arc<dyn ElectronicId> {
        Arc::new(IcelandicElectronicId::new(config))
    }
}

impl Default for AuthFactory {
    fn default() -> Self {
        Self::new()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use auth_contracts::config::{ApiKeyConfig, PasswordConfig};
    use uuid::Uuid;

    #[test]
    fn test_create_password_hasher() {
        let factory = AuthFactory::new();
        let config = PasswordConfig { salt_cost: 4 }; // Use low cost for tests
        let hasher = factory.create_password_hasher(config);

        // Just verify we can hash a password
        let password = "test_password";
        let hashed = hasher.hash(password).unwrap();
        assert!(hasher.verify(password, &hashed).is_ok());
    }

    #[test]
    fn test_create_api_key_manager() {
        let factory = AuthFactory::new();
        let config = ApiKeyConfig {
            organization_id: Some(Uuid::new_v4()),
        };
        let manager = factory.create_api_key_manager(config);

        // Just verify we can create and split an API key
        let api_key = manager.generate("test").unwrap();
        let api_key_str = api_key.api_key().to_string();
        let split = manager.split(&api_key_str).unwrap();

        assert_eq!(split.prefix(), "test");
    }
}
