[package]
name = "auth-core"
version = "0.1.0"
edition = "2021"

[dependencies]
auth-contracts = { path = "../auth-contracts" }
aes-gcm-siv.workspace = true
anyhow.workspace = true
async-trait.workspace = true
base64.workspace = true
bcrypt.workspace = true
chrono.workspace = true
isahc.workspace = true
jsonwebtoken.workspace = true
prefixed-api-key.workspace = true
rand.workspace = true
reqwest.workspace = true
serde.workspace = true
serde_json.workspace = true
sha2.workspace = true
tokio.workspace = true
uuid.workspace = true

[dev-dependencies]
tokio-shared-rt.workspace = true
