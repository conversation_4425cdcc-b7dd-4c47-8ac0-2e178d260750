use anyhow::Result;
use auth_contracts::config::{
    ApiKeyConfig, ElectronicIdConfig, EncryptionConfig, JwtConfig, PasswordConfig,
};
use auth_core::AuthFactory;
use uuid::Uuid;

fn main() -> Result<()> {
    // Initialize the auth factory
    let factory = AuthFactory::new();

    // Create configuration structs (normally these would come from your app's environment)
    let jwt_config = JwtConfig {
        private_key: "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----".to_string(),
        public_key: "-----B<PERSON>IN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----".to_string(),
        token_expiration_seconds: 300,
    };

    let password_config = PasswordConfig { salt_cost: 12 };

    let api_key_config = ApiKeyConfig {
        organization_id: Some(Uuid::new_v4()),
    };

    let encryption_config = EncryptionConfig {
        encryption_key: "your_base64_encoded_32_byte_key".to_string(),
    };

    let electronic_id_config = ElectronicIdConfig {
        base_url: "https://example.com".to_string(),
        client_id: "client_id".to_string(),
        client_secret: "client_secret".to_string(),
    };

    // Create the authentication components
    let jwt_encoder = factory.create_jwt_encoder(jwt_config);
    let password_hasher = factory.create_password_hasher(password_config);
    let api_key_manager = factory.create_api_key_manager(api_key_config);
    let data_encryptor = factory.create_data_encryptor(encryption_config)?;
    let electronic_id = factory.create_icelandic_electronic_id(electronic_id_config);

    println!("Successfully created authentication components!");

    // Example: Password hashing
    let password = "secure_password";
    let hashed = password_hasher.hash(password)?;
    println!("Password hashing: '{password}' → '{hashed}'");

    let is_valid = password_hasher.verify(password, &hashed)?;
    println!("Password verification: {is_valid}");

    // Example: API key generation
    let api_key = api_key_manager.generate("api")?;
    println!("Generated API key: {}", api_key.api_key());

    // Example: Data encryption
    let sensitive_data = "This is sensitive information";
    let encrypted = data_encryptor.encrypt(sensitive_data.as_bytes())?;
    println!("Encrypted data: {:?}", encrypted);

    let decrypted = data_encryptor.decrypt(&encrypted)?;
    let decrypted_str = String::from_utf8(decrypted)?;
    println!("Decrypted data: {decrypted_str}");

    // Note: Electronic ID authentication requires user interaction,
    // so it's not demonstrated in this simple example.

    Ok(())
}
