/// Configuration for JWT token encoding and decoding
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct JwtConfig {
    /// Private key for signing tokens (PEM format)
    pub private_key: String,
    /// Public key for verifying tokens (PEM format)
    pub public_key: String,
    /// Default expiration time for access tokens in seconds
    pub token_expiration_seconds: u32,
}

/// Configuration for password hashing
#[derive(<PERSON><PERSON><PERSON>, <PERSON><PERSON>)]
pub struct PasswordConfig {
    /// Cost factor for bcrypt algorithm (4-31, higher is more secure but slower)
    pub salt_cost: u32,
}

/// Configuration for Icelandic Electronic ID
#[derive(Debug, <PERSON>lone)]
pub struct ElectronicIdConfig {
    /// Base URL for the Icelandic Electronic ID service
    pub base_url: String,
    /// Client ID for the Icelandic Electronic ID service
    pub client_id: String,
    /// Client secret for the Icelandic Electronic ID service
    pub client_secret: String,
}

/// Configuration for data encryption
#[derive(Debug, <PERSON>lone)]
pub struct EncryptionConfig {
    /// Base64-encoded 32-byte key for AES-GCM-SIV encryption
    pub encryption_key: String,
}

/// Configuration for API key management
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>)]
pub struct ApiKeyConfig {
    // Currently no static configuration needed for API key management
    // Organization IDs are passed dynamically when generating keys
}
