# Check whether the GQL schema has changed. Run codegen if it has
if (git diff --cached --name-only | grep -q 'backend/api/schema.graphql') then
	npm run gen:gql

	# Front End
	if (git diff --name-only | grep -q 'apps/frontend/src/generated/graphql/index.ts') then
		git add apps/frontend/src/generated/graphql/index.ts
	fi
	# Front End
	if (git diff --name-only | grep -q 'apps/frontend/src/generated/graphql/possibleTypes.json') then
		git add apps/frontend/src/generated/graphql/possibleTypes.json
	fi

	# PDF Generator
	if (git diff --name-only | grep -q 'apps/pdf-generator/src/generated/graphql/index.ts') then
		git add apps/pdf-generator/src/generated/graphql/index.ts
	fi
	if (git diff --name-only | grep -q 'apps/pdf-generator/src/generated/graphql/possibleTypes.json') then
		git add apps/pdf-generator/src/generated/graphql/possibleTypes.json
	fi

	# Seed
	if (git diff --name-only | grep -q 'libs/graphql/src/generated/graphql/index.ts') then
		git add libs/graphql/src/generated/graphql/index.ts
	fi
	if (git diff --name-only | grep -q 'libs/graphql/src/generated/graphql/possibleTypes.json') then
		git add libs/graphql/src/generated/graphql/possibleTypes.json
	fi
fi
