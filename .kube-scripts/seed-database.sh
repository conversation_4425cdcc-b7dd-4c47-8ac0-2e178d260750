#!/bin/bash

if [ -z $1 ]; then
  echo "Please set db password";
  kubectl get secret
  exit 1
fi

secret=$1


SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

source "$SCRIPT_DIR/.sure.sh"

pod=$(kubectl get pods --selector=app=database,variant=staging -o json  | jq .items[0].metadata.name -r)


echo "pw: $secret"

kubectl cp packages/backend/seed/seed.dump $pod:/tmp

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$secret pg_restore -d leviosa -U postgres --data-only --no-privileges --no-owner /tmp/seed.dump"