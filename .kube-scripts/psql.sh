#!/bin/bash
if [ -z $1 ]; then
  echo "Please select variant";
  exit 1
fi

# This can be found by running describe on the postgres primary pod. 
# Example value hsu
VARIANT=$1


DB_PASSWORD=$(kubectl get secret --selector=app=database,variant=$VARIANT -o json  | jq '.items[0].data["postgres-password"]' -r  | base64 -d)

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)




pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)


echo "Exec psql on $pod"

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres " 

# If executing something for the leviosa db you can connect by running:
# \c leviosa
# in the psql console.