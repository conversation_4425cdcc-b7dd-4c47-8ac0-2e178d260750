#!/bin/bash
SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )

if [ -z $1 ]; then
  echo "Please select variant";
  exit 1
fi

if [ -z $2 ]; then
  echo "Please select file";
  exit 1
fi


VARIANT=$1


DB_PASSWORD=$(kubectl get secret --selector=app=database,variant=$VARIANT -o json  | jq '.items[0].data["postgres-password"]' -r  | base64 -d)

pod=$(kubectl get pods --selector=app=database,variant=$VARIANT -o json  | jq .items[0].metadata.name -r)

echo "Got pod $pod"

echo "I will now copy the file $2 to $pod" 
source "$SCRIPT_DIR/.sure.sh"
kubectl cp "$2" $pod:/tmp/restore.sql;

#echo "Exec psql on $pod"

echo "I will restore the backup to $pod database" 
source "$SCRIPT_DIR/.sure.sh"

kubectl exec -ti $pod -- /bin/bash -c "PGPASSWORD=$DB_PASSWORD psql -U postgres -d leviosa < /tmp/restore.sql" 
