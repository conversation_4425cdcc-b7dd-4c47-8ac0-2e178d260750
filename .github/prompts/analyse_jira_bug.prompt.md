---
mode: "agent"
tools: ["jira_get_issue", "jira_update_issue", "jira_add_comment", "githubRepo", "codebase"]
description: "Take a Jira issue, analyse it and create a report. Notice 🔎 has special meaning and is used by AI to recognise a standardised analysis."
---

# Goal

The output of this workflow is an analysis report of an issue described in Jira ticket. _Do not do any code changes unless prompted to do so._

# Workflow

## Preparation

1. Assert we have Jira issue ID passed as argument in the format of {DEV}-{integer}. Memorize it as `jira_ticket_id`.

Message user: "Remember, selection of NLM (e.g. Gemini Pro 2.5) may greatly improve analysis results".

## Fetch and update Jira issue

2. Fetch issue description using `#jira_get_issue`, and parameter `fields: summary, description, attachments, labels, assignee, status, customfield_10121` .

- Field `summary` is actually title of the issue, memorize it as `jira_title`.
- Field `customfield_10121` states "area" to work and may be helpful for analysing the bug.

3. Add `ai-anlysed` label to issue, using `#jira_update_issue`

See [Copilot instructions](../copilot-instructions.md) and therein mentioned `README_AI.md` files to better understand the codebase before commencing.

## Analyse issue

5. You may now analyse the issue. You're programming skills are excellent and you have all the capabilities and experience required to solve the issue.

If you find the culprit relates to a package or packages, consider introspecting it using `#githubRepo` e.g. in case it needs a new version or is incorrectly used.

If you think further information (e.g. documentation or guidelines) from external websites will be helpful, ask user to help you find relevant links for you to read.

## Create report

6. Now create the _analysis report_ which is structured as follows. Notice text inside curly-brackets is for you to replace as needed:

```markdown
# 🔎AI analysis report

## Root cause

{What is the root cause of bug, where does the error originate? How will it be fixed? Describe in brief text, use minimal technical details.
_In case the issue description is vague or doesn't have enough details to analyse the issue, you should not proceed further and return to user that you cannot analyse further instead of returning an unclear analysis report. You should then ask for more details from user, it is helpful to specify what details exactly._}

## How to fix

{Describe how to approach the problem described as root cause. Specify folders or files involved. Since you were instructed to abort if root-cause analysis was incomplete, you should describe this as if you have a strong and valid theory of issue culprit and the execution plan therefor is assertive (not hesitating) since we want it to be clearly defined so that is easy to read and understand.}
```

7. Use `#jira_add_comment` to store the analysis report.

# Finally

Return message: "✅Please see analysis report [link to Jira issue and comment]. If you feel it is incomplete, please provide more details in issue description and run the analysis again."
