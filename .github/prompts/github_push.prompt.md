---
mode: "agent"
tools: ["create_pull_request", "get_me", "add_assignees_to_issue", "update_pull_request", "request_copilot_review"]
description: "Create branch and pull-request."
---

# Goal

Create new branch and pull-request to contain commits of current work. Do not work on codebase unless prompted to do so.

# Workflow

## Preparation

If you receive no context from previous work then you should ask for Jira ticket ID so that you can fetch meta-data (e.g. title) needed to create branch and pull-request.

You are reminded to use [Copilot instructions](../copilot-instructions.md) containing instructions about working with Github.

# Create branch and pull-request

1. Use CLI to do the following commands in one sequence:

- create a local Github branch named as: {jira_ticket_id}-{snake_case(issue_title)}.
- commit staged files.
- publish the branch to `{leviosa.github.owner}/{leviosa.github.repo}` (main branch is `{leviosa.github.mainBranch}`)

2. `#create_pull_request` from main branch and set following fields:

- title: `{jira_ticket_id} {issue_title}`
- body: use the template from [Pull-request template](../pull_request_template.md) to fill in information about work done in the previous work. "Drive by" may be set to "n/a" if it does not apply.

3. `#update_pull_request` and set:

- draft: `false`
- assignees: add user as assignee (`{leviosa.github.username}`).

4. Set Copilot as reviewer using `#request_copilot_review`.

5. Transition Jira issue status to `in review` (the issue-id is found in PR description). _Do not add comments to Jira unless asked to do so_.

# Finally

Return message: "✅Published to Github -> [link to newly created PR]"
