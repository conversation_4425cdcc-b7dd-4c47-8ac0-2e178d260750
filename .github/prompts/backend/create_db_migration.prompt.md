---
mode: "agent"
description: "This script creates a DB migration matching the current branch name and outputs the file name."
---

# Goal

Create a new migration file and output the filename.

Here are steps to achieve this:

1. navigate to the migrations folder `backend/migration`
2. Execute this command from the cli: `git rev-parse --abbrev-ref HEAD | tr '[:upper:]' '[:lower:]' | sed -r 's/[-]+/_/g' | read mig_name ; cargo run -- generate $mig_name;`.
   It will create a new migration file in the following form: `m{date}_{timestamp}_{branch name in snake case}`. It will also modify `backend/migration/src/lib.rs`.
3. Open the migration file and overwrite the contents of the file with this:

```Rust
use sea_orm_migration::{prelude::*, sea_orm::ConnectionTrait};

#[derive(DeriveMigrationName)]
pub struct Migration;
#[async_trait::async_trait]
impl MigrationTrait for Migration {
    async fn up(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        let sql = r#"--sql
            --postgres

        "#;

        manager
            .get_connection()
            .execute_unprepared(sql)
            .await
            .map(|_| ())
    }

    async fn down(&self, manager: &SchemaManager) -> Result<(), DbErr> {
        Ok(())
    }
}
```

4. In `lib.rs` there is a large vector with migrations to run. Please add the new migration at the end of the vector.
5. Output the migration file name.
