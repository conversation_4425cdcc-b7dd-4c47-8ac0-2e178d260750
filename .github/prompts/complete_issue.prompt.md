---
mode: "agent"
tools: ["get_available_transitions", "jira_transition_issue", "jira_get_issue", "codebase", "githubRepo"]
description: "Take an issue and immediately starts coding. This is good for issues considered 'AI ready'."
---

# Goal

The output of this workflow is update of codebase from a given issue or context. _Do not make any code changes unless prompted to do so._

# Workflow

## Preparation

1. Assert we have Jira issue ID passed as argument in the format of {DEV}-{integer}. Memorize it as `jira_ticket_id` and ask for it if missing in argument.

## Fetch and update Jira issue

2. Fetch issue description using `#jira_get_issue`, and parameter `fields: summary, description, attachments, labels, assignee, status, customfield_10121, comment` .

- Notice field `summary` is actually title of the issue (not description), memorized from now on as `jira_title`.
- Field `customfield_10121` states "area" to work and may be helpful for analysing the bug.

Also fetch available transitions for issue using `#get_available_transitions` and remember these since `#jira_transition_issue` requires ids not labels.

Regarding comments, we are _only interested in AI created "analysis report"_ which is recognised by the 🔎 symbol. It may have additional comments contributing to the analysis. **Ignore all other comments.**

3. Update issue as follows, using `#jira_update_issue`:

- assignee: resolved from settings `{leviosa.atlassian.username}`
- label: `ai-completed`

4. Transition issue status to `in progress`.

See [Copilot instructions](../copilot-instructions.md) and therein mentioned `README_AI.md` files to better understand the codebase before commencing.

## Execute codebase work

6. Analyse issue and commence coding work. You're programming skills are excellent and you have all the skills and capabilities required to resolving the issue given you have enough information.

If you find the culprit relates to a package or packages, consider introspecting it using `#githubRepo` e.g. in case it needs a new version or is incorrectly used. If you think further information (e.g. documentation or guidelines) from external websites will be helpful, ask user to help you find relevant links for you to read.

# Finally

Return message: " ✅Coding work complete, please validate work done and discuss with me refinements needed."
