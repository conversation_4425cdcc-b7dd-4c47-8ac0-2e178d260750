name: CI
on:
  pull_request:
    branches:
      - master
      - development
      - 'release/**'
# Needed for nx-set-shas when run on the main branch
permissions:
  actions: read
  contents: read
  pull-requests: write

jobs:
  changes:
    runs-on: ubuntu-latest
    permissions:
      pull-requests: write
    outputs:
      migrations: ${{ steps.filter.outputs.migrations }}
      backend-rust: ${{ steps.filter.outputs.backend-rust }}
      backend-go: ${{ steps.filter.outputs.backend-go }}
      frontend: ${{ steps.filter.outputs.frontend }}
    steps:
      # For more information, see: https://github.com/dorny/paths-filter#examples
      - uses: dorny/paths-filter@v2
        id: filter
        with:
          filters: |
            backend-rust:
              - 'backend/**'
            backend-go:
              - 'micro-services/**'
            frontend:
              - 'apps/**'
              - 'libs/**'
              - 'package-lock.json'
              - 'nx.json'
            migrations:
              - 'backend/migration/**'
      - uses: actions/github-script@v6
        id: label-front-end
        if: steps.filter.outputs.frontend == 'true'
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ["Front end"]
            })
      - uses: actions/github-script@v6
        id: label-back-end
        if: steps.filter.outputs.backend-rust == 'true' || steps.filter.outputs.migrations == 'true' || steps.filter.outputs.backend-go == 'true'
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ["Back end"]
            })
      - uses: actions/github-script@v6
        id: label-migrations
        if: steps.filter.outputs.migrations == 'true'
        with:
          script: |
            github.rest.issues.addLabels({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              labels: ["Migrations"]
            })

  frontend-matrix:
    name: FE - All the Jobs
    needs: [changes]
    if: ${{ needs.changes.outputs.frontend == 'true' }}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        job: [format, lint, typecheck, test]
      fail-fast: true #if any job in the matrix fails, the entire workflow fails fast
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: "apps/frontend/package.json"
          cache: 'npm'

      - name: Cache node modules
        id: cache-npm
        uses: actions/cache@v3
        env:
          cache-name: cache-node-modules
        with:
          path: ~/.npm
          key: ${{ runner.os }}-build-${{ env.cache-name }}-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-build-${{ env.cache-name }}-
            ${{ runner.os }}-build-
            ${{ runner.os }}-
      
      - if: ${{ steps.cache-npm.outputs.cache-hit != 'true' }}
        name: List the state of node modules
        continue-on-error: true
        run: npm list

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit --no-fund --ignore-scripts --legacy-peer-deps

      - name: Derive appropriate eSHAs for base and head for `nx affected` commands
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: "development"
      
      - name: Set output vars
        run: |
          echo "BASE: ${{ env.NX_BASE }}"
          echo "HEAD: ${{ env.NX_HEAD }}"

      - run: git branch --track development origin/development

      - name: Run job
        run: |
          case ${{ matrix.job }} in
            format)
              npm run prettier:check
              ;;
            lint)
              npx nx affected -t lint --base=${{env.NX_BASE}} --head=${{env.NX_HEAD}} --parallel=4
              ;;
            typecheck)
              npx nx affected -t typecheck --base=${{env.NX_BASE}} --head=${{env.NX_HEAD}} --parallel=4
              ;;
            test)
              npx nx affected -t test --base=${{env.NX_BASE}} --head=${{env.NX_HEAD}} --parallel=1
              ;;
          esac

      - name: Check if coverage report exists
        id: check-coverage
        if: ${{ matrix.job == 'test' }}
        run: |
          if [ -f "apps/frontend/coverage/coverage-summary.json" ]; then
            echo "exists=true" >> $GITHUB_OUTPUT
          else
            echo "exists=false" >> $GITHUB_OUTPUT
          fi

      - name: Add coverage comment to PR
        if: ${{ matrix.job == 'test' && steps.check-coverage.outputs.exists == 'true' }}
        uses: davelosert/vitest-coverage-report-action@v2
        with:
          working-directory: apps/frontend
          github-token: ${{ secrets.GITHUB_TOKEN }}

  build-frontend-docker:
    name: FE - Build Docker images
    needs: [frontend-matrix, changes]
    if: github.base_ref == 'development' && github.event.pull_request.draft == false && needs.changes.outputs.frontend == 'true'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version-file: "apps/frontend/package.json"
          cache: 'npm'

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: NX Cache
        uses: actions/cache@v3
        with:
          path: node_modules/.cache/nx
          key: ${{ runner.os }}-nx-${{ hashFiles('package-lock.json') }}

      - name: Derive appropriate SHAs for Nx affected commands
        uses: nrwl/nx-set-shas@v4
        with:
          main-branch-name: "development"

      - name: Install dependencies
        run: npm ci --prefer-offline --no-audit --no-fund --ignore-scripts --legacy-peer-deps
      
      - name: Build Docker images for affected apps
        run: npx nx affected -t build:docker --base=${{env.NX_BASE}} --head=${{env.NX_HEAD}} --parallel=4


  test-go-backend:
    needs: [changes]
    if: ${{ needs.changes.outputs.backend-go == 'true' || needs.changes.outputs.migrations == 'true' }}
    name: Test GO Backend
    runs-on: ubuntu-latest
    services:
      database:
        image: postgres:15.1
        env:
          POSTGRES_PASSWORD: leviosa
          POSTGRES_DB: leviosa
        ports:
          - 5432:5432
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            micro-services
            backend
            .github
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.87.0
          override: true
      - name: Set up cargo cache
        uses: actions/cache/restore@v3
        continue-on-error: false
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: ${{ runner.os }}-cargo-
      - name: Run migrations
        env:
          DATABASE_URL: postgres://postgres:leviosa@localhost:5432/leviosa
          LEVIOSA_USER_PASSWORD: leviosa
          NHI_SERVICE_DB_PASSWORD: nhi-service
        run: cargo run --locked -p migration -- up
      - name: Setup Go
        uses: actions/setup-go@v4
        with:
          go-version: '1.21.x'
      - name: Install dependencies
        run: go get .
        working-directory: micro-services/nhi-service
      - name: Build
        run: go build -v
        working-directory: micro-services/nhi-service
      - name: Test with the Go CLI
        run: go test -v ./...
        working-directory: micro-services/nhi-service
        env:
          PG_HOST: localhost
          PG_PORT: 5432
          PG_USER: nhi_service_user
          PG_PASSWORD: nhi-service
          PG_DB: leviosa

  test-rust-backend:
    needs: [changes]
    if: ${{ needs.changes.outputs.backend-rust == 'true' || needs.changes.outputs.migrations == 'true' }}
    name: Test Backend
    runs-on:
      group: large-runners
    env:
      CARGO_INCREMENTAL: 0
    services:
      database:
        image: postgres:15.1
        env:
          POSTGRES_PASSWORD: leviosa
          POSTGRES_DB: leviosa
        ports:
          - 5432:5432
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            backend
            packages
            .github
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.87.0
          override: true
      - name: Set up cargo cache
        uses: actions/cache/restore@v3
        continue-on-error: false
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: ${{ runner.os }}-cargo-
      - name: Run migrations
        env:
          DATABASE_URL: postgres://postgres:leviosa@localhost:5432/leviosa
          LEVIOSA_USER_PASSWORD: leviosa
          NHI_SERVICE_DB_PASSWORD: nhi-service
        run: cargo run --locked -p migration -- up
      - name: Compile
        run: cargo test --locked --no-run
      - name: Run Tests
        env:
          PORT: 4001
          PG_HOST: localhost
          PG_PORT: 5432
          PG_USER: leviosa
          PG_PASSWORD: leviosa
          PG_DB: leviosa
          PG_MIGRATION_USER: postgres
          PG_MIGRATION_PASSWORD: leviosa
          LEVIOSA_USER_PASSWORD: leviosa
          NHI_SERVICE_DB_PASSWORD: nhi-service
          TOKEN_SECRET: secret
          ADMIN_KEY: key
          EMIT_SCHEMA_FILE: true
          DATABASE_URL: postgres://postgres:leviosa@localhost:5432/leviosa
          LOGS: true
          SALT_COST: 4
          S3_ENDPOINT: http://localhost:9000
          FILE_STORAGE_BUCKET: leviosa
          S3_REGION: eu-west-1
          REFRESH_TOKEN_EXPIRATION_TIME: 1800
          JWT_EXPIRATION_TIME: 300
          JWT_PRIVATE_KEY_PATH: dev.private.key
          JWT_PUBLIC_KEY_PATH: dev.public.pem
          SENTRY_ENVIRONMENT: ci_tests
          SENTRY_SAMPLE_RATE: 0.0
          SENTRY_TRACES_SAMPLE_RATE: 0.0
          SENTRY_PROFILES_SAMPLE_RATE: 0.0
          JIRA_HOST: https://leviosa.atlassian.net
          EMAIL_SENDER: mock
          PRESCRIPTION_API: mock
          ORACLE_API: mock
          ORACLE_API_ENDPOINT: http://localhost:3200
          LOG_LEVEL: info
          LOGS_STRUCTURED: false
        run: cargo test --locked

  lint-rust-backend:
    needs: [changes]
    if: ${{ needs.changes.outputs.backend-rust == 'true' || needs.changes.outputs.migrations == 'true' }}
    name: Lint Backend
    runs-on: ubuntu-latest
    env:
      CARGO_INCREMENTAL: 0
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          sparse-checkout: |
            backend
            packages
            .github
      - name: Install Rust
        uses: actions-rs/toolchain@v1
        with:
          profile: minimal
          toolchain: 1.87.0
          override: true
          components: clippy
      - name: Set up cargo cache
        uses: actions/cache/restore@v3
        continue-on-error: false
        with:
          path: |
            ~/.cargo/bin/
            ~/.cargo/registry/index/
            ~/.cargo/registry/cache/
            ~/.cargo/git/db/
            target/
          key: ${{ runner.os }}-cargo-${{ hashFiles('**/Cargo.lock') }}
          restore-keys: ${{ runner.os }}-cargo-
      - name: Run Lint
        run: |
          cargo clippy --locked -- -D warnings
          cargo clippy --version