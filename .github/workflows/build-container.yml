name: Build and test container
on:
  workflow_call:
    inputs:
      dockerfile_path:
        type: string
        required: true
      build_context:
        type: string
        default: "."
      tag_prefix:
        type: string
        required: true
      env_file_path:
        type: string
        required: true
      run_options:
        type: string
        default: ""
      skip_testing:
        type: boolean
        default: false
      needs_postgres:
        type: boolean
        default: false
      needs_backend_rust:
        type: boolean
        default: false
    secrets:
      gcp_registry_token:
        required: true
      sentry_auth_token:
        required: false
      sentry_clinic_portal_dsn:
        required: false
jobs:
  build-container:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
      - name: Login DO Registry
        uses: docker/login-action@v2
        with:
          registry: eu.gcr.io
          username: _json_key
          password: ${{ secrets.gcp_registry_token }}
      - name: Build image
        uses: docker/build-push-action@v6
        with:
          context: ${{ inputs.build_context }}
          file: ${{ inputs.dockerfile_path }}
          secrets: |
            SENTRY_AUTH_TOKEN=${{ secrets.sentry_auth_token }}
            SENTRY_CLINIC_PORTAL_DSN=${{ secrets.sentry_clinic_portal_dsn }}
          load: true
          tags: ${{ inputs.tag_prefix }}
          build-args: |
            GIT_REF_NAME=${{ github.ref_name }}
            SENTRY_CLINIC_PORTAL_DSN=${{ secrets.sentry_clinic_portal_dsn }}
          cache-from: type=gha,scope=${{ inputs.tag_prefix }}
          cache-to: type=gha,mode=max,scope=${{ inputs.tag_prefix }}
      - name: Start Postgres
        if: inputs.needs_postgres
        run: cp backend/migration/.env.example backend/migration/.env && bash backend/migration/scripts/db_up.sh
      - name: Run migrations
        if: inputs.needs_postgres
        run: docker run --rm --network=host --env-file ./backend/migration/.env.example eu.gcr.io/ultra-dimension-297311/leviosa_migrator:latest
      - name: Start Rust backend
        if: inputs.needs_backend_rust
        run: docker run -d --rm --network=host --env-file ./backend/api/.env.example --mount type=bind,source="$(pwd)"/backend/api/dev.private.key,target=/leviosa/dev.private.key --mount type=bind,source="$(pwd)"/backend/api/dev.public.pem,target=/leviosa/dev.public.pem eu.gcr.io/ultra-dimension-297311/leviosa_backend_rust:latest
      - name: Start container
        if: ${{ !inputs.skip_testing }}
        run: docker run --name ${{ inputs.tag_prefix }} -d --network=host --env-file ${{ inputs.env_file_path }} ${{ inputs.run_options }} ${{ inputs.tag_prefix }}
      - name: Wait for container to become healthy
        if: ${{ !inputs.skip_testing }}
        uses: ./.github/actions/wait-for-container
        with:
          container: ${{ inputs.tag_prefix }}
      - name: Push image
        run: |
          docker tag ${{ inputs.tag_prefix }} eu.gcr.io/ultra-dimension-297311/${{inputs.tag_prefix}}:latest
          docker tag ${{ inputs.tag_prefix }} eu.gcr.io/ultra-dimension-297311/${{inputs.tag_prefix}}:${{ github.sha }}
          docker push eu.gcr.io/ultra-dimension-297311/${{inputs.tag_prefix}}:latest
          docker push eu.gcr.io/ultra-dimension-297311/${{inputs.tag_prefix}}:${{ github.sha }}
