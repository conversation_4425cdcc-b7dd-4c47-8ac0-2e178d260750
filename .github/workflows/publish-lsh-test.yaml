on: workflow_dispatch
name: Publish landspitali testing
jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Install openconnect
        run: sudo apt install openconnect jq
      - name: Connect to VPN
        run: echo ${{ secrets.VPN_LSH_PASSWORD }} | sudo openconnect -b --user ${{ secrets.VPN_LSH_USERNAME }} --passwd-on-stdin ${{ secrets.VPN_LSH_HOSTNAME }}
      - name: Create Kube config
        run: mkdir -p ~/.kube && echo "${{ secrets.KUBE_LSH_CONFIG }}" > ~/.kube/config
      - name: Update config
        run: echo -e '${{ secrets.KUBE_LSH_TEST_ENV_SECRETS }}' > kustomize/overlays/lsh-test/.env.secret
      - name: Get current date
        id: date
        run: echo "::set-output name=date::$(date +'%Y-%m-%dT%H_%M_%S')"
      - name: Create config
        run: echo -e '\nIMAGE_TAG=${{ github.sha }}\nDATE=${{ steps.date.outputs.date }}' >> kustomize/overlays/lsh-test/.application-config.config
      - name: Delete old migration job so the deployment will not throw an error.
        run: kubectl delete jobs.batch lsh-test-leviosa-migrations
      - name: Deploy kubectl kustomize
        run: kubectl apply -k kustomize/overlays/lsh-test/
