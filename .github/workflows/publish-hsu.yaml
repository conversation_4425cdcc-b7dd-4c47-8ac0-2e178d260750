on: workflow_dispatch
name: Publish HSU prod
jobs:
  publish:
    name: Publish
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2
      - name: Connect to VPN
        uses: ./.github/actions/connect-vpn
        with:
          username: ${{ secrets.VPN_TRS_USERNAME }}
          password: ${{ secrets.VPN_TRS_PASSWORD }}
          group: ${{ secrets.VPN_TRS_GROUP }}
          hostname: ${{ secrets.VPN_TRS_HOSTNAME }}
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Create Kube config
        run: mkdir -p ~/.kube && echo "${{ secrets.KUBE_TRS_CONFIG }}" > ~/.kube/config
      - name: Fetch the k8s envrionment secrets
        run: op items get nucjioxy6tifgtej3cojucz7ly --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/hsu/.env.secret
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Fetch the k8s public key for env
        run: op items get 6pw33h3finkodgl33gtkjf7vli --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/hsu/public.pem
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Fetch the k8s private key for env
        run: op items get e5cxtviqs2pjyxcvoieu662xey --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/hsu/private.key
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Get current date
        id: date
        run: echo "::set-output name=date::$(date +'%Y-%m-%dT%H_%M_%S')"
      - name: Create config
        run: echo -e '\nIMAGE_TAG=${{ github.sha }}\nDATE=${{ steps.date.outputs.date }}' >> kustomize/overlays/hsu/.application-config.config
      - name: Delete old migration job so the deployment will not throw an error.
        run: kubectl delete jobs.batch --selector variant=hsu || true
      - name: Deploy kubectl kustomize
        run: kustomize build kustomize/overlays/hsu/ | kubectl apply -f -
