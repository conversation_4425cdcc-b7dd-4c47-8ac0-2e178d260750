name: AWS Build and Push
on:
  schedule:
    - cron: "0 8 * * *"
  push:
    branches: 
      - 'release/**'
  workflow_dispatch:
jobs:
  api-build:
    name: Backend Rust
    uses: ./.github/workflows/build-container-multi-step.yml
    with:
      dockerfile_path: ./docker/backend-rust/backend-rust.dockerfile
      tag_prefix: gql-api

  api-test-and-push:
    name: Backend Rust
    needs: [ api-build, migrator ]
    uses: ./.github/workflows/test-and-push-container-multi-step.yml
    with:
      tag_prefix: gql-api
      env_file_path: ./backend/api/.env.example
      run_options: --mount type=bind,source="$(pwd)"/backend/api/dev.private.key,target=/leviosa/dev.private.key --mount type=bind,source="$(pwd)"/backend/api/dev.public.pem,target=/leviosa/dev.public.pem
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
  
  public-api-build:
    name: Public API
    uses: ./.github/workflows/build-container-multi-step.yml
    with:
      dockerfile_path: ./docker/public-api/public-api.dockerfile
      tag_prefix: public-gql-api

  public-api-test-and-push:
    name: Public API
    needs: [ public-api-build, migrator ]
    uses: ./.github/workflows/test-and-push-container-multi-step.yml
    with:
      tag_prefix: public-gql-api
      env_file_path: ./backend/public-api/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  backend-go-build:
    name: Backend Go
    uses: ./.github/workflows/build-container-multi-step.yml
    with: 
      dockerfile_path: ./docker/backend-go/backend-go.dockerfile
      tag_prefix: nhi-service

  backend-go-test-and-push:
    name: Backend Go
    needs: [ backend-go-build, migrator ]
    uses: ./.github/workflows/test-and-push-container-multi-step.yml
    with:
      tag_prefix: nhi-service
      env_file_path: ./micro-services/nhi-service/.env.example
      run_options: --mount type=bind,source="$(pwd)"/micro-services/nhi-service/dev.public.pem,target=/leviosa/dev.public.pem
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  migrator:
    name: Migrator
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/migrator/migrator.dockerfile
      tag_prefix: db-migrations
      env_file_path: ./backend/migration/.env.example
      skip_testing: true
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  clinic-portal:
    name: ClinicPortal
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/frontend/frontend.dockerfile
      tag_prefix: clinic-portal
      env_file_path: ./apps/frontend/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_CLINIC_PORTAL_AUTH_TOKEN }}
      SENTRY_CLINIC_PORTAL_DSN: ${{ secrets.SENTRY_CLINIC_PORTAL_DSN }}

  check-in-kiosk:
    name: Check in kiosk
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/check-in-kiosk/check-in-kiosk.dockerfile
      tag_prefix: check-in-kiosk
      env_file_path: ./apps/check-in-kiosk/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_KIOSK_AUTH_TOKEN }}

  online-booking:
    name: Online booking
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/online-booking/online-booking.dockerfile
      tag_prefix: online-booking
      env_file_path: ./apps/online-booking/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      SENTRY_AUTH_TOKEN: ${{ secrets.SENTRY_ONLINE_BOOKING_AUTH_TOKEN }}

  external-ehr-client:
    name: External EHR Client
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/external-ehr-client/external-ehr-client.dockerfile
      tag_prefix: external-ehr-client
      env_file_path: ./backend/external-ehr-client/.env.example
      skip_testing: true
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  cron-jobs:
    name: Cron jobs
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/cron-jobs/cron-jobs.dockerfile
      tag_prefix: cron-jobs
      env_file_path: ./backend/cron-jobs/.env.example
      skip_testing: true
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  pdf-generator:
    name: Pdf Generator
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/pdf-generator/pdf-generator.dockerfile
      tag_prefix: pdf-generator
      env_file_path: ./apps/pdf-generator/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  backup-16:
    name: Database Backup 16
    if: false
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/backups/backup16.dockerfile
      build_context: ./docker/backups
      tag_prefix: db-backup-16
      skip_testing: true
      env_file_path: ./docker/backups/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

  backup-15:
    name: Database Backup 15
    if: false
    uses: ./.github/workflows/build-and-push-container-simple.yml
    with:
      dockerfile_path: ./docker/backups/backup.dockerfile
      build_context: ./docker/backups
      tag_prefix: db-backup-15
      skip_testing: true
      env_file_path: ./docker/backups/.env.example
    secrets:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
