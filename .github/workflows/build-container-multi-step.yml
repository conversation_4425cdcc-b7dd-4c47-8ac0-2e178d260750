name: Build container
on:
  workflow_call:
    inputs:
      dockerfile_path:
        type: string
        required: true
      build_context:
        type: string
        default: "."
      tag_prefix:
        type: string
        required: true
    secrets:
      SENTRY_AUTH_TOKEN:
        required: false
      SENTRY_CLINIC_PORTAL_DSN:
        required: false
jobs:
  build-container:
    name: Build container
    runs-on:
      group: large-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Build image
        uses: docker/build-push-action@v6
        with:
          context: ${{ inputs.build_context }}
          file: ${{ inputs.dockerfile_path }}
          load: true
          tags: ${{ inputs.tag_prefix }}
          cache-from: type=gha,scope=${{ inputs.tag_prefix }}
          cache-to: type=gha,mode=max,scope=${{ inputs.tag_prefix }}
          platforms: "linux/amd64"
      
      - name: Save Docker image
        run: docker save ${{ inputs.tag_prefix }} > image.tar
      
      - name: Upload Docker image
        uses: actions/upload-artifact@v4
        with:
          name: docker-image-${{ inputs.tag_prefix }}
          path: image.tar
          retention-days: 1
