name: Publish Test
on:
  # release:
  #   types: [published]
  workflow_dispatch:
jobs:
  rollout:
    name: Rollout
    runs-on: ubuntu-latest
    steps:
      # Disable
      - name: Checkout
        run: |
          echo "This workflow is disabled. We want to use AWS Deploy for testing deployments"
          exit 1
      - name: Checkout
        uses: actions/checkout@v2
      - name: Connect to VPN
        uses: ./.github/actions/connect-vpn
        with:
          username: ${{ secrets.VPN_LEVIOSA_HOSTING_USERNAME }}
          password: ${{ secrets.VPN_LEVIOSA_HOSTING_PASSWORD }}
          group: ${{ secrets.VPN_LEVIOSA_HOSTING_GROUP }}
          hostname: ${{ secrets.VPN_LEVIOSA_HOSTING_HOSTNAME }}
      - name: Install 1Password CLI
        uses: 1password/install-cli-action@v1
      - name: Create Kube config
        run: mkdir -p ~/.kube && echo "${{ secrets.KUBE_LEVIOSA_TESTING }}" > ~/.kube/config
      - name: Fetch the k8s envrionment secrets
        run: op items get lr37nwtnoupqnjf2q54qclkoru --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/test/.env.secret
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Fetch the k8s public key for env
        run: op items get yjmgorofvtvc675fehpyfgjd6a --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/test/public.pem
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Fetch the k8s private key for env
        run: op items get 2onfjzw6hca7hcgrkct4zrpfsy --vault='Environment variables' --fields notesPlain  --format json  | jq .value -r > kustomize/overlays/test/private.key
        env:
          OP_SERVICE_ACCOUNT_TOKEN: ${{ secrets.ONE_PASSWORD_SERVICE_ACCOUNT_TOKEN }}
      - name: Get current date
        id: date
        run: echo "::set-output name=date::$(date +'%Y-%m-%dT%H_%M_%S')"
      - name: Create config
        run: echo -e '\nIMAGE_TAG=${{ github.sha }}\nDATE=${{ steps.date.outputs.date }}\nGIT_REF_NAME=${{ github.ref_name }}' >> kustomize/overlays/test/.application-config.config
      - name: Delete old migration job so the deployment will not throw an error.
        run: kubectl delete jobs.batch test-leviosa-migrations || true
      - name: Deploy kubectl kustomize
        run: kustomize build kustomize/overlays/test/ | kubectl apply -f -
