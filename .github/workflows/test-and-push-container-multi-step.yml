name: Test and push container
on:
  workflow_call:
    inputs:
      tag_prefix:
        type: string
        required: true
      env_file_path:
        type: string
        required: true
      run_options:
        type: string
        default: ""
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
jobs:
  test-and-push-container:
    name: Test and push container
    runs-on:
      group: large-runners
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      
      - name: Download Docker image
        uses: actions/download-artifact@v4
        with:
          name: docker-image-${{ inputs.tag_prefix }}
          path: .
      
      - name: Load Docker image
        run: docker load < image.tar

      - name: Start Postgres
        run: cp backend/migration/.env.example backend/migration/.env && bash backend/migration/scripts/db_up.sh

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-west-1
      
      - name: Login to AWS ECR
        uses: aws-actions/amazon-ecr-login@v2
        id: login-ecr

      - name: Run migrations
        run: docker run --rm --network=host --env-file ./backend/migration/.env.example ${{ steps.login-ecr.outputs.registry }}/db-migrations:${{ github.sha }}

      - name: Start container
        run: docker run --name ${{ inputs.tag_prefix }} -d --network=host --env-file ${{ inputs.env_file_path }} ${{ inputs.run_options }} ${{ inputs.tag_prefix }}

      - name: Wait for container to become healthy
        uses: ./.github/actions/wait-for-container
        with:
          container: ${{ inputs.tag_prefix }}

      - name: Push image to ECR
        run: |
          docker tag ${{ inputs.tag_prefix }} ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:latest
          docker tag ${{ inputs.tag_prefix }} ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:${{ github.sha }}
          docker push ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:latest
          docker push ${{ steps.login-ecr.outputs.registry }}/${{ inputs.tag_prefix }}:${{ github.sha }}
