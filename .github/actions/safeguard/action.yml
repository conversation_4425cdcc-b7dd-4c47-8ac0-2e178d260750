name: Safeguard
description: Aborts workflow run if a Publish or Release workflow is already running
inputs:
  token:
    description: GitHub token
    required: true
  repository:
    description: Repository name
    required: true
  current_run_id:
    description: ID of current workflow run
    required: true
runs:
  using: composite
  steps:
    - run: >
        INPUT_TOKEN=${{ inputs.token }}
        INPUT_REPOSITORY=${{ inputs.repository }}
        INPUT_CURRENT_RUN_ID=${{ inputs.current_run_id }}
        INPUT_GITHUB_REF_NAME=$GITHUB_REF_NAME
        yarn node ./.github/actions/safeguard/
      shell: bash
