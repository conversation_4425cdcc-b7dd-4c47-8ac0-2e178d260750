name: "Wait for Docker container"
description: "Wait for a Docker container to become healthy"
inputs:
  container:
    description: "Name of the container to wait on"
    required: true
  timeout:
    description: "Timeout, in seconds, after which the operation is considered failed"
    required: false
    default: 60
runs:
  using: "composite"
  steps:
    - run: ./.github/actions/wait-for-container/wait-for-container.sh ${{ inputs.container }} ${{ inputs.timeout }}
      shell: bash
