name: 'Connect to VPN'
description: 'Connect to VPN and make the connection usable for rest'
inputs:
  username:
    required: true
    description: 'username to use for login'
  password:
    required: true
    description: 'password to use for login'
  group:
    required: true
    description: 'group to use for login'
  hostname:
    required: true
    description: 'hostname to use for login'
runs:
  using: 'composite'
  steps:
    - name: Install openconnect
      shell: bash
      run: sudo apt install openconnect jq
    - name: Connect to VPN
      shell: bash
      run: echo -e '#!/bin/bash\nexport INTERNAL_IP4_DNS=\n. /usr/share/vpnc-scripts/vpnc-script' > /tmp/dnsfix && chmod +x /tmp/dnsfix && echo ${{ inputs.password }} | sudo openconnect -b -s /tmp/dnsfix --user ${{ inputs.username }} --authgroup ${{ inputs.group }}  --passwd-on-stdin ${{ inputs.hostname }}