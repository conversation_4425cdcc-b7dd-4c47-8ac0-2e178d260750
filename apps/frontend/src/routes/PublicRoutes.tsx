import { lazy } from "react"
import { Navigate, Route } from "react-router-dom"

import { PublicCard } from "features/authentication"
import Login from "features/authentication/components/Login/Login"
import { SentryRoutes } from "lib/sentry/sentry"

import { RouteStrings } from "./RouteStrings"

const ResetPassword = lazy(
  () => import("features/authentication/routes/ResetPassword/ResetPassword")
)
const ForgotPassword = lazy(
  () => import("features/authentication/routes/ForgotPassword/ForgotPassword")
)
const Invite = lazy(
  () => import("features/authentication/routes/Invite/Invite")
)

export default function PublicRoutes() {
  return (
    <PublicCard>
      <SentryRoutes>
        <Route path={RouteStrings.login} element={<Login />} />
        <Route
          path={RouteStrings.forgetPassword}
          element={<ForgotPassword />}
        />
        <Route path={RouteStrings.resetPassword} element={<ResetPassword />} />
        <Route path={RouteStrings.join} element={<Invite />} />
        <Route path="*" element={<Navigate to={RouteStrings.login} />} />
      </SentryRoutes>
    </PublicCard>
  )
}
