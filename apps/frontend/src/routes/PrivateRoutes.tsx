import { lazy, Suspense } from "react"
import {
  Navigate,
  NavigateOptions,
  Outlet,
  Route,
  useNavigate,
} from "react-router-dom"

import Restricted from "features/authentication/components/Restricted/Restricted"
import { OrganisationPriceListProvider } from "features/billing/components/OrganisationPriceList/OrganisationPriceList.context"
import CreateEventInstance from "features/calendar/components/CreateEventInstance/CreateEventInstance"
import AvailabilitySchedule from "features/calendar/features/AvailabilitySchedule/AvailabilitySchedules"
import JournalTemplates from "features/journal-templates"
import LocationsOverview from "features/locations/components/LocationsOverview/LocationsOverview"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import FrontPage from "features/mainLayout/components/FrontPage/FrontPage"
import { OrganisationManageCreateDepartment } from "features/organisation-management/components/OrganisationManage/OrganisationManageCreateDepartment/OrganisationManageCreateDepartment"
import { OrganisationManageCreateTeam } from "features/organisation-management/components/OrganisationManage/OrganisationManageCreateTeam/OrganisationManageCreateTeam"
import CreateServiceType from "features/organisation-management/components/ServiceTypePage/CreateServiceType/CreateServiceType"
import EditServiceType from "features/organisation-management/components/ServiceTypePage/EditServiceType/EditServiceType"
import WaitingListTable from "features/waiting-list/components/WaitingListTable/WaitingListTable"
import {
  JournalEntryDetails,
  ListItemDetails,
  NoAccess,
  NoWorklistItemSelected,
} from "features/worklist/components/WorklistItemDetails/WorklistItemDetails"
import { SentryRoutes } from "lib/sentry/sentry"

import { PermissionKey } from "generated/graphql"

import { RouteStrings } from "./RouteStrings"

const Calendar = lazy(
  () => import("features/calendar/components/Calendar/Calendar")
)

const ServiceTypesPage = lazy(
  () =>
    import(
      "features/organisation-management/components/ServiceTypePage/ServiceTypePage"
    )
)

const UserManagement = lazy(
  () => import("features/user-management/UserManagement")
)
const RegisterSubject = lazy(
  () =>
    import(
      "features/subject-journal/components/RegisterSubject/RegisterSubject"
    )
)

const Dashboard = lazy(() => import("features/dashboard"))
const SubjectJournal = lazy(() => import("features/subject-journal/"))

const OrganisationManage = lazy(
  () =>
    import(
      "features/organisation-management/components/OrganisationManage/OrganisationManage"
    )
)
const DepartmentEdit = lazy(
  () =>
    import(
      "features/organisation-management/components/DepartmentEdit/DepartmentEdit"
    )
)
const TeamEdit = lazy(
  () => import("features/organisation-management/components/TeamEdit/TeamEdit")
)
const TeamView = lazy(
  () => import("features/organisation-management/components/TeamView/TeamView")
)
const ProviderView = lazy(
  () => import("features/organisation-management/components/ProviderView")
)
const ActorSettings = lazy(
  () => import("features/organisation-management/components/ActorSettings")
)
const SubjectEdit = lazy(
  () => import("features/subject-journal/components/SubjectEdit/SubjectEdit")
)
const WorklistPage = lazy(
  () => import("features/worklist/components/WorklistPage/WorklistPage")
)

const InviteUserForm = lazy(
  () =>
    import(
      "features/organisation-management/components/ProviderInvite/ProviderInvite"
    )
)

const PatientInvoice = lazy(
  () => import("features/billing/components/PatientInvoice/PatientInvoice")
)

const InvoiceOverview = lazy(
  () => import("features/billing/components/InvoiceOverview/InvoiceOverview")
)

const OrganisationPriceList = lazy(
  () =>
    import(
      "features/billing/components/OrganisationPriceList/OrganisationPriceList"
    )
)

const OrganisationPriceListEdit = lazy(
  () =>
    import(
      "features/billing/components/OrganisationPriceList/EditOrganisationPriceList/EditOrganisationPriceList"
    )
)

const NationalHealthInsurancePriceList = lazy(
  () =>
    import(
      "features/billing/components/NationalHealthInsurancePriceList/NationalHealthInsurancePriceList"
    )
)

const WaitingListPage = lazy(
  () =>
    import("features/waiting-list/components/WaitingListPage/WaitingListPage")
)

const NotFoundPage = lazy(
  () => import("features/mainLayout/components/ErrorPages/NotFoundPage")
)

type PrivateRoutesProps = {
  actor: GlobalDataWithNonNullableActor["actor"]
  config: GlobalDataWithNonNullableActor["config"]
}

export const Redirect = ({
  to,
  options,
}: {
  to: string
  options?: NavigateOptions
}) => {
  const navigate = useNavigate()
  navigate(to, options)

  return null
}

export const PrivateRoutes = ({ actor, config }: PrivateRoutesProps) => {
  return (
    <SentryRoutes>
      <Route path={RouteStrings.home} element={<FrontPage />} />
      <Route path={RouteStrings.subjectJournal} element={<SubjectJournal />} />
      <Route
        path={RouteStrings.journalTemplates}
        element={
          <JournalTemplates>
            <Suspense>
              <Outlet />
            </Suspense>
          </JournalTemplates>
        }
      ></Route>
      <Route
        path={RouteStrings.invoiceOverview}
        element={<InvoiceOverview />}
      />
      <Route path={RouteStrings.patientInvoice} element={<PatientInvoice />} />
      <Route
        path={RouteStrings.organisationPriceList}
        element={
          <OrganisationPriceListProvider>
            <OrganisationPriceList />
          </OrganisationPriceListProvider>
        }
      />
      <Route
        path={RouteStrings.organisationPriceListEdit}
        element={
          <OrganisationPriceListProvider>
            <OrganisationPriceListEdit />
          </OrganisationPriceListProvider>
        }
      />
      <Route
        path={RouteStrings.nationalHealthInsurancePriceList}
        element={<NationalHealthInsurancePriceList />}
      />
      <Route path={RouteStrings.subjectEdit} element={<SubjectEdit />} />
      <Route path={RouteStrings.providerView} element={<ProviderView />} />
      <Route path={RouteStrings.actorSettings} element={<ActorSettings />} />
      <Route path={RouteStrings.worklist}>
        {["", "org"].map((parentPath) => (
          <Route key={parentPath} path={parentPath} element={<WorklistPage />}>
            <Route index element={<NoWorklistItemSelected />} />
            <Route
              path={RouteStrings.worklistItem}
              element={
                <Restricted
                  to={PermissionKey.ListsWaitingListView}
                  fallback={<NoAccess />}
                >
                  <ListItemDetails />
                </Restricted>
              }
            />
            <Route
              path={RouteStrings.worklistEntry}
              element={
                <Restricted
                  to={PermissionKey.SubjectJournalJournalEntryView}
                  fallback={<NoAccess />}
                >
                  <JournalEntryDetails />
                </Restricted>
              }
            />
          </Route>
        ))}
      </Route>
      <Route
        path={RouteStrings.organisationManage}
        element={
          <OrganisationManage>
            <Suspense>
              <Outlet />
            </Suspense>
          </OrganisationManage>
        }
      >
        <Route path="create-team" element={<OrganisationManageCreateTeam />} />

        <Route
          path=":organisationId/create-department"
          element={<OrganisationManageCreateDepartment />}
        />
      </Route>
      <Route path={RouteStrings.teamEdit} element={<TeamEdit />} />
      <Route
        path={RouteStrings.team}
        element={
          <TeamView>
            <Outlet />
          </TeamView>
        }
      ></Route>
      <Route path={RouteStrings.waitingList} element={<WaitingListPage />}>
        <Route
          path="unassigned"
          element={<WaitingListTable view="unassigned" />}
        />
        <Route path="me" element={<WaitingListTable view="me" />} />
        <Route path="" element={<WaitingListTable />} />
        {/* modals with routes goes here */}
      </Route>

      <Route
        path={RouteStrings.departmentEdit}
        element={<DepartmentEdit config={config} />}
      />
      <Route
        path={RouteStrings.providerCreateInvite}
        element={<InviteUserForm leviosaKindId={config.leviosaKindId} />}
      />
      <Route
        path={RouteStrings.teamDashboard}
        element={<Dashboard actorId={actor.id} />}
      />
      <Route path={RouteStrings.teamEdit} element={<TeamEdit />} />
      <Route
        path={RouteStrings.departmentEdit}
        element={<DepartmentEdit config={config} />}
      />
      <Route
        path={RouteStrings.calendar}
        element={
          <Calendar>
            <Suspense>
              <Outlet />
            </Suspense>
          </Calendar>
        }
      >
        <Route path="create-event" element={<CreateEventInstance />} />

        <Route path="view/:eventId/:providerId?" element={<></>} />
      </Route>
      <Route
        path={RouteStrings.serviceTypes}
        element={
          <ServiceTypesPage>
            <Suspense>
              <Outlet />
            </Suspense>
          </ServiceTypesPage>
        }
      >
        <Route path="create" element={<CreateServiceType />} />

        <Route path=":serviceTypeId" element={<EditServiceType />} />
      </Route>
      <Route
        path={RouteStrings.teamDashboard}
        element={<Dashboard actorId={actor.id} />}
      />
      <Route
        path={RouteStrings.calendarSchedule}
        element={<AvailabilitySchedule />}
      />
      <Route path={RouteStrings.userManagement} element={<UserManagement />} />
      <Route
        path={RouteStrings.registerSubject}
        element={<RegisterSubject />}
      />
      <Route path={RouteStrings.locations} element={<LocationsOverview />} />
      <Route path={RouteStrings.login} element={<Navigate to="/" replace />} />
      <Route path="*" element={<NotFoundPage />} />
    </SentryRoutes>
  )
}
