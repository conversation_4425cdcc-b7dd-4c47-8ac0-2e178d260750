import { Path } from "react-router-dom"

import { RouteStrings } from "./RouteStrings"

type RouteKeys = keyof typeof RouteStrings

export const getRecordPagePath = (
  route: (typeof RouteStrings)[RouteKeys],
  ...args: string[]
): Path["pathname"] => {
  let recordRoute = route.endsWith("/") ? route : route + "/"
  const rxReplaceParam = /:.+?\//

  for (let i = 0; i < args.length; i++) {
    recordRoute = recordRoute.replace(rxReplaceParam, args[i] + "/")
  }

  return recordRoute.endsWith("/")
    ? recordRoute.slice(0, recordRoute.length - 1)
    : recordRoute
}
