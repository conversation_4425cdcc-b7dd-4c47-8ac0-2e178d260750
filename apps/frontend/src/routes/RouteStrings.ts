export const PrivateRoutes = {
  home: "/",
  organisationPriceList: "/organisation/price-list/",
  organisationPriceListEdit: "/organisation/price-list/edit",
  nationalHealthInsurancePriceList: "/national-health-insurance/price-list/",
  subjectJournal: "/subject/:subjectId/journal",
  invoiceOverview: "/billing/invoices",
  patientInvoice: "/billing/issue-invoice/:invoiceId",
  createInterventionPeriod: "/subject/:subjectId/createInterventionPeriod",
  subjectEdit: "/subject/:subjectId/edit",
  journalTemplates: "/journal-templates/:templateType/:templateId?",

  organisationManage: "/organisation/manage",
  organisationManageCreateTeam: "/organisation/manage/create-team",
  organisationManageCreateDepartment:
    "/organisation/manage/:organisationId/create-department",

  teamEdit: "/team/:teamId/edit",
  team: "/team/:teamId",

  waitingList: "/waiting-list/:pageNumber/:view?",

  teamDashboard: "/team/:teamId/dashboard",
  departmentEdit: "/department/:departmentId/edit",
  departmentCreate: "/department/create",
  userCalendar: "/user/:selfId/calendar",

  userManagement: "/users/list/:view?",
  providerCreateInvite: "/users/invite",

  serviceTypes: "/services",
  createServiceType: "/services/create",
  editServiceType: "/services/:serviceTypeId",

  calendar: "/calendar/:view?",
  calendarViewEventInstance: "/calendar/:view?/view/:eventId",
  calendarCreateEventInstance: "/calendar/:view?/create-event",

  calendarSchedule: "/calendar/schedule/:providerId?/:scheduleId?/:serviceId?",

  providerView: "/provider/:providerId/view",

  worklist: "/worklist",
  worklistOrg: "/worklist/org",
  worklistItem: "item/:listItemId",
  worklistEntry: "entry/:journalEntryId",

  actorSettings: "/settings",
  registerSubject: "/subject/register",
  locations: "/locations/:buildingId?/:corridorId?",
  buildingCreate: "/locations/createBuilding",
  roomCreate: "/locations/createRoom",
  bedCreate: "/locations/createBed",
  corridorCreate: "/locations/createCorridor",
}

export const PublicRoutes = {
  login: "/login",
  forgetPassword: "/forgot-password",
  resetPassword: "/reset-password/:token",
  join: "/join/:token",
}

export const RouteStrings = { ...PublicRoutes, ...PrivateRoutes }

export type RouteKeys = keyof typeof RouteStrings
export type RouteString = (typeof RouteStrings)[RouteKeys]
