// write a mock data based on globalState context
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"

import {
  ActivationStatus,
  LanguageId,
  LeviosaKindId,
  PermissionKey,
  ServiceType,
  ProviderSpecialty,
} from "generated/graphql"

export const mockGlobalData: GlobalDataWithNonNullableActor = {
  __typename: "Query",
  config: {
    leviosaKindId: LeviosaKindId.Lite,
    build: "2023-03-20 10:09:34.115494 UTC",
    appVersion: "1.1",
    shortDateFormat: "YYYY-MM-DD",
    longDateFormat: "YYYY-MM-DDTHH:mm:ss",
    nnMode: false,
    personaIdentifierKind: {
      rxValidator: "^\\d{6}\\S{4}$|$\\d{6}-\\S{4}$",
      label: "Kennitala",
      __typename: "PersonaIdentifierKind",
    },
    uiLanguage: {
      id: LanguageId.EnGb,
      name: "English",
      ietfTag: "en-GB",
      isTranslationSupported: true,
      __typename: "Language",
    },
    contentLanguage: {
      id: LanguageId.EnGb,
      name: "English",
      ietfTag: "en-GB",
      isTranslationSupported: true,
      __typename: "Language",
    },
    __typename: "AppConfig",
  },
  actor: {
    id: "c142000d-90cf-4efa-9a5c-b5f95b5cabb3",
    permissions: [
      PermissionKey.AccountsProviderCreate,
      PermissionKey.AccountsProviderInvite,
    ],
    snippets: [],
    // roles: [],
    __typename: "Provider",
    selectedSubjects: [],
    recentSubjectInteractions: [],
    name: "Leviosa Moderator",
    nameInitials: "L. M.",
    specialty: ProviderSpecialty.Other,
    doctorNumber: "**********",
    phoneNumber: "8540066",
    teamsAndDepartments: [
      {
        __typename: "Team",
        id: "084da44f-91e3-4993-a918-b4ccfd172d6f",
        name: "Leviosa-Organisation moderators",
        description: "Dumbledore",
        serviceType: ServiceType.ClinicalAttendance,
        department: {
          __typename: "Department",
          id: "e81b19ab-de6d-4893-8df1-60b78f96467d",
          name: "Leviosa",
        },
      },
    ],
    organisation: {
      id: "e81b19ab-de6d-4893-8df1-60b78f96467d",
      name: "Leviosa",
      config: {
        __typename: "OrganisationConfig",
        enableNationalRegistry: false,
      },
      __typename: "Organisation",
    },
    lastSubjectInteraction: null,
    selectedDepartment: {
      id: "e81b19ab-de6d-4893-8df1-60b78f96467d",
      __typename: "Department",
    },
    externalEhrId: "APP-MODERATOR",
    activationStatus: ActivationStatus.Active,
  },
  // availableLanguages: [
  //   {
  //     id: LanguageId.EnGb,
  //     name: "English",
  //     ietfTag: "en-GB",
  //     isTranslationSupported: true,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.Is,
  //     name: "Icelandic",
  //     ietfTag: "is",
  //     isTranslationSupported: true,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.Ru,
  //     name: "Russian",
  //     ietfTag: "ru",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.Sv,
  //     name: "Swedish",
  //     ietfTag: "sv-SE",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.No,
  //     name: "Norwegian",
  //     ietfTag: "no-NO",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.Da,
  //     name: "Danish",
  //     ietfTag: "da",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.De,
  //     name: "German",
  //     ietfTag: "de-DE",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  //   {
  //     id: LanguageId.Fr,
  //     name: "French",
  //     ietfTag: "fr-FR",
  //     isTranslationSupported: false,
  //     __typename: "Language",
  //   },
  // ],
}
