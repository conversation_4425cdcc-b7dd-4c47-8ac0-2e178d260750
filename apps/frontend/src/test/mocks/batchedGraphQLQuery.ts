import { bypass, getResponse, http, HttpR<PERSON>ponse, RequestHandler } from "msw"

// <PERSON><PERSON> (Mock Service Worker) does not natively support batched GraphQL queries.
// This implementation follows the official MSW recipe for handling GraphQL query batching:
// https://mswjs.io/docs/graphql/mocking-responses/query-batching/
export function batchedGraphQLQuery(url: string, handlers: Request<PERSON>and<PERSON>[]) {
  return http.post(url, async ({ request }) => {
    const requestClone = request.clone()
    const payload = await request.clone().json()

    // Ignore non-batched GraphQL queries.
    if (!Array.isArray(payload)) {
      return
    }

    const responses = await Promise.all(
      payload.map(async (query) => {
        // Construct an individual query request
        // to the same URL but with an unwrapped query body.
        const queryRequest = new Request(requestClone, {
          body: JSON.stringify(query),
        })

        // Resolve the individual query request
        // against the list of request handlers you provide.
        const response = await getResponse(handlers, queryRequest)

        // Return the mocked response, if found.
        // Otherwise, perform the individual query as-is,
        // so it can be resolved against an original server.
        return response || fetch(bypass(queryRequest))
      })
    )

    // Read the mocked response JSON bodies to use
    // in the response to the entire batched query.
    const queryData = await Promise.all(
      responses.map((response) => response?.json())
    )

    return HttpResponse.json(queryData)
  })
}
