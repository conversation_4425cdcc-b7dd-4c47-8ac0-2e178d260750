import { graphql, HttpResponse } from "msw"

import {
  AuthenticationMethod,
  GetAuthenticationMethodDocument,
  GetAuthenticationMethodQuery,
  GetAuthenticationMethodQueryVariables,
  LanguageId,
  ProviderSpecialty,
  UserPassLoginDocument,
  UserPassLoginMutation,
  UserPassLoginMutationVariables,
  LogInWithElectronicIdDocument,
  LogInWithElectronicIdMutation,
  LogInWithElectronicIdMutationVariables,
  RemoveSessionCookieDocument,
  RemoveSessionCookieMutation,
  RemoveSessionCookieMutationVariables,
} from "generated/graphql"

export const getPasswordAuthMethodNoSession = graphql.query<
  GetAuthenticationMethodQuery,
  GetAuthenticationMethodQueryVariables
>(GetAuthenticationMethodDocument, () => {
  return HttpResponse.json({
    data: {
      __typename: "Query",
      authenticationMethod: {
        __typename: "AuthenticationMethodResponse",
        method: AuthenticationMethod.Password,
        session: null,
      },
    },
  })
})

export const getPasswordAuthMethodWithSession = graphql.query<
  GetAuthenticationMethodQuery,
  GetAuthenticationMethodQueryVariables
>(GetAuthenticationMethodDocument, () => {
  return HttpResponse.json({
    data: {
      __typename: "Query",
      authenticationMethod: {
        __typename: "AuthenticationMethodResponse",
        method: AuthenticationMethod.Password,
        session: {
          __typename: "SessionData",
          sessionAuthenticationMethod: AuthenticationMethod.Password,
          userId: "mock-user-id",
          phoneNumber: "111-1234",
          email: "<EMAIL>",
          name: "Mock Provider",
        },
      },
    },
  })
})

export const getElectronicIdAuthMethodNoSession = graphql.query<
  GetAuthenticationMethodQuery,
  GetAuthenticationMethodQueryVariables
>(GetAuthenticationMethodDocument, () => {
  return HttpResponse.json({
    data: {
      __typename: "Query",
      authenticationMethod: {
        __typename: "AuthenticationMethodResponse",
        method: AuthenticationMethod.ElectronicId,
        session: null,
      },
    },
  })
})

export const getElectronicIdAuthMethodWithSession = graphql.query<
  GetAuthenticationMethodQuery,
  GetAuthenticationMethodQueryVariables
>(GetAuthenticationMethodDocument, () => {
  return HttpResponse.json({
    data: {
      __typename: "Query",
      authenticationMethod: {
        __typename: "AuthenticationMethodResponse",
        method: AuthenticationMethod.ElectronicId,
        session: {
          __typename: "SessionData",
          sessionAuthenticationMethod: AuthenticationMethod.ElectronicId,
          userId: "mock-electronic-id-user-id",
          phoneNumber: "222-5678",
          email: "<EMAIL>",
          name: "Mock ElectronicId User",
        },
      },
    },
  })
})

export const userPassLoginMutationMock = graphql.mutation<
  UserPassLoginMutation,
  UserPassLoginMutationVariables
>(UserPassLoginDocument, ({ variables }) => {
  return HttpResponse.json({
    data: {
      __typename: "Mutation",
      logIn: {
        __typename: "LogInInfo",
        actor: {
          __typename: "Provider",
          id: "mock-provider-id",
          email: variables.email,
          name: "Mock Provider",
          specialty: ProviderSpecialty.AttendingPhysician,
          phoneNumber: "+************",
          doctorNumber: "123456",
          externalEhrId: "ehr-123",
          selectedDepartment: {
            __typename: "Department",
            id: "mock-department-id",
          },
          config: {
            __typename: "ProviderConfig",
            uiLanguage: {
              __typename: "Language",
              id: LanguageId.EnGb,
            },
          },
        },
        tokens: {
          __typename: "Tokens",
          accessToken: "mock-access-token",
          refreshToken: "mock-refresh-token",
        },
      },
    },
  })
})

export const userPassLoginMutationMockError = graphql.mutation<
  UserPassLoginMutation,
  UserPassLoginMutationVariables
>(UserPassLoginDocument, ({ variables }) => {
  const { email } = variables
  return HttpResponse.json({
    errors: [
      {
        message: `User with "${email}" was not found`,
      },
    ],
  })
})

export const electronicIdLoginMutationMock = graphql.mutation<
  LogInWithElectronicIdMutation,
  LogInWithElectronicIdMutationVariables
>(LogInWithElectronicIdDocument, ({ variables }) => {
  return HttpResponse.json({
    data: {
      __typename: "Mutation",
      logInWithElectronicId: {
        __typename: "LogInInfo",
        actor: {
          __typename: "Provider",
          id: "mock-electronic-id-user-id",
          phoneNumber: variables.input.phoneNumber,
          email: "<EMAIL>",
          name: "Mock ElectronicId User",
          specialty: ProviderSpecialty.AttendingPhysician,
          doctorNumber: "654321",
          externalEhrId: "ehr-456",
          selectedDepartment: {
            __typename: "Department",
            id: "mock-department-id",
          },
          config: {
            __typename: "ProviderConfig",
            uiLanguage: {
              __typename: "Language",
              id: LanguageId.EnGb,
            },
          },
        },
        tokens: {
          __typename: "Tokens",
          accessToken: "mock-electronic-id-access-token",
          refreshToken: "mock-electronic-id-refresh-token",
        },
      },
    },
  })
})

export const getErrorAuthMethod = graphql.query(
  GetAuthenticationMethodDocument,
  () => {
    return HttpResponse.json(
      {
        errors: [
          {
            message: "Internal server error",
            locations: [{ line: 2, column: 3 }],
            path: ["authenticationMethod"],
            extensions: { code: "INTERNAL_SERVER_ERROR" },
          },
        ],
      },
      { status: 500 }
    )
  }
)

export const removeSessionCookieMutationMock = graphql.mutation<
  RemoveSessionCookieMutation,
  RemoveSessionCookieMutationVariables
>(RemoveSessionCookieDocument, () => {
  return HttpResponse.json({
    data: {
      __typename: "Mutation",
      removeSessionCookie: true,
    },
  })
})

export const loginGQLHandlers = []
