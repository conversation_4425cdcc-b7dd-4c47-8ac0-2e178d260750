import { graphql } from "msw"

import { batchedGraphQLQuery } from "../batchedGraphQLQuery"
import { loginGQLHandlers } from "./loginHandlers"

const defaultHandlers = [
  // useful for debugging
  graphql.operation(({ query }) => {
    // console.log("MSW caught operation:", query)
  }),
]

export const handlers = [
  batchedGraphQLQuery("/graphql", [...defaultHandlers, ...loginGQLHandlers]),
  ...[...defaultHandlers, ...loginGQLHandlers],
]
