import { DEFAULT_LANG } from "i18n"
import i18next from "i18next"
import { initReactI18next } from "react-i18next"

import generic from "../locales/en_GB/generic.json"
import routes from "../locales/en_GB/routes.json"

export const i18nMockInstance = i18next.use(initReactI18next)
i18nMockInstance.init({
  lng: DEFAULT_LANG,
  fallbackLng: DEFAULT_LANG,
  ns: ["generic", "routes"],
  defaultNS: "generic",
  debug: false,
  initImmediate: false, // Synchronous for tests
  resources: {
    [DEFAULT_LANG]: {
      generic,
      routes,
    },
  },
  interpolation: {
    escapeValue: false,
  },
})
