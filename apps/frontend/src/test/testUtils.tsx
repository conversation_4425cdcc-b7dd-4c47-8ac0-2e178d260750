import { Apollo<PERSON>rovider } from "@apollo/client"
import {
  RenderResult,
  render as rtlR<PERSON>,
  RenderOptions as RTLRenderOptions,
} from "@testing-library/react"
import { ReactNode } from "react"
import { I18nextProvider } from "react-i18next"
import { MemoryRouter } from "react-router-dom"
import { i18nMockInstance } from "test/i18nTestUtils"

import { AuthProvider } from "features/authentication/AuthProvider"
import { apolloClient } from "lib/apollo/apolloClient"

type CustomRenderOptions = { initialEntries?: string[] } & RTLRenderOptions

// custom render function for React component testing
function render(
  ui: ReactNode,
  { initialEntries = ["/"], ...options }: CustomRenderOptions = {}
): RenderResult {
  const Wrapper = ({ children }: { children: ReactNode }) => (
    <ApolloProvider client={apolloClient}>
      <I18nextProvider i18n={i18nMockInstance}>
        <MemoryRouter initialEntries={initialEntries}>
          <AuthProvider>{children}</AuthProvider>
        </MemoryRouter>
      </I18nextProvider>
    </ApolloProvider>
  )
  return rtlRender(ui, { wrapper: Wrapper, ...options })
}

export * from "@testing-library/react"
export { render }
