import { useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { Status } from "ui"

type PasswordStatus = {
  status: Status
  message: string
}

export default function usePasswordValidation() {
  const { t } = useTranslation()
  const passwordRef = useRef<HTMLInputElement>(null)
  const repeatedPasswordRef = useRef<HTMLInputElement>(null)
  const [passwordStatus, setPasswordStatus] = useState<PasswordStatus>({
    status: "default",
    message: "",
  })
  const [repeatedPasswordStatus, setRepeatedPasswordStatus] =
    useState<PasswordStatus>({ status: "default", message: "" })

  const validatePassword = () => {
    if (passwordRef.current === null) return false

    const password = passwordRef.current.value

    if (password.length < 8) {
      setPasswordStatus({
        status: "error",
        message: t("routes:validations.passwordIsTooShort"),
      })
      passwordRef.current.setCustomValidity(
        t("routes:validations.passwordIsTooShort")
      )

      return false
    }

    setPasswordStatus({
      status: "success",
      message: "",
    })
    passwordRef.current.setCustomValidity("")

    return true
  }

  const validateRepeatedPassword = () => {
    if (passwordRef.current === null || repeatedPasswordRef.current === null)
      return false

    const password = passwordRef.current.value
    const repeatedPassword = repeatedPasswordRef.current.value

    if (password !== repeatedPassword) {
      setRepeatedPasswordStatus({
        status: "error",
        message: t("routes:validations.passwordsNoMatch"),
      })
      repeatedPasswordRef.current.setCustomValidity(
        t("routes:validations.passwordsNoMatch")
      )

      return false
    }

    setRepeatedPasswordStatus({
      status: "success",
      message: "",
    })
    repeatedPasswordRef.current.setCustomValidity("")

    return true
  }

  return {
    passwordRef,
    repeatedPasswordRef,
    passwordStatus,
    setPasswordStatus,
    repeatedPasswordStatus,
    setRepeatedPasswordStatus,
    validatePassword,
    validateRepeatedPassword,
  }
}
