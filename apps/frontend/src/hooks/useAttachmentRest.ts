import { useState } from "react"
import { useTranslation } from "react-i18next"

import { getTokens } from "features/authentication/utils/tokenStorage"
import { isFileSizeWithinLimit } from "utils/formatFileSize/formatFileSize"

export const useAttachmentRest = () => {
  const [isAttachmentsLoading, setIsAttachmentsLoading] = useState(false)
  const baseUrl = import.meta.env.VITE_BACKEND_API_URL || window.origin

  const { t } = useTranslation()

  const token = getTokens()?.accessToken

  const getFileFormData = (files: FileList | File) => {
    const formData = new FormData()

    if (files instanceof File) {
      formData.append("files", files)
      return formData
    }

    for (const singleFile of files) {
      formData.append("files", singleFile)
    }

    return formData
  }

  const attachFileToJournalEntry = async (
    journalEntryId: string,
    files: FileList | File
  ) => {
    const url = `${baseUrl}/api/journal-entry/${journalEntryId}/attachment/upload?token=${token}`

    const formData = getFileFormData(files)

    if (!isFileSizeWithinLimit(files)) {
      throw new Error(t("File size should be less than 100MB"))
    }

    setIsAttachmentsLoading(true)
    const response = await fetch(url, {
      method: "POST",
      body: formData,
    })
    setIsAttachmentsLoading(false)

    if (response.ok) {
      return "success"
    }

    throw new Error(t("Failed to attach file to journal entry"))
  }

  const getUrlForFile = (fileId: string) => {
    return `${baseUrl}/api/file/${fileId}?token=${token}`
  }

  return {
    attachFileToJournalEntry,
    getUrlForFile,
    isAttachmentsLoading,
  }
}
