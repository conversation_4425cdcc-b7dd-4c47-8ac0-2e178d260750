import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "react"

import { useSetSubjectJournalFocusedItemMutation } from "generated/graphql"

type JournalBlockClickType = {
  encounterId: string | null
  sectionId: string | null
}

export const useJournalBlockClick = ({
  encounterId,
  sectionId,
}: JournalBlockClickType) => {
  const [setSubjectJournalFocusedItem] =
    useSetSubjectJournalFocusedItemMutation()

  const handleClick: MouseEventHandler<HTMLAnchorElement> = async (e) => {
    e.stopPropagation()

    await setSubjectJournalFocusedItem({
      variables: { journalEntryBlockId: sectionId, encounterId },
    })
  }

  return {
    onClick: handleClick,
  }
}
