import { useCallback, useRef } from "react"

import useLayoutEffectImpl from "utils/useLayoutEffect"

// @ts-expect-error - No idea why -GSA
export function useEvent<T extends (...args) => void>(callback?: T) {
  // @ts-expect-error - No idea why -GSA
  const ref = useRef<T | undefined>(() => {
    throw new Error("Cannot call an event handler while rendering.")
  })
  useLayoutEffectImpl(() => {
    ref.current = callback
  })
  return useCallback(
    // @ts-expect-error - No idea why -GSA
    (...args: Parameters<T>): ReturnType<T> => ref.current?.(...args),
    []
  )
}
