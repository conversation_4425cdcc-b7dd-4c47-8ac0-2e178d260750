import { renderHook } from "@testing-library/react"
import { describe, test, expect } from "vitest"

import { useHtmlToMarkdown } from "./useHtmlToMarkdown"

describe("useHtmlToMarkdown", () => {
  // Helper function to normalize markdown for comparison
  const normalizeMarkdown = (markdown: string): string[] => {
    return markdown
      .trim()
      .split("\n")
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
  }

  // Helper function for testing with normalized comparison
  const testConversionNormalized = (html: string, expectedMarkdown: string) => {
    const { result } = renderHook(() => useHtmlToMarkdown())
    const output = result.current.convertToMarkdown(html)

    const normalizedOutput = normalizeMarkdown(output)
    const normalizedExpected = normalizeMarkdown(expectedMarkdown)

    expect(normalizedOutput).toEqual(normalizedExpected)
  }

  // Original test helper for simple cases
  const testConversion = (html: string, expectedMarkdown: string) => {
    const { result } = renderHook(() => useHtmlToMarkdown())
    const markdown = result.current.convertToMarkdown(html)
    expect(markdown.trim()).toBe(expectedMarkdown.trim())
  }

  describe("HTML cleaning", () => {
    test("removes colgroups and col tags", () => {
      const html =
        '<table><colgroup><col width="50"><col width="50"></colgroup><tr><td>Cell</td></tr></table>'
      const expected = `|  |
| --- |
| Cell |`
      testConversion(html, expected)
    })

    test("removes inline styles and unnecessary attributes", () => {
      const html =
        '<div style="color: red;" dir="ltr" data-custom="value" width="100" height="200">Content</div>'
      const expected = "Content"
      testConversion(html, expected)
    })

    test("cleans up span tags", () => {
      const html = "<p>Text with <span>span</span> tags</p>"
      const expected = "Text with span tags"
      testConversion(html, expected)
    })
  })

  describe("Text formatting", () => {
    test("converts emphasis (em) tags", () => {
      const html = "<p>This is <em>emphasized</em> text</p>"
      const expected = "This is *emphasized* text"
      testConversion(html, expected)
    })

    test("converts underline tags", () => {
      const html = "<p>This is <u>underlined</u> text</p>"
      const expected = "This is __underlined__ text"
      testConversion(html, expected)
    })

    test("converts bold tags", () => {
      const html = "<p>This is <b>bold</b> and <strong>strong</strong> text</p>"
      const expected = "This is **bold** and **strong** text"
      testConversion(html, expected)
    })

    test("converts superscript tags", () => {
      const html = "<p>This is <sup>superscript</sup> text</p>"
      const expected = "This is <sup>superscript</sup> text"
      testConversion(html, expected)
    })

    test("converts subscript tags", () => {
      const html = "<p>This is <sub>subscript</sub> text</p>"
      const expected = "This is <sub>subscript</sub> text"
      testConversion(html, expected)
    })

    test("converts strikethrough tags", () => {
      const html =
        "<p>This is <s>strikethrough</s> and <del>deleted</del> text</p>"
      const expected = "This is ~~strikethrough~~ and ~~deleted~~ text"
      testConversion(html, expected)
    })
  })

  describe("List handling", () => {
    test("converts ordered lists", () => {
      const html =
        "<ol><li>First item</li><li>Second item</li><li>Third item</li></ol>"
      const expected = "1. First item\n2. Second item\n3. Third item"
      testConversionNormalized(html, expected)
    })

    test("converts checklists, preserve empty items", () => {
      const html = `
        <ul __lexicallisttype="check">
          <li aria-checked="true"><span>Done item</span></li>
          <li aria-checked="false"><span>Todo item</span></li>
          <li aria-checked="true"><span></span></li>
        </ul>
      `
      const expected = "- [x] Done item\n- [ ] Todo item\n- [x] \n"
      testConversionNormalized(html, expected)
    })
  })

  describe("Table handling", () => {
    test("converts simple table with header row", () => {
      const html = `
        <table>
          <tr><th>Header 1</th><th>Header 2</th></tr>
          <tr><td>Row 1, Cell 1</td><td>Row 1, Cell 2</td></tr>
          <tr><td>Row 2, Cell 1</td><td>Row 2, Cell 2</td></tr>
        </table>
      `
      const expected = `
| Header 1 | Header 2 |
| --- | --- |
| Row 1, Cell 1 | Row 1, Cell 2 |
| Row 2, Cell 1 | Row 2, Cell 2 |
`
      testConversion(html, expected)
    })

    test("converts table without header row", () => {
      const html = `
        <table>
          <tr><td>Row 1, Cell 1</td><td>Row 1, Cell 2</td></tr>
          <tr><td>Row 2, Cell 1</td><td>Row 2, Cell 2</td></tr>
        </table>
      `
      const expected = `
|  |  |
| --- | --- |
| Row 1, Cell 1 | Row 1, Cell 2 |
| Row 2, Cell 1 | Row 2, Cell 2 |
`
      testConversion(html, expected)
    })
  })

  describe("Block elements", () => {
    test("converts code blocks", () => {
      const html = "<pre>const x = 10;\nconst y = 20;</pre>"
      const expected = "```\nconst x = 10;\nconst y = 20;\n```"
      testConversion(html, expected)
    })

    test("converts blockquotes", () => {
      const html =
        "<blockquote><span>halló</span><br><span>önnur lína</span></blockquote>"
      const expected = "> halló\n> önnur lína"
      testConversionNormalized(html, expected)
    })
  })

  describe("Special cases", () => {
    test("preserves hash symbols in normal text", () => {
      const html = "<p>This is a #hashtag in the middle of text</p>"
      const expected = "This is a #hashtag in the middle of text"
      testConversion(html, expected)
    })

    test("escapes hash symbols only in clinical code spans", () => {
      const html =
        '<p>This text has a <span data-reference-type="clinical-code"># 123 clinical code</span> in the middle</p>'
      const expected = "This text has a \\# 123 clinical code in the middle"
      testConversion(html, expected)
    })

    test("does not escape hash symbols in normal text", () => {
      const html = "<p>This is a normal #hashtag in the middle of text</p>"
      const expected = "This is a normal #hashtag in the middle of text"
      testConversion(html, expected)
    })

    test("preserves heading functionality with hash symbols", () => {
      const html = "<h1>This is a heading</h1><p>Text with #hashtag</p>"
      const expected = "# This is a heading\n\nText with #hashtag"
      testConversionNormalized(html, expected)
    })

    test("properly handles both headings and clinical code hashes", () => {
      const html = `
        <h1>Patient Report</h1>
        <p>Patient has been diagnosed with <span data-reference-type="clinical-code">#J45.9 sickness</span> (Asthma) 
        and requires monitoring.</p>
        <h2>Treatment Plan</h2>
        <p>Regular #follow-up visits recommended.</p>
      `
      const expected = `
        # Patient Report
        
        Patient has been diagnosed with #J45.9 sickness (Asthma) and requires monitoring.
        
        ## Treatment Plan
        
        Regular #follow-up visits recommended.
      `
      testConversionNormalized(html, expected)
    })

    test("correctly handles headings with line breaks", () => {
      const html = `<h1 dir="ltr"><span style="white-space: pre-wrap;">Heading</span><br><span style="white-space: pre-wrap;">line2</span></h1>`
      const expected = "# Heading line2"
      testConversion(html, expected)
    })

    test("correctly handles multi-level headings with multiple line breaks", () => {
      const html = `
        <h1>First Level<br>Heading</h1>
        <p>Some paragraph text</p>
        <h2>Second <br><br> Level<br>Heading</h2>
      `
      const expected = `
        # First Level Heading
        
        Some paragraph text
        
        ## Second Level Heading
      `
      testConversionNormalized(html, expected)
    })

    test("handles complex nested content", () => {
      const html = `
        <div>
          <h1>Title</h1>
          <p>Paragraph with <strong>bold</strong> and <em>italic</em></p>
          <ul>
            <li>List item 1</li>
            <li>List item 2</li>
          </ul>
        </div>
      `
      const expected =
        "# Title\n\nParagraph with **bold** and *italic*\n\n* List item 1\n* List item 2"
      testConversionNormalized(html, expected)
    })
  })
})
