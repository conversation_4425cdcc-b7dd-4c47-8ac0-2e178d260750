import { isTypename } from "utils/isTypename"

import { LocationType, useGetRoomsQuery } from "generated/graphql"

// Assuming no Corridors
export const useFetchSubjectLocation = () => {
  const { data, ...rest } = useGetRoomsQuery({
    variables: { locationType: LocationType.Room },
  })
  const rooms = data?.locations.filter(isTypename("Room")) || []
  const locationsList = rooms.flatMap((room) => {
    return room.beds.beds.map((bed) => {
      return {
        value: bed.id,
        label: `${room.label}: ${bed.label}`,
      }
    })
  })
  return { data: locationsList, rest }
}
