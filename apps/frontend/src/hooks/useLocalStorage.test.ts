import { renderHook, act } from "@testing-library/react"

import useLocalStorage from "./useLocalStorage"

describe("useLocalStorage", () => {
  beforeEach(() => {
    localStorage.clear()
  })

  it("should initialize with default value if no value is in localStorage", () => {
    const { result } = renderHook(() =>
      useLocalStorage("testKey", "defaultValue")
    )

    expect(result.current[0]).toBe("defaultValue")
  })

  it("should initialize with value from localStorage if it exists", () => {
    localStorage.setItem("testKey", JSON.stringify("storedValue"))
    const { result } = renderHook(() =>
      useLocalStorage("testKey", "defaultValue")
    )

    expect(result.current[0]).toBe("storedValue")
  })

  it("should update localStorage when value is set", () => {
    const { result } = renderHook(() =>
      useLocalStorage("testKey", "defaultValue")
    )
    act(() => {
      result.current[1]("newValue")
    })

    expect(localStorage.getItem("testKey")).toBe(JSON.stringify("newValue"))
    expect(result.current[0]).toBe("newValue")
  })

  // it("should update state when storage event is fired", () => {
  //   const { result } = renderHook(() =>
  //     useLocalStorage("testKey", "defaultValue")
  //   )

  //   act(() => {
  //     localStorage.setItem("testKey", JSON.stringify("newValue"))
  //     window.dispatchEvent(new StorageEvent("storage", { key: "testKey" }))
  //   })

  //   expect(result.current[0]).toBe("newValue")
  // })

  it("should remove event listener on unmount", () => {
    const { unmount } = renderHook(() =>
      useLocalStorage("testKey", "defaultValue")
    )
    const removeEventListenerSpy = vi.spyOn(window, "removeEventListener")

    unmount()

    expect(removeEventListenerSpy).toHaveBeenCalledWith(
      "storage",
      expect.any(Function)
    )
  })
})
