import { ApolloError } from "@apollo/client"
import { useState } from "react"

import { notification } from "ui"

import {
  EncounterStatus,
  namedOperations,
  useCancelEncounterMutation,
  useCheckInEncounterMutation,
  useCheckOutEncounterMutation,
  useConcludeEncounterMutation,
  useDiscardEncounterMutation,
} from "generated/graphql"

type EncounterActionType = {
  encounterId: string
}

export const useEncounterActions = ({ encounterId }: EncounterActionType) => {
  const now = new Date()

  const [isDialogForDeleteOpen, setIsDialogForDeleteOpen] = useState(false)

  const handleErrorMessage = (error: ApolloError) => {
    notification.create({
      status: "error",
      message: error.message,
    })
  }

  const [checkInEncounter] = useCheckInEncounterMutation({
    variables: {
      encounterId,
    },
    refetchQueries: [
      namedOperations.Query.GetSubjectSummaryEncounters,
      namedOperations.Query.Dashboard,
      namedOperations.Query.GetSubjectJournal,
    ],
    onError: (error) => {
      handleErrorMessage(error)
    },
  })

  const [checkOutEncounter] = useCheckOutEncounterMutation({
    variables: {
      encounterId,
    },
    refetchQueries: [
      namedOperations.Query.GetSubjectSummaryEncounters,
      namedOperations.Query.Dashboard,
      namedOperations.Query.GetSubjectJournal,
    ],
    onError: (error) => {
      handleErrorMessage(error)
    },
  })

  const [concludeEncounter] = useConcludeEncounterMutation({
    variables: {
      encounterId,
      input: {
        toDate: now,
      },
    },
    refetchQueries: [
      namedOperations.Query.GetSubjectSummaryEncounters,
      namedOperations.Query.Dashboard,
      namedOperations.Query.GetSubjectJournal,
    ],
    onError: (error) => {
      handleErrorMessage(error)
    },
  })

  const [cancelEncounter] = useCancelEncounterMutation({
    variables: {
      encounterId,
      input: {},
    },
    refetchQueries: [
      namedOperations.Query.GetSubjectSummaryEncounters,
      namedOperations.Query.Dashboard,
      namedOperations.Query.GetSubjectJournal,
    ],
    onError: (error) => {
      handleErrorMessage(error)
    },
  })

  const [discardEncounter] = useDiscardEncounterMutation({
    variables: {
      discardEncounterId: encounterId,
    },
    refetchQueries: [
      namedOperations.Query.GetSubjectSummaryEncounters,
      namedOperations.Query.Dashboard,
      namedOperations.Query.GetSubjectJournal,
    ],
    onError: (error) => {
      handleErrorMessage(error)
    },
    onCompleted: () => {
      notification.create({
        status: "success",
        message: "Encounter has been deleted",
      })
    },
  })

  const prioritizedEncounterTransitions: [EncounterStatus, () => void][] = [
    [EncounterStatus.InProgress, checkInEncounter],
    [EncounterStatus.CheckedOut, checkOutEncounter],
    [EncounterStatus.Concluded, concludeEncounter],
    [EncounterStatus.Cancelled, cancelEncounter],
  ]

  const encounterTransitions: Record<EncounterStatus, () => void> = {
    [EncounterStatus.InProgress]: checkInEncounter,
    [EncounterStatus.CheckedOut]: checkOutEncounter,
    [EncounterStatus.Concluded]: concludeEncounter,
    [EncounterStatus.Cancelled]: cancelEncounter,
    [EncounterStatus.Deleted]: () => setIsDialogForDeleteOpen(true),
    [EncounterStatus.Planned]: () => {
      throw new Error("not implemented") //COMEBACK frontend implementation needed
    },
  }

  return {
    prioritizedEncounterTransitions,
    encounterTransitions,
    encounter: {
      isDialogForDeleteOpen,
      setIsDialogForDeleteOpen,
      discardEncounter,
    },
  }
}
