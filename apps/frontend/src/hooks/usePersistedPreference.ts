import { addMinutes } from "date-fns"
import { useCallback, useEffect } from "react"
import { z } from "zod"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

import useLocalStorage from "./useLocalStorage"

const preferenceSchema = z.object({
  expiry: z.string().optional(),
  value: z.string().optional(),
})

const isExpired = (expiry?: string) => {
  if (!expiry) return false

  return new Date(expiry) < new Date()
}

const getStorageObject = (
  value: string | undefined,
  timeToLiveMinutes?: number
) => {
  const expiry = timeToLiveMinutes
    ? addMinutes(new Date(), timeToLiveMinutes).toISOString()
    : undefined
  return { expiry, value }
}

/**
 * Arguments for the usePersistedPreference hook.
 *
 * @typedef {Object} PersistedPreferenceArgs
 * @property {string} key - The key under which the preference will be stored.
 * @property {T} [defaultValue] - The default value to use if no value is found in storage.
 * @property {number} [timeToLiveMinutes] - The time-to-live for the stored preference in minutes.
 */
type PersistedPreferenceArgs<T extends string = string> = {
  key: string
  defaultValue?: T
  timeToLiveMinutes?: number
}

/**
 * A custom hook that manages a persisted preference with an optional time-to-live (TTL).
 * The preference is stored in local storage and is associated with a specific actor.
 *
 * @example
 * const [preference, setPreference] = usePersistedPreference({
 *   key: 'theme',
 *   defaultValue: 'light',
 *   timeToLiveMinutes: 60,
 * });
 *
 * // Update the preference
 * setPreference('dark');
 */
export function usePersistedPreference<T extends string = string>({
  key,
  defaultValue,
  timeToLiveMinutes,
}: PersistedPreferenceArgs<T>) {
  const { globalData } = useGlobalState()
  const actorId = globalData.actor.id
  const lsKey = `${actorId}-${key}`
  const [storedPreference, setPreferenceValue] = useLocalStorage(
    lsKey,
    getStorageObject(defaultValue, timeToLiveMinutes),
    preferenceSchema
  )

  const storePreference = useCallback(
    (value: T | undefined) => {
      const preference = getStorageObject(value, timeToLiveMinutes)
      setPreferenceValue(preference)
    },
    [setPreferenceValue, timeToLiveMinutes]
  )

  let isValueValid = false
  let returnStorageObject: z.infer<typeof preferenceSchema> = {
    value: defaultValue,
    expiry: undefined,
  }
  try {
    returnStorageObject = preferenceSchema.parse(storedPreference)
    isValueValid = !isExpired(returnStorageObject.expiry)
  } catch (e) {
    // The stored value is invalid - return the default value
    console.error(e)
  }

  useEffect(() => {
    if (!isValueValid) {
      storePreference(defaultValue)
    }
  }, [defaultValue, isValueValid, storePreference])

  return [returnStorageObject.value as T | undefined, storePreference] as const
}
