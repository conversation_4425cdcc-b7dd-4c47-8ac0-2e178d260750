import { act, renderHook } from "@testing-library/react"
import { ReactNode } from "react"
import { mockGlobalData } from "test/mocks/GlobalStateMock"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"

import { usePersistedPreference } from "./usePersistedPreference"

const ProviderWrapper = ({ children }: { children: ReactNode }) => {
  return (
    <GlobalProvider currentTeamId="-1" globalData={mockGlobalData}>
      {children}
    </GlobalProvider>
  )
}

const defaultValue = "default-value"
const newValue = "new-value"
const key = "test-key"
const lsKey = `${mockGlobalData.actor.id}-${key}`

const renderUsePersistedPreferenceHook = (
  key = "test-key",
  defaultValue = "default-value",
  timeToLiveMinutes?: number
) =>
  renderHook(
    () =>
      usePersistedPreference({
        key,
        defaultValue,
        timeToLiveMinutes,
      }),
    {
      wrapper: ProviderWrapper,
    }
  )

describe("usePersistedPreference", () => {
  beforeEach(() => {
    // Clear localStorage before each test
    localStorage.clear()
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  it("should return default value if no value is found in storage", () => {
    const { result } = renderUsePersistedPreferenceHook()
    expect(result.current[0]).toBe(defaultValue)

    const lsValue = JSON.parse(localStorage.getItem(lsKey) || "{}")
    expect(lsValue).toEqual({ value: defaultValue })
  })

  it("should store the new preference value", () => {
    const { result } = renderUsePersistedPreferenceHook()
    expect(result.current[0]).toBe(defaultValue)

    act(() => {
      result.current[1](newValue)
    })

    expect(result.current[0]).toBe(newValue)
  })

  it("should reset to default value if stored value is expired", () => {
    const initialDate = new Date("2025-01-01T01:00:00Z")
    vi.useFakeTimers()
    vi.setSystemTime(initialDate)
    const timeToLiveMinutes = 8

    const { result, rerender } = renderUsePersistedPreferenceHook(
      key,
      defaultValue,
      timeToLiveMinutes
    )

    act(() => {
      result.current[1](newValue)
    })

    expect(result.current[0]).toBe(newValue)

    const expiredTime = new Date("2025-01-01T01:09:00Z")
    vi.setSystemTime(expiredTime)
    rerender()

    expect(result.current[0]).toBe(defaultValue)
    vi.useRealTimers()
  })
})
