/**
 * A custom hook that provides functionality to convert HTML content into Markdown format.
 * It includes custom rules for handling various HTML elements and attributes from Lexical,
 * as well as cleaning up unnecessary HTML tags and attributes.
 *
 * @returns {Object} An object containing the following:
 * - `convertToMarkdown(html: string): string`: A function that takes an HTML string as input
 *   and returns the corresponding Markdown string.
 *
 * ### Features:
 * - Cleans up unnecessary HTML tags and attributes, such as `<colgroup>`, inline styles, and more.
 * - Escapes hash symbols in clinical code spans to prevent Markdown parsing issues.
 * - Custom rules for converting specific HTML elements to Markdown:
 *   - `<em>` to `*text*`
 *   - `<u>` to `__text__`
 *   - `<b>` and `<strong>` to `**text**`
 *   - `<s>` and `<del>` to `~~text~~`
 *   - `<pre>` to fenced code blocks
 *   - `<table>` to Markdown table syntax
 *   - `<ul>` with `__lexicallisttype="check"` to checklists (`- [x]` or `- [ ]`)
 *   - `<ol>` to ordered lists
 *   - `<blockquote>` to blockquote syntax (`> text`)
 *   - `<mark> to ==text==
 *
 * ### Example Usage:
 * ```typescript
 * const { convertToMarkdown } = useHtmlToMarkdown();
 * const html = "<p><strong>Hello</strong> World!</p>";
 * const markdown = convertToMarkdown(html);
 * console.log(markdown); // Outputs: "**Hello** World!"
 * ```
 */
import { useCallback } from "react"
import TurndownService from "turndown"

import { processHTMLList } from "../utils/htmlListToMarkdownProcessing"

export function useHtmlToMarkdown() {
  const cleanHtml = useCallback((html: string): string => {
    return (
      html
        // First, properly fix headings with line breaks to ensure they stay on a single line
        // Find all heading tags and their content
        .replace(
          /(<h[1-6][^>]*>)([\s\S]*?)(<\/h[1-6]>)/gi,
          (match, openTag, content, closeTag) => {
            // Replace all <br> tags and any excessive whitespace with a single space
            const cleanContent = content
              .replace(/<br\s*\/?>/gi, " ")
              .replace(/\s+/g, " ")
              .trim()
            return openTag + cleanContent + closeTag
          }
        )
        // Remove colgroups and unnecessary tags
        .replace(/<colgroup>[\s\S]*?<\/colgroup>/gi, "")
        .replace(/<col[^>]*>/gi, "")

        // Remove inline styles and attributes that don't affect content
        .replace(/\s?(style|dir|colspan|rowspan|width|height)="[^"]*"/gi, "")

        // Don't remove p tags since we need them for newlines
        // We need to keep data-reference-type attribute for clinical code handling
        // but will clean up other span elements
        .replace(
          /<span(?!\s[^>]*data-reference-type="clinical-code")[^>]*>/gi,
          ""
        )
        .replace(/<\/span>/gi, "")
        // Fix nested bold elements - replace <b><strong>text</strong></b> with just <strong>text</strong>
        .replace(
          /<b>\s*<strong([^>]*)>([\s\S]*?)<\/strong>\s*<\/b>/gi,
          "<strong$1>$2</strong>"
        )
        // Also handle the reverse case
        .replace(
          /<strong[^>]*>\s*<b>([\s\S]*?)<\/b>\s*<\/strong>/gi,
          "<strong>$1</strong>"
        )
    )
  }, [])

  // Lexical specific functionality
  const escapeHashInText = useCallback((markdown: string): string => {
    // This will find text that looks like "#123 clinical code" where the hash is inside a clinical code span
    return markdown.replace(
      /data-reference-type="clinical-code"[^#]*#(\w+)/g,
      (match, codeText) => {
        // Replace the hash with an escaped hash
        return match.replace(`#${codeText}`, `\\#${codeText}`)
      }
    )
  }, [])

  const convertToMarkdown = useCallback(
    (html: string): string => {
      const turndownService = new TurndownService({
        headingStyle: "atx",
        bulletListMarker: "*",
        codeBlockStyle: "fenced",
      })

      turndownService.addRule("emphasis", {
        filter: "em",
        replacement: (content) => `*${content}*`, // Convert <em> to *
      })

      turndownService.addRule("underline", {
        filter: "u",
        replacement: (content) => `__${content}__`, // Convert <u> to __text__, which in markdown is essentially interpreted as bold but since markdown doesnt natively support underline we do this instead
      })

      // Custom rule for handling <b>, <strong> (bold)
      turndownService.addRule("bold", {
        filter: ["b", "strong"],
        replacement: (content) => `**${content}**`, // Convert <b> and <strong> to **
      })

      // Custom rule for handling <sup> (superscript)
      turndownService.addRule("superscript", {
        filter: "sup",
        replacement: (content) => `<sup>${content}</sup>`,
      })

      // // Custom rule for handling <sub> (subscript)
      turndownService.addRule("subscript", {
        filter: "sub",
        replacement: (content) => `<sub>${content}</sub>`,
      })

      // Custom rule for handling <s>, <del> (strikethrough)
      turndownService.addRule("strikethrough", {
        filter: ["s", "del"],
        replacement: (content) => `~~${content}~~`, // Convert <s> and <del> to ~~text~~
      })

      // Custom rule for handling <pre> (code block)
      turndownService.addRule("code", {
        filter: "pre",
        replacement: (content) => `\`\`\`\n${content}\n\`\`\``, // Convert <pre> to fenced code block
      })

      // Custom rule for handling <table>
      turndownService.addRule("table", {
        filter: "table",
        replacement: (_content, node) => {
          const table = node as HTMLTableElement
          const rows = Array.from(table.querySelectorAll("tr"))
          if (rows.length === 0) return ""

          const firstThRow = rows.find((row) => row.querySelector("th"))

          const [headerRow, dataRows] = firstThRow
            ? [firstThRow, rows.filter((row) => row !== firstThRow)]
            : [null, rows]

          const colCount = headerRow
            ? headerRow.querySelectorAll("th").length
            : rows[0]?.querySelectorAll("td").length || 0

          const header = `| ${(headerRow
            ? Array.from(headerRow.querySelectorAll("th")).map((cell) =>
                (cell.textContent || "").trim()
              )
            : Array(colCount).fill("")
          ).join(" | ")} |`

          const separator = `| ${Array(colCount).fill("---").join(" | ")} |`

          const body = dataRows
            .map((row) => {
              const cells = Array.from(row.querySelectorAll("td, th"))
              const contents = cells.map((cell) =>
                (cell.textContent || "").trim()
              )
              return `| ${contents.join(" | ")} |`
            })
            .join("\n")

          return `\n${header}\n${separator}\n${body}\n\n`
        },
      })

      // Lexical specific rule
      // Custom rule for handling <ul> with __lexicallisttype="check" (checklists)
      turndownService.addRule("checklist", {
        filter: (node: HTMLElement) =>
          node.nodeName === "UL" &&
          node.getAttribute("__lexicallisttype") === "check",

        replacement: (_content, node): string => {
          if (!node) return ""

          let markdown = ""
          const listItems = node.childNodes as NodeListOf<ChildNode>

          listItems.forEach((child) => {
            if (
              child.nodeType === 1 &&
              (child as HTMLElement).nodeName === "LI"
            ) {
              const li = child as HTMLLIElement
              const checked = li.getAttribute("aria-checked") === "true"
              const checkbox = checked ? "- [x] " : "- [ ] "

              const span = Array.from(li.childNodes).find(
                (n) =>
                  n.nodeType === 1 && (n as HTMLElement).nodeName === "SPAN"
              ) as HTMLSpanElement | undefined

              // Don't trim to preserve whitespace and handle empty items
              const text = span?.textContent || li.textContent || ""

              markdown += `${checkbox}${text}\n`
            }
          })

          return markdown
        },
      })

      // Custom rule for handling <ul>
      turndownService.addRule("unorderedList", {
        filter: (node: HTMLElement) =>
          node.nodeName === "UL" &&
          node.getAttribute("__lexicallisttype") !== "check", // Skip checklists
        replacement: (_content, node) => {
          return `\n${processHTMLList(node as HTMLElement, 0, false)}\n`
        },
      })

      // Custom rule for handling <ol>
      turndownService.addRule("orderedList", {
        filter: "ol",
        replacement: (_content, node) => {
          return `\n${processHTMLList(node as HTMLElement, 0, true)}\n`
        },
      })

      // Custom rule for blockquotes (<blockquote>)
      turndownService.addRule("blockquote", {
        filter: "blockquote",
        replacement: (content) => `\n> ${content.replace(/\n/g, "\n> ")}`, // Start with a new line and convert <blockquote> to > text with new lines prefixed by >
      })

      // Custom rule for mark (<mark>)
      turndownService.addRule("mark", {
        filter: "mark",
        replacement: (content) => `==${content}==`, // Convert <mark> to ==text==
      })

      const cleanedHtml = cleanHtml(html)
      const markdown = turndownService.turndown(cleanedHtml)

      return escapeHashInText(markdown)
    },
    [cleanHtml, escapeHashInText]
  )

  return { convertToMarkdown }
}
