import { Suspense } from "react"

import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import { Header } from "features/mainLayout/components/Header/Header"
import { PrintSubjectLabel } from "features/mainLayout/components/PrintSubjectLabel/PrintSubjectLabel"
import { PowerMenu } from "features/power-menu/PowerMenu"
import { PrivateRoutes } from "routes/PrivateRoutes"

import { GlobalDataWithNonNullableActor } from "./GlobalDataWithNonNullableActor"
import styles from "./MainLayout.module.css"

type MainLayoutProps = {
  globalData: GlobalDataWithNonNullableActor
}

export default function MainLayout({
  globalData: { actor, config },
}: MainLayoutProps) {
  return (
    <>
      <Header actor={actor} config={config} />
      <ErrorBoundary>
        <Suspense>
          <main id="main-content" className={styles.main}>
            <PrivateRoutes actor={actor} config={config} />
          </main>
        </Suspense>
      </ErrorBoundary>
      <PowerMenu config={config} />
      <PrintSubjectLabel />
    </>
  )
}
