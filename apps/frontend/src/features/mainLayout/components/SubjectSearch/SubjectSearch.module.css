.wrapper {
  min-width: 400px;
  border: 0;
  padding: 16px;
  height: fit-content;
  max-height: 80vh;
  color: var(--color-text);
  box-shadow:
    0px 0px 4px rgba(13, 16, 57, 0.1),
    0px 0px 20px rgba(13, 16, 57, 0.2);
}

.loadingSpinner {
  width: 100%;
  display: flex;
  justify-content: center;
  color: var(--color-text);
}

.noResults {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
  padding: 16px;
}

.wrapper:has(.searchTip) .noResults {
  padding-bottom: 8px;
}

.noResults > svg {
  height: 80px;
}

.heading {
  color: var(--color-text);
}

.infoText {
  margin-top: 8px;
}

.searchTip {
  text-align: center;
  max-width: 75%;
  margin: 0 auto;
  margin-top: 8px;
}

.wrapper h2:not(:first-of-type) {
  margin-top: 16px;
}

.comboboxWrapper {
  position: relative;
  display: inline-block;
}

.input.input {
  margin: 0;
  margin-bottom: 16px;
}

.loadingIcon {
  position: absolute;
  right: 16px;
  top: 25%;
  transform: translateY(-50%);
}

.subjectCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 8px;
  padding: 16px;
  margin: 0 -16px;
  border-radius: 0;

  &:hover,
  &[data-active-item] {
    background-color: var(--color-background-hover);
  }
}

.externalSubjectCard {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 8px;
  padding: 16px;
  margin: 0 -16px;
  align-items: center;
}

.subjectLink {
  color: var(--color-text);
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 16px;
  margin: -16px;
}

.subjectLink:only-child {
  min-width: 100%;
  width: -webkit-fill-available;
  width: -moz-available;
}

.subjectInfoSection {
  color: inherit;
}

.subjectInfoContainer {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.subjectInfo {
  display: flex;
  gap: 8px;
  align-items: center;
}

.openJournalButton {
  margin-left: auto;
  visibility: hidden;
}

.subjectCard[data-active-item] .openJournalButton,
.subjectCard:hover .openJournalButton {
  visibility: visible;
}

.addSubjectButton {
  height: fit-content;
  width: fit-content;
}

.selectSubjectButton {
  cursor: pointer;
  width: 100%;
}

.selectSubject {
  color: var(--color-blue-gray-violet-dark);
}
.tooltip {
  display: flex;
  align-items: stretch;
}

@media (max-width: 1600px) {
  .selectSubject {
    font-size: 14px;
  }
}

.ruler {
  margin: 16px -16px 4px;
}

.ruler::after {
  background-color: var(--color-lev-blue-200);
}
