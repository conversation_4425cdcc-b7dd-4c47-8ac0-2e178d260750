import { PopoverProvider } from "@ariakit/react"
import c from "classnames"
import { matchSorter } from "match-sorter"
import { MouseEvent, useEffect, useState } from "react"
import { useHotkeys } from "react-hotkeys-hook"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  Link,
  matchPath,
  useLocation,
  useNavigate,
} from "react-router-dom"
import { useDebounce } from "use-debounce"

import ComputerIllustration from "@leviosa/assets/illustrations/computer.svg?react"
import { isPersonaIdValid } from "@leviosa/utils"

import {
  Combobox,
  ComboboxItem,
  ComboboxProvider,
  Popover,
  PopoverDisclosure,
  usePopoverStore,
} from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useComboboxStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { SubjectInfoSection } from "components/SubjectDetailsCard/SubjectDetailsCard"
import usePermissions from "features/authentication/hooks/usePermissions"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { usePowerMenu } from "features/power-menu/PowerMenu.context"
import { replaceHotKeyChar } from "features/power-menu/components/PowerMenuItem/replaceHotkeyChar"
import modifier from "features/power-menu/lib/hotkeys/hotkeyModifier"
import { PrivateRoutes, RouteStrings } from "routes/RouteStrings"
import { color } from "styles/colors"
import { Button, ButtonText, Heading, Text, TextWithIcon } from "ui"

import {
  PermissionKey,
  useSearchForSubjectsQuery,
  SubjectInteractionSelectionFragmentFragment,
  GenderId,
  useCreateSubjectInteractionMutation,
  useCreateSubjectFromExternalRegistryMutation,
  LeviosaKindId,
} from "generated/graphql"

import headerStyles from "../Header/Header.module.css"
import SubjectAdded from "./SubjectAdded"
import styles from "./SubjectSearch.module.css"

export type ExternalRegistryResult = {
  name: string
  personaId: string
  age: string
  gender: GenderId
}

const isFirstSixCharsNumbers = (str: string): boolean => {
  const firstSixChars = str.substring(0, 6)
  return /^\d{6}$/.test(firstSixChars)
}

type NoSearchResultsProps = {
  personaId: string
  showAddSubjectButton: boolean
}
const NoSearchResults = ({
  personaId,
  showAddSubjectButton,
}: NoSearchResultsProps) => {
  const { t } = useTranslation()

  return (
    <div className={styles.noResults}>
      <ComputerIllustration />
      <Heading>{t("No results found")}</Heading>
      {showAddSubjectButton && (
        <Button
          variant="filled"
          as={Link}
          to={{
            pathname: RouteStrings.registerSubject,
          }}
          state={{ personaId }}
          replace
        >
          {t("+ Add new subject")}
        </Button>
      )}
    </div>
  )
}

type SubjectsListProps = {
  subjects: SubjectInteractionSelectionFragmentFragment["subject"][]
  closePopover: () => void
  heading: string
}
const SubjectsList = ({
  subjects,
  closePopover,
  heading,
}: SubjectsListProps) => {
  const { t } = useTranslation()

  if (subjects.length === 0) return null

  return (
    <>
      <Heading size="xsmall" className={styles.heading}>
        {t(heading)}
      </Heading>
      {subjects.map((s) => (
        <SubjectComboboxItem
          key={s.id}
          subject={s}
          closePopover={closePopover}
        />
      ))}
    </>
  )
}

type ExternalRegistryResultsProps = {
  externalRegistryResults: ExternalRegistryResult
  loading: boolean
  addSubjectFromExternalRegistry: (personaId: string) => void
}
const ExternalRegistryResults = ({
  externalRegistryResults,
  addSubjectFromExternalRegistry,
  loading,
}: ExternalRegistryResultsProps) => {
  const { t } = useTranslation()

  return (
    <>
      <Panel>
        <TextWithIcon size="small">{t("noSubjectsMatchCriteria")}</TextWithIcon>
      </Panel>
      <hr className={styles.ruler} />
      <Heading size="xsmall" className={styles.heading}>
        {t("National Registry")}
      </Heading>
      <div className={styles.externalSubjectCard}>
        <div className={styles.subjectInfoContainer}>
          <div className={styles.subjectInfo}>
            <PiiSensitive as={ButtonText}>
              {externalRegistryResults.name}
            </PiiSensitive>
          </div>

          <SubjectInfoSection
            age={externalRegistryResults.age}
            gender={externalRegistryResults.gender}
            personaId={externalRegistryResults.personaId}
            phoneNumber={null}
            sectionCn={styles.subjectInfoSection}
          />
        </div>
        <Button
          size="small"
          variant="filled"
          onClick={() =>
            addSubjectFromExternalRegistry(externalRegistryResults.personaId)
          }
          className={styles.addSubjectButton}
          icon={loading ? <Icon name="loader-4-line" spin /> : null}
        >
          {t("Add subject")}
        </Button>
      </div>
    </>
  )
}

const ExternalRegistryInformation = () => {
  const { t } = useTranslation()

  return (
    <>
      <hr className={styles.ruler} />
      <Heading size="xsmall" className={styles.heading}>
        {t("National Registry")}
      </Heading>
      <Text size="small" className={styles.infoText}>
        {t("enterValidPersonaIdToSearchExternalRegistry")}
      </Text>
    </>
  )
}

type SubjectComboboxItemProps = {
  subject: SubjectInteractionSelectionFragmentFragment["subject"]
  closePopover: () => void
}
const SubjectComboboxItem = ({
  subject,
  closePopover,
}: SubjectComboboxItemProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { pathname } = useLocation()

  const { hasPermission } = usePermissions()
  const [setSelectedSubject] = useCreateSubjectInteractionMutation()

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const sjLink = generatePath(RouteStrings.subjectJournal, {
    subjectId: subject.id,
  })

  const handleSelectSubject = () => {
    const isSJPage = pathname.startsWith("/subject")
    const isSubjectEditPage = matchPath(
      { path: PrivateRoutes.subjectEdit },
      pathname
    )

    if (isSubjectEditPage) {
      navigate(
        generatePath(RouteStrings.subjectEdit, {
          subjectId: subject.id,
        })
      )
    } else if (canViewSubjectJournal && isSJPage) {
      navigate(sjLink)
    } else {
      setSelectedSubject({
        variables: { subjectId: subject.id },
      })
    }

    closePopover()
  }

  const handleOpenJournal = (e: MouseEvent<HTMLAnchorElement>) => {
    e.stopPropagation()
    closePopover()
  }

  return (
    <ComboboxItem
      className={styles.subjectCard}
      onClick={handleSelectSubject}
      onKeyDown={(e: React.KeyboardEvent<HTMLDivElement>) => {
        if (e.target instanceof HTMLAnchorElement) {
          return
        }

        if (e.key === "Enter" && e.shiftKey && canViewSubjectJournal) {
          navigate(sjLink)
        } else if (e.key === "Enter") {
          handleSelectSubject()
        }
      }}
    >
      <div className={styles.subjectLink}>
        <PiiSensitive as={ButtonText}>{subject.name}</PiiSensitive>

        <SubjectInfoSection
          age={subject.age}
          gender={subject.gender}
          personaId={subject.personaId}
          phoneNumber={subject.phoneNumber}
          sectionCn={styles.subjectInfoSection}
        />
      </div>

      {canViewSubjectJournal && (
        <Button
          as={Link}
          size="small"
          to={sjLink}
          onMouseDown={(e: MouseEvent) => {
            // Prevents the combobox input from regaining focus and scrolling when
            // the button is clicked after scrolling.
            // Necessary for the navigation to always work (DEV-4884)
            e.preventDefault()
          }}
          onClick={handleOpenJournal}
          className={styles.openJournalButton}
          data-testid="open-subject-journal-button"
        >
          {t("Open journal")}
        </Button>
      )}
    </ComboboxItem>
  )
}

type SubjectSearchProps = {
  actor: GlobalDataWithNonNullableActor["actor"]
  anchorRef: React.RefObject<HTMLDivElement>
  noSubjectSelected: boolean
}

const SubjectSearch = ({
  actor,
  anchorRef,
  noSubjectSelected,
}: SubjectSearchProps) => {
  const { t } = useTranslation()
  const navigate = useNavigate()
  const { hasPermission } = usePermissions()

  const { isOpen } = usePowerMenu()

  const {
    globalData: {
      config: { leviosaKindId },
    },
  } = useGlobalState()

  const [
    addingSubjectFromExternalRegistry,
    setAddingSubjectFromExternalRegistry,
  ] = useState(false)

  const [
    subjectAddedFromExternalRegistry,
    setSubjectAddedFromExternalRegistry,
  ] = useState<(ExternalRegistryResult & { id: string }) | null>(null)

  const [error, setError] = useState<string | null>(null)

  const comboboxStore = useComboboxStore({
    resetValueOnHide: true,
  })

  const popoverStore = usePopoverStore({
    placement: "bottom-end",
    setOpen: (open) => {
      comboboxStore.setActiveId("")
      comboboxStore.setValue("")
      setAddingSubjectFromExternalRegistry(false)
      setSubjectAddedFromExternalRegistry(null)
      setError(null)
      popoverStore.setOpen(open)
    },
  })

  const { value: comboboxValue } = comboboxStore.useState()

  const [debouncedSearchValue] = useDebounce(comboboxValue, 200)

  const [setSelectedSubject] = useCreateSubjectInteractionMutation()

  const [createSubjectFromExternalRegistry] =
    useCreateSubjectFromExternalRegistryMutation()

  useHotkeys(
    `${modifier}+shift+k`,
    () => {
      if (isOpen) {
        return
      }

      popoverStore.setOpen((open) => !open)
    },
    {
      enableOnTags: ["TEXTAREA", "INPUT", "SELECT"],
      enableOnContentEditable: true,
    }
  )

  const {
    recentSubjectInteractions,
    organisation: {
      config: { enableNationalRegistry },
    },
  } = actor

  const recentSubjectsList = recentSubjectInteractions.map(
    ({ subject }) => subject
  )
  const [filteredRecentSubjects, setFilteredRecentSubjects] = useState<
    SubjectInteractionSelectionFragmentFragment["subject"][]
  >([])

  // Filter recentSubjectInteractions first
  useEffect(() => {
    if (addingSubjectFromExternalRegistry || subjectAddedFromExternalRegistry)
      return
    if (debouncedSearchValue) {
      const subjects = matchSorter(
        recentSubjectInteractions.map(({ subject }) => subject),
        debouncedSearchValue,
        {
          keys: ["name", "personaId"],
        }
      )
      setFilteredRecentSubjects(subjects.length > 0 ? subjects : [])
    } else {
      setFilteredRecentSubjects([])
    }
  }, [debouncedSearchValue, recentSubjectInteractions])

  const {
    data: subjectsData,
    previousData: previousSubjectsData,
    loading,
  } = useSearchForSubjectsQuery({
    variables: { searchQuery: debouncedSearchValue },
    fetchPolicy: "network-only",
  })

  useEffect(() => {
    if (!anchorRef?.current) return
    popoverStore.setAnchorElement(anchorRef.current)
  }, [popoverStore])

  const queriedSubjects =
    subjectsData?.searchForSubjects.subjects ||
    previousSubjectsData?.searchForSubjects.subjects ||
    []

  // Filter out subjects that are already in the recent subjects list
  const filteredQueriedSubjects = queriedSubjects.filter((subject) => {
    return !filteredRecentSubjects.some((recentSubject) => {
      return recentSubject.id === subject.id
    })
  })

  const externalRegistryResults =
    subjectsData?.searchForSubjects?.externalRegistryResult || null

  const closePopover = () => {
    popoverStore.hide()
    comboboxStore.setActiveId("")
    comboboxStore.setValue("")
    setAddingSubjectFromExternalRegistry(false)
    setSubjectAddedFromExternalRegistry(null)
    setError(null)
  }

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const addSubjectFromExternalRegistry = (personaId: string) => {
    setAddingSubjectFromExternalRegistry(true)

    createSubjectFromExternalRegistry({
      variables: {
        personaId,
      },
      onCompleted: (data) => {
        const subjectId = data.createSubjectFromExternalRegistry.id

        const navigationLink = generatePath(RouteStrings.subjectJournal, {
          subjectId: subjectId,
        })

        // We want to show the loading spinner for a bit before adding the subject for improved UX
        setTimeout(() => {
          setSubjectAddedFromExternalRegistry(
            data.createSubjectFromExternalRegistry
          )
          // If the user does not have permission to view the subject journal, we need to set the selected subject manually
          if (!canViewSubjectJournal) {
            setSelectedSubject({
              variables: { subjectId },
            })
          }
          setAddingSubjectFromExternalRegistry(false)

          if (canViewSubjectJournal) {
            navigate(navigationLink)
          }
        }, 1500)
      },
      onError: () => {
        setAddingSubjectFromExternalRegistry(false)
        closePopover()
        setError(t("couldNotAddSubjectFromExternalRegistry"))
      },
    })
  }

  const noSearchResults =
    filteredQueriedSubjects.length === 0 &&
    filteredRecentSubjects.length === 0 &&
    !loading &&
    debouncedSearchValue.length > 0 &&
    !externalRegistryResults

  const showAddSubjectButton =
    !loading &&
    debouncedSearchValue.replace("-", "").length === 10 &&
    isFirstSixCharsNumbers(debouncedSearchValue) &&
    leviosaKindId !== LeviosaKindId.Lite

  const showExternalRegistryInfo =
    enableNationalRegistry &&
    !isPersonaIdValid(debouncedSearchValue) &&
    !subjectAddedFromExternalRegistry &&
    debouncedSearchValue &&
    !externalRegistryResults

  const showPersonaIdSearchTip =
    !enableNationalRegistry &&
    !showAddSubjectButton &&
    !subjectAddedFromExternalRegistry &&
    debouncedSearchValue

  return (
    <PopoverProvider store={popoverStore}>
      {noSubjectSelected && (
        <button
          className={styles.selectSubjectButton}
          onClick={() => popoverStore.setOpen(!popoverStore.getState().open)}
        >
          <Text className={styles.selectSubject} size="large">
            {t("Select subject")}
          </Text>
        </button>
      )}
      <Tooltip
        className={styles.tooltip}
        tooltipContent={replaceHotKeyChar(`shift+${modifier}+K`)}
        placement="bottom"
        timeout={500}
      >
        <PopoverDisclosure
          className={headerStyles.dropdownButton}
          variant="clear"
          data-testid="subject-search-button"
          store={{ ...popoverStore, setAnchorElement: () => null }}
        >
          <Icon name="arrow-down-s-line" fontSize={24} />
        </PopoverDisclosure>
      </Tooltip>

      <Popover
        className={c(styles.wrapper, color.light)}
        onClose={() => {
          comboboxStore.setActiveId("")
          comboboxStore.setValue("")
        }}
        sameWidth
        portal
      >
        <ComboboxProvider store={comboboxStore}>
          <div className={styles.comboboxWrapper}>
            {!subjectAddedFromExternalRegistry && (
              <Combobox
                className={styles.input}
                placeholder={t("Search by name or ID")}
                autoSelect
              />
            )}
            {loading && (
              <Icon name="loader-4-line" className={styles.loadingIcon} spin />
            )}
          </div>
          {noSearchResults && (
            <NoSearchResults
              personaId={debouncedSearchValue}
              showAddSubjectButton={showAddSubjectButton}
            />
          )}
          {!subjectAddedFromExternalRegistry && (
            <SubjectsList
              subjects={
                debouncedSearchValue
                  ? filteredRecentSubjects
                  : recentSubjectsList
              }
              closePopover={closePopover}
              heading={t("Recently viewed subjects")}
            />
          )}
          <SubjectsList
            subjects={filteredQueriedSubjects}
            closePopover={closePopover}
            heading={t("Search results")}
          />
          {showExternalRegistryInfo && <ExternalRegistryInformation />}
          {externalRegistryResults && !subjectAddedFromExternalRegistry && (
            <ExternalRegistryResults
              externalRegistryResults={externalRegistryResults}
              loading={addingSubjectFromExternalRegistry}
              addSubjectFromExternalRegistry={addSubjectFromExternalRegistry}
            />
          )}
          {showPersonaIdSearchTip && (
            <Text size="small" className={styles.searchTip}>
              {t("enterValidPersonaIdForMoreAccurateResults")}
            </Text>
          )}
          {subjectAddedFromExternalRegistry && (
            <SubjectAdded
              subject={subjectAddedFromExternalRegistry}
              closePopover={closePopover}
            />
          )}
          {error && (
            <Panel status="error">
              <Text size="small">{error}</Text>
            </Panel>
          )}
        </ComboboxProvider>
      </Popover>
    </PopoverProvider>
  )
}

export default SubjectSearch
