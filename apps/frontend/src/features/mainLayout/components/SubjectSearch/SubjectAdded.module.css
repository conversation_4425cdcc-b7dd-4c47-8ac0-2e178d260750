.subjectAddedContainer {
  display: flex;
  gap: 16px;
  flex-direction: column;
  padding-bottom: 8px;
}

.subjectCard {
  display: flex;
  gap: 8px;
  flex-direction: column;
  border-radius: 0;
}

.subjectInfo {
  display: flex;
  gap: 8px;
  align-items: center;
}

.infoText {
  color: var(--color-blue-gray-violet-dark);
}

.buttonsContainer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}
