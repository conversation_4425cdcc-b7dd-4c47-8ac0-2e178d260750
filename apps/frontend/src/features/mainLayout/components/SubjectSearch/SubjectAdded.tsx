import { FormEvent } from "react"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import { genderIconNames } from "components/Icon/GenderIcon"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import {
  Input,
  TextWithIcon,
  Text,
  PhoneNumberInput,
  FormGrid,
  Button,
  Heading,
} from "ui"
import parseAgeText from "utils/parseAgeText"

import { useUpdateSubjectMutation } from "generated/graphql"

import styles from "./SubjectAdded.module.css"
import { ExternalRegistryResult } from "./SubjectSearch"

export type SubjectAddedProps = {
  subject: ExternalRegistryResult & {
    id: string
  }
  closePopover: () => void
}

const SubjectAdded = ({ subject, closePopover }: SubjectAddedProps) => {
  const { t } = useTranslation()
  const [updateSubject] = useUpdateSubjectMutation()

  const ageString = parseAgeText(subject.age)

  const personaIdFormatted = formatPersonaId(subject.personaId)

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.target as HTMLFormElement)

    const { email, phoneNumber } = Object.fromEntries(formData.entries())

    updateSubject({
      variables: {
        id: subject.id,
        input: {
          email: {
            set: email.toString() || null,
          },
          phoneNumber: {
            set: phoneNumber.toString() || null,
          },
        },
      },
      onCompleted: () => {
        closePopover()
      },
    })
  }

  return (
    <div className={styles.subjectAddedContainer}>
      <Panel status="success" variant="no-border">
        <TextWithIcon size="small" status="success">
          {t("subjectHasBeenAdded")}
        </TextWithIcon>
      </Panel>
      <div className={styles.subjectCard}>
        <PiiSensitive as={Heading}>{subject.name}</PiiSensitive>
        <div className={styles.subjectInfo}>
          <PiiSensitive as={Text} size="small">
            {personaIdFormatted}
          </PiiSensitive>
          <Icon name={genderIconNames[subject.gender]} fontSize={14} />
          <Text size="small">{t(...ageString)}</Text>
        </div>
      </div>

      <Text size="small" className={styles.infoText}>
        {t("pleaseFillInContactInfoForCommunications")}
      </Text>

      <FormGrid onSubmit={handleSubmit}>
        <PhoneNumberInput label="Phone number" name="phoneNumber" />
        <Input label={t("Email")} type="email" name="email" />
        <div className={styles.buttonsContainer}>
          <Button variant="filled" type="submit">
            {t("doSave")}
          </Button>
        </div>
      </FormGrid>
    </div>
  )
}

export default SubjectAdded
