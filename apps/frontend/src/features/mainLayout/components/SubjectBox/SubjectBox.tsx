import c from "classnames"
import { useRef } from "react"
import { generatePath, Link, useLocation } from "react-router-dom"

import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { SubjectInfoSection } from "components/SubjectDetailsCard/SubjectDetailsCard"
import usePermissions from "features/authentication/hooks/usePermissions"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import SubjectSummaryButton from "features/dashboard/components/SubjectSummaryButton/SubjectSummaryButton"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { RouteStrings } from "routes/RouteStrings"
import { Heading } from "ui"

import { ClinicalCodingCriticalType, PermissionKey } from "generated/graphql"

import headerStyles from "../Header/Header.module.css"
import SubjectSearch from "../SubjectSearch/SubjectSearch"
import styles from "./SubjectBox.module.css"

type SubjectBoxProps = {
  actor: GlobalDataWithNonNullableActor["actor"]
}

export const SubjectBox = ({ actor }: SubjectBoxProps) => {
  const { hasPermission } = usePermissions()
  const { pathname } = useLocation()
  const getPathObject = useGetCalendarPathObject()

  const wrapperRef = useRef<HTMLDivElement>(null)

  const { selectedSubjects } = actor

  const selectedSubject =
    selectedSubjects.length === 0 ? null : selectedSubjects[0].subject

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)
  // When the current user doesn't have access to the Subject Journal we should redirect to the calendar
  const subjectLink =
    selectedSubject && canViewSubjectJournal
      ? generatePath(RouteStrings.subjectJournal, {
          subjectId: selectedSubject.id,
        })
      : getPathObject(RouteStrings.calendar)

  return (
    <>
      <div
        className={c(headerStyles.boxCommon, {
          [headerStyles.active]: pathname.startsWith("/subject"),
        })}
        ref={wrapperRef}
      >
        {selectedSubject && (
          <Link
            to={subjectLink}
            className={c(styles.header, headerStyles.boxLink)}
          >
            <PiiSensitive
              as={Heading}
              data-testid="subject-name"
              className={c(headerStyles.boxTitle, styles.selectedSubject)}
            >
              {selectedSubject.name}
            </PiiSensitive>

            <SubjectInfoSection {...selectedSubject} />
          </Link>
        )}
        <div className={styles.subjectButtons}>
          {selectedSubject && (
            <SubjectSummaryButton
              {...selectedSubject}
              hasCriticalCodes={
                selectedSubject.clinicalCodings.filter(
                  ({ criticalType, closedAt }) =>
                    criticalType !== ClinicalCodingCriticalType.NotCritical &&
                    !closedAt
                ).length > 0
              }
            />
          )}
        </div>
        <SubjectSearch
          actor={actor}
          anchorRef={wrapperRef}
          noSubjectSelected={!selectedSubject}
        />
      </div>
    </>
  )
}
