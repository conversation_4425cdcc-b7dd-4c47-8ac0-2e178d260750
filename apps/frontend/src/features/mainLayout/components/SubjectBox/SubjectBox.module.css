.header {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
  justify-content: center;
  overflow: hidden;
}

.selectedSubject {
  line-break: auto;
  line-clamp: 2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.subjectButtons {
  display: flex;
  gap: 16px;
  align-items: center;
}

.createButton {
  width: 100%;
}

.createNewSubjectButton {
  font-size: 24px;
}

.dropDownButtonIcon {
  font-size: 24px;
}
