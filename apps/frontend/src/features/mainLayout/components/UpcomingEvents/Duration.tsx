import { useState } from "react"
import { useTranslation } from "react-i18next"

import { useInterval } from "@leviosa/utils"

import { Text } from "ui"
import { getTimeAgo } from "utils/timeUtils"

type DurationProps = {
  date: string
  className?: string
}

const thirtySecondsInMilliseconds = 30000

export const Duration = ({ date, className = "" }: DurationProps) => {
  const { t } = useTranslation()
  const [timeAgo, setTimeAgo] = useState(getTimeAgo(date, t))

  useInterval(() => {
    setTimeAgo(getTimeAgo(date, t))
  }, thirtySecondsInMilliseconds)

  return (
    <Text size="small" secondary className={className}>
      {timeAgo}
    </Text>
  )
}
