import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"

import { PermissionKey, ProviderBoxQuery } from "generated/graphql"

import UpcomingEvent from "./UpcomingEvent"
import styles from "./UpcomingEvents.module.css"

type ProviderEventBadgeViewProps = {
  upcomingEvents: ProviderBoxQuery["events"][number][]
}

export default function UpcomingEvents({
  upcomingEvents,
}: ProviderEventBadgeViewProps) {
  const getCalendarPath = useGetCalendarPathObject()
  const { globalData } = useGlobalState()
  const { actor } = globalData
  const { t } = useTranslation()

  const nextTwoEvents = upcomingEvents.length
    ? upcomingEvents.slice(0, 2)
    : undefined

  return (
    <ul className={styles.list}>
      {/* Restricted b/c LITE does not have Calendar */}
      <Restricted to={PermissionKey.CalendarView} fallback={<></>}>
        {nextTwoEvents && nextTwoEvents.length > 0 ? (
          nextTwoEvents.map((event) => (
            <UpcomingEvent key={event.id} event={event} />
          ))
        ) : (
          <Link
            to={getCalendarPath(RouteStrings.calendar, {
              search: { provider: actor.id },
            })}
          >
            {t("No upcoming appointments")}
          </Link>
        )}
      </Restricted>
    </ul>
  )
}
