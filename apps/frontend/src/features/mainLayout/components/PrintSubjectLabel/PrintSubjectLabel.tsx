import { useRef, useEffect } from "react"
import { useTranslation } from "react-i18next"

import { formatPersonaId } from "@leviosa/utils"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { InlineGuidePanel } from "components/InlineGuide/InlineGuidePanel"
import { usePrintSubjectLabel } from "features/mainLayout/components/PrintSubjectLabel/PrintSubjectLabelContext"
import { usePersistedPreference } from "hooks/usePersistedPreference"
import { logException } from "lib/sentry/sentry"
import { Button, ButtonText, Input, Modal, notification, Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"

import styles from "./PrintSubjectLabel.module.css"

export const PrintSubjectLabel = () => {
  const { t: tFeatures } = useTranslation("features", {
    keyPrefix: "mainLayout.PrintSubjectLabel",
  })

  const dateFormat = useDateFormatter("is")
  const printContentRef = useRef<HTMLDivElement>(null)
  const { showPrintSubjectLabel, setShowPrintSubjectLabel } =
    usePrintSubjectLabel()

  const [registrationNumber, setRegistrationNumber] =
    usePersistedPreference<string>({
      key: "providerRegistrationNumber",
    })

  const { globalData } = useGlobalState()

  const actor = globalData.actor
  const selectedSubject =
    actor.selectedSubjects.length === 1 ? actor.selectedSubjects[0] : null

  const printLabel = () => {
    if (!printContentRef.current) return

    // Create a hidden iframe
    const iframe = document.createElement("iframe")
    iframe.style.position = "fixed"
    iframe.style.right = "0"
    iframe.style.bottom = "0"
    iframe.style.width = "0"
    iframe.style.height = "0"
    iframe.style.border = "0"
    document.body.appendChild(iframe)

    // Get the HTML content without inline styles
    const contentDiv = printContentRef.current.cloneNode(true) as HTMLDivElement

    // We'll replace class names with a simple ID for styling via CSS
    contentDiv.removeAttribute("class")
    contentDiv.id = "subjectLabel"

    // Style text elements with classes instead of inline styles
    const buttonTexts = contentDiv.querySelectorAll(
      "button-text, div[class*='ButtonText']"
    )
    buttonTexts.forEach((element) => {
      const el = element as HTMLElement
      el.className = "button-text"
    })

    const texts = contentDiv.querySelectorAll("text, div[class*='Text']")
    texts.forEach((element) => {
      const el = element as HTMLElement
      el.className = "text"
    })

    const htmlContent = contentDiv.outerHTML

    // Write to the iframe
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document
    if (!iframeDoc) {
      document.body.removeChild(iframe)
      return
    }

    iframeDoc.open()
    iframeDoc.write(`
      <html>
        <head>
          <title>Subject Label</title>
          <style>
            body {
              margin: 0;
              padding: 0;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }
            
            #subjectLabel {
              width: 89mm;
              height: 36mm;
              padding: 24px;
              display: flex;
              flex-direction: column;
              justify-content: center;
              box-sizing: border-box;
              font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            }
            
            .button-text {
              font-weight: 600;
              font-size: 14px;
              margin-bottom: 2px;
            }
            
            .text {
              font-size: 14px;
              margin-bottom: 2px;
            }
            
            /* Print-specific styles */
            @page {
              size: 89mm 36mm;
              margin: 0;
            }
            
            @media print {
              html, body {
                width: 89mm;
                height: 36mm;
                overflow: hidden;
              }
              
              #subjectLabel {
                width: 89mm !important;
                height: 36mm !important;
                page-break-inside: avoid;
              }
            }
          </style>
        </head>
        <body>
          ${htmlContent}
        </body>
      </html>
    `)
    iframeDoc.close()

    // Add onload event to print after content is loaded
    iframe.onload = () => {
      try {
        iframe.contentWindow?.focus()
        iframe.contentWindow?.print()

        // Remove iframe after printing (or after a delay if print was canceled)
        setTimeout(() => {
          if (iframe.parentNode) {
            document.body.removeChild(iframe)
          }
        }, 1000)
      } catch (error) {
        logException(error)
        notification.create({
          message: tFeatures("somethingWentWrong"),
          status: "error",
        })
        document.body.removeChild(iframe)
      }
    }
  }

  // Print automatically when conditions are met
  useEffect(() => {
    // Only run when the modal is shown, we have a registration number and a subject
    if (showPrintSubjectLabel && registrationNumber && selectedSubject) {
      // Small delay to ensure the content ref is mounted
      const timer = setTimeout(() => {
        printLabel()
        // Close the modal after printing
        setTimeout(() => {
          setShowPrintSubjectLabel(false)
        }, 500)
      }, 100)

      return () => clearTimeout(timer)
    }
    return
  }, [
    showPrintSubjectLabel,
    registrationNumber,
    selectedSubject,
    setShowPrintSubjectLabel,
  ])

  // Early return - must be after all hooks
  if (!selectedSubject || !showPrintSubjectLabel) return null

  const physiciansNumber = actor.doctorNumber
  const subjectName = selectedSubject.subject.name
  const subjectPersonaId = formatPersonaId(selectedSubject.subject.personaId)

  const dateNow = dateFormat(new Date(), {
    dateStyle: "short",
  })

  const handleSubmit = (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    const formData = new FormData(event.currentTarget)
    const regNumber = formData.get("registrationNumber") as string
    setRegistrationNumber(regNumber)
  }

  if (!registrationNumber) {
    return (
      <Modal
        isOpen
        onClose={() => setShowPrintSubjectLabel(false)}
        title={tFeatures("registrationNumber")}
        contentClassName={styles.modal}
      >
        <form onSubmit={handleSubmit} className={styles.modalContent}>
          <InlineGuidePanel
            content={tFeatures("pleaseEnterRegistrationNumberBelow")}
          />
          <Input
            label={tFeatures("registrationNumber")}
            name="registrationNumber"
            required
          />
          <div className={styles.modalFooter}>
            <Button
              onClick={() => setShowPrintSubjectLabel(false)}
              variant="clear"
            >
              {tFeatures("cancel")}
            </Button>
            <Button type="submit" variant="filled">
              {tFeatures("print")}
            </Button>
          </div>
        </form>
      </Modal>
    )
  }

  return (
    <>
      {/* Hidden div containing print content */}
      <div className={styles.hiddenContent}>
        <div ref={printContentRef} className={styles.subjectLabel}>
          <ButtonText>{subjectName}</ButtonText>
          <ButtonText>{subjectPersonaId}</ButtonText>
          <Text>{dateNow}</Text>
          {physiciansNumber && (
            <Text>{`${tFeatures("physiciansNumber")}: ${physiciansNumber}`}</Text>
          )}
          {registrationNumber && <ButtonText>{registrationNumber}</ButtonText>}
        </div>
      </div>
    </>
  )
}
