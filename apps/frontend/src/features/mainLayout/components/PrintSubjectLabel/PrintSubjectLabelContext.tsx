import React, {
  createContext,
  useContext,
  useState,
  ReactNode,
  useCallback,
} from "react"

type PrintSubjectLabelContextType = {
  showPrintSubjectLabel: boolean
  setShowPrintSubjectLabel: (show: boolean) => void
}

const PrintSubjectLabelContext = createContext<
  PrintSubjectLabelContextType | undefined
>(undefined)

export const PrintSubjectLabelProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [showPrintSubjectLabel, setShowPrintSubjectLabel] = useState(false)

  const setShowPrintSubjectLabelCallback = useCallback((show: boolean) => {
    setShowPrintSubjectLabel(show)
  }, [])

  return (
    <PrintSubjectLabelContext.Provider
      value={{
        showPrintSubjectLabel,
        setShowPrintSubjectLabel: setShowPrintSubjectLabelCallback,
      }}
    >
      {children}
    </PrintSubjectLabelContext.Provider>
  )
}

export const usePrintSubjectLabel = () => {
  const context = useContext(PrintSubjectLabelContext)
  if (context === undefined) {
    throw new Error(
      "usePrintSubjectLabel must be used within a PrintSubjectLabelProvider"
    )
  }
  return context
}
