query ProviderBox($id: UUID!, $inputFilter: EventInstancesFilterInput!) {
  provider: user(id: $id) {
    id
    journalEntries(status: DRAFT) {
      id
    }
  }
  listItems(
    filter: {
      assigneeIds: { excludeNullValues: true, values: [$id] }
      handled: false
    }
  ) {
    totalCount
  }

  # Also gets total count of events for today
  events: eventInstances(inputFilter: $inputFilter) {
    id
    fromDate
    toDate
    title
    serviceType {
      id
      name
      color
    }
    participants {
      ... on ParticipantSubject {
        objId
        participantId
        attendanceState {
          id
          state
          date
        }
        subject {
          name
        }
      }
    }
  }
}
