.wrapper {
  grid-column: span 4/ -1;
  display: grid;
  grid-template-columns: 1fr max-content max-content auto;
  gap: 16px;
  align-items: center;
}

.actionButton {
  padding: 3px 6px;
}

.powerMenuButton {
  cursor: pointer;
  width: 48px;

  &:hover {
    background-color: transparent;
  }
}

.providerBadgeInfo {
  display: flex;
  justify-content: end;
}

.providerBoxButton {
  padding: 8px 12px;
}

/* Select the button inside the provider box */
.providerBoxButton > span {
  height: 24px;
  width: 24px;
}

.invoiceOverviewButton {
  margin-left: auto;
  margin-right: -16px;

  align-self: center;
  font-size: 24px;
  line-height: 1;
}

@media (max-width: 1600px) {
  .wrapper {
    grid-column: span 5 / -1;
  }

  .logo {
    grid-column: span 1;
  }
}
