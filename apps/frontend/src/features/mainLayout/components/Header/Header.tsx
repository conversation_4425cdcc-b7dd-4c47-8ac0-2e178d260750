import { useEffect, useState } from "react"
import { Link, useLocation } from "react-router-dom"

import LeviosaLogoA from "@leviosa/assets/leviosa/LogoA.svg?react"
import LeviosaLogoB from "@leviosa/assets/leviosa/LogoB.svg?react"
import LeviosaLogoC from "@leviosa/assets/leviosa/LogoC.svg?react"

import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { RouteStrings } from "routes/RouteStrings"
import { Grid } from "ui"

import { DepartmentTeamBox } from "../DepartmentTeamBox/DepartmentTeamBox"
import { ProviderBox } from "../ProviderBox/ProviderBox"
import { SubjectBox } from "../SubjectBox/SubjectBox"
import styles from "./Header.module.css"

type HeaderProps = {
  actor: GlobalDataWithNonNullableActor["actor"]
  config: GlobalDataWithNonNullableActor["config"]
}

const Logos = [LeviosaLogoA, LeviosaLogoB, LeviosaLogoC]
const getRandomLogoIndex = () => Math.floor(Math.random() * Logos.length)

export const Header = ({ actor, config }: HeaderProps) => {
  const [currentLogoIndex, setCurrentLogoIndex] = useState(getRandomLogoIndex())

  const location = useLocation()
  const { teamsAndDepartments } = actor

  useEffect(() => {
    setCurrentLogoIndex(getRandomLogoIndex())
  }, [location.pathname])

  const RandomLogo = Logos[currentLogoIndex]

  return (
    <Grid as="header" className={styles.wrapper}>
      <Link to={RouteStrings.home} className={styles.logo}>
        <RandomLogo />
      </Link>
      <SubjectBox actor={actor} />
      <DepartmentTeamBox teamsAndDepartments={teamsAndDepartments} />

      <ProviderBox actor={actor} />
    </Grid>
  )
}
