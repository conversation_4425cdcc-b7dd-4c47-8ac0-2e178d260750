fragment LanguageSelectionFragment on Language {
  id
  name
  ietfTag
  isTranslationSupported
}

query GlobalData {
  config {
    leviosaKindId
    build
    appVersion
    shortDateFormat
    longDateFormat
    nnMode
    personaIdentifierKind {
      rxValidator
      label
    }
    uiLanguage {
      ...LanguageSelectionFragment
    }
    contentLanguage {
      ...LanguageSelectionFragment
    }
  }

  actor {
    id
    ...ProviderInfoFragment
    doctorNumber
    ...PermissionsFragment
    selectedSubjects: subjectInteractions(limit: 1, groupSubjects: true) {
      ...SubjectInteractionSelectionFragment
    }
    recentSubjectInteractions: subjectInteractions(
      limit: 15
      groupSubjects: true
    ) {
      ...SubjectInteractionSelectionFragment
    }
    # for PowerMenu & DepartmentBox, wants Provider's Teams not all from Org!
    teamsAndDepartments: teams {
      id
      name
      description
      serviceType
      department {
        id
        name
      }
    }
    lastSubjectInteraction {
      id
      subject {
        id
        name
        personaId
      }
    }
    selectedDepartment {
      id
    }
    externalEhrId
    activationStatus
    snippets {
      id
      snippetType
      languageId
      matchText
      replaceText
    }
    organisation {
      id
      name
      config {
        enableNationalRegistry
      }
    }
  }
}
