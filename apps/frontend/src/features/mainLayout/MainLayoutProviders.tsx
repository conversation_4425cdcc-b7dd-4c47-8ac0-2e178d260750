import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"
import LocalStorageProvider from "components/LocalStorageProvider/LocalStorageProvider"
import { useAuth } from "features/authentication/AuthProvider"
import { useDetectIdle } from "features/authentication/hooks/useDetectIdle"
import { PowerMenuProvider } from "features/power-menu/PowerMenu.context"
import { Loading } from "ui"

import {
  GlobalDataQuery,
  PermissionKey,
  useGlobalDataQuery,
} from "generated/graphql"

import { GlobalDataWithNonNullableActor } from "./GlobalDataWithNonNullableActor"
import MainLayout from "./MainLayout"
import { PrintSubjectLabelProvider } from "./components/PrintSubjectLabel/PrintSubjectLabelContext"

function useEnsureActorIsNotNull(
  data?: GlobalDataQuery | null
): asserts data is GlobalDataWithNonNullableActor | null {
  const { deauthenticate } = useAuth()
  if (data?.actor === null) {
    deauthenticate()
  }
}

const RootPrivate = () => {
  const { data, loading, error } = useGlobalDataQuery()
  // Some users are allowed to persist login
  const canPersistLogin = !!data?.actor?.permissions.includes(
    PermissionKey.AuthenticationPersistLogin
  )
  const { deauthenticate } = useAuth()

  useEnsureActorIsNotNull(data)

  useDetectIdle(canPersistLogin ? undefined : deauthenticate)

  if (!loading && error) deauthenticate()
  if (!data && loading) return <Loading large white />
  if (!data) return null

  // Compute currentTeamId
  const currentTeamId =
    data.actor.teamsAndDepartments.length > 0
      ? data.actor.teamsAndDepartments[0].id
      : // COMEBACK HACK waiting for BE fix in DEV-2097 which will allow fetching ID from Provider.config
        ""

  return (
    <GlobalProvider globalData={data} currentTeamId={currentTeamId}>
      <LocalStorageProvider>
        <PowerMenuProvider>
          <PrintSubjectLabelProvider>
            <MainLayout globalData={data} />
          </PrintSubjectLabelProvider>
        </PowerMenuProvider>
      </LocalStorageProvider>
    </GlobalProvider>
  )
}

export default RootPrivate
