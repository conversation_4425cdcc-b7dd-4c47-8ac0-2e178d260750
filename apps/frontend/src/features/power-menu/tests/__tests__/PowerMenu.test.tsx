/* eslint-disable @typescript-eslint/no-empty-function */
import "@testing-library/jest-dom"

// import { act, fireEvent, Render, screen } from "test/Render"

// import { PowerMenuGroup } from "../../lib/types"
// import { PowerMenuTestWrapper } from "../PowerMenuTestWrapper/PowerMenuTestWrapper"
// import {
//   getGroupOfChildItems,
//   getGroupOfParentItems,
//   testGroups,
// } from "../lib/powerMenuData"

// const openPowerMenu = async (newGroups?: PowerMenuGroup[]) => {
//   await act(() => {
//     Render(<PowerMenuTestWrapper newGroups={newGroups} />)
//   })

//   await act(async () => {
//     fireEvent.click(screen.getByText("Open Power Menu"))
//   })
// }

// COMEBACK fix ci. Ariakit updated to v1.0.0 and it breaks the build yarn 3 when running yarn test
// Error message is Cannot find module '@ariakit/react/composite' from '/Users/<USER>/Developer/newleviosa/apps/frontend/src/features/power-menu

describe("Power Menu ", () => {
  test("Open Power Menu ", async () => {
    // await openPowerMenu()
    // const powerMenuContent = screen.getByTestId("power-menu")
    // expect(powerMenuContent).toBeVisible()
  })
})

// describe("Power Menu", () => {
//   test("Open Power Menu", async () => {
//     // await openPowerMenu()

//     // const powerMenuContent = screen.getByTestId("power-menu")

//     // expect(powerMenuContent).toBeVisible()
//   })

//   test("Close Power Menu", async () => {
//     await openPowerMenu()

//     await act(() => fireEvent.click(screen.getByLabelText("Close")))

//     const powerMenuContent = screen.queryByTestId("power-menu")

//     expect(powerMenuContent).toBeNull()
//   })

//   test("Input focused when PowerMenu opened", async () => {
//     await openPowerMenu()

//     const searchInput = screen.getByTestId("search-input")

//     expect(searchInput).toHaveFocus()
//   })

//   test("PowerMenu groups rendered properly", async () => {
//     await openPowerMenu()

//     const powerMenuGroups = screen.getAllByTestId("power-menu-group")

//     expect(powerMenuGroups).toHaveLength(testGroups.length)
//   })

//   test("PowerMenu items rendered properly", async () => {
//     await openPowerMenu()

//     const powerMenuGroups = screen.getAllByTestId("power-menu-item")

//     const items = testGroups.map((group) => group.items).flat()

//     expect(powerMenuGroups).toHaveLength(items.length)
//   })

//   test("Change focus from input to first option (arrow down)", async () => {
//     await openPowerMenu()

//     await act(async () => {
//       const searchInput = screen.getByTestId("search-input")

//       fireEvent.keyDown(searchInput, { key: "ArrowDown", keyCode: 40 })
//     })

//     const firstItem = screen.getAllByTestId("power-menu-item")[0]

//     expect(firstItem).toHaveFocus()
//   })

//   test("Change focus from input to first option (arrow up)", async () => {
//     await openPowerMenu()

//     await act(async () => {
//       const searchInput = screen.getByTestId("search-input")

//       fireEvent.keyDown(searchInput, { key: "ArrowUp", keyCode: 38 })
//     })

//     const items = screen.getAllByTestId("power-menu-item")

//     expect(items[items.length - 1]).toHaveFocus()
//   })

//   test("Change focus from input with hover navigation ", async () => {
//     await openPowerMenu()

//     await act(async () => {
//       const firstItem = screen.getAllByTestId("power-menu-item")[0]

//       await fireEvent.mouseOver(firstItem)
//     })

//     const firstItem = screen.getAllByTestId("power-menu-item")[0]

//     expect(firstItem).toHaveFocus()
//   })

//   test("Filter Power Menu Items", async () => {
//     const title = "FilterElement"

//     const group = [
//       {
//         id: "Group",
//         title: "Group",
//         items: [
//           {
//             id: title,
//             title,
//             execute: () => {},
//           },
//         ],
//       },
//     ]

//     await openPowerMenu(group)

//     await act(async () => {
//       const searchInput = screen.getByTestId("search-input")
//       fireEvent.change(searchInput, { target: { value: title } })
//     })

//     const items = screen.getAllByTestId("power-menu-item")

//     expect(items).toHaveLength(1)
//   })

//   test("Execute function on item on mouse click", async () => {
//     const handleClick = jest.fn()

//     const group = getGroupOfParentItems(handleClick)

//     await openPowerMenu(group)

//     await act(async () => {
//       const items = screen.getAllByTestId("power-menu-item")
//       fireEvent.click(items[0])
//     })

//     expect(handleClick).toHaveBeenCalled()
//   })

//   test("Execute function on item with enter keyboard click", async () => {
//     const handleClick = jest.fn()

//     const group = getGroupOfParentItems(handleClick)

//     await openPowerMenu(group)

//     await act(async () => {
//       const items = screen.getAllByTestId("power-menu-item")

//       fireEvent.keyDown(items[0], { key: "Enter", keyCode: 13 })
//     })

//     expect(handleClick).toHaveBeenCalled()
//   })

//   test("Execute function on item on mouse click", async () => {
//     const handleClick = jest.fn()

//     const group = getGroupOfParentItems(handleClick)

//     await openPowerMenu(group)

//     await act(async () => {
//       const items = screen.getAllByTestId("power-menu-item")
//       fireEvent.click(items[0])
//     })

//     expect(handleClick).toHaveBeenCalled()
//   })

//   test("Execute function on mouse click on child item", async () => {
//     const handleClick = jest.fn()

//     const group = getGroupOfChildItems(handleClick)

//     await openPowerMenu(group)

//     await act(async () => {
//       const firstItem = await screen.getAllByTestId("power-menu-item")[0]

//       fireEvent.click(firstItem)
//     })

//     await act(async () => {
//       const powerMenuSubItem = await screen.findByTestId("power-menu-item")
//       await fireEvent.click(powerMenuSubItem)
//     })

//     expect(handleClick).toHaveBeenCalled()
//   })

//   const backFromChildToParent = async () => {
//     const group = getGroupOfChildItems()

//     await openPowerMenu(group)

//     await act(async () => {
//       const firstItem = await screen.getAllByTestId("power-menu-item")[0]

//       fireEvent.click(firstItem)
//     })

//     await act(async () => {
//       const searchInput = screen.getByTestId("search-input")

//       fireEvent.change(searchInput, { target: { value: "Itm" } })

//       fireEvent.keyDown(searchInput, { key: "ArrowLeft", keyCode: 37 })
//     })
//   }

//   test("Check if input has focus when executing parent item and returning back from child to parent", async () => {
//     await backFromChildToParent()

//     const searchInput = screen.getByTestId("search-input")

//     expect(searchInput).toHaveFocus()
//   })

//   test("Check if input has value of executed item when executing parent item and returning back from child to parent", async () => {
//     await backFromChildToParent()

//     const searchInput = screen.getByTestId("search-input")

//     expect(searchInput).toHaveValue("Itm")
//   })
// })
