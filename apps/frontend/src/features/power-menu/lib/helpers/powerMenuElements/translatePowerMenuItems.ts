import { TFunction } from "i18next"

import { PowerMenuGroup } from "../../types"

export const translatePowerMenuItems = (
  initialPowerMenuGroups: PowerMenuGroup[],
  t: TFunction
): PowerMenuGroup[] => {
  return initialPowerMenuGroups.map((group) => {
    const title = group.title ? t(group.title) : ""

    const items = group.items.map((item) => ({
      ...item,
      title: t(item.title),
      description: item.description ? t(item.description) : "",
    }))

    return {
      ...group,
      title,
      items,
    }
  })
}
