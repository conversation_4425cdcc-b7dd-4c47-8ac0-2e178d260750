import { useMemo } from "react"

import { PowerMenuGroup } from "../../types"
import { extractArgument } from "../argument/extractArgument"
import { fuzzySearchPowerMenu } from "../argument/fuzzySearchPowerMenu"

// Fuzzy search doesn't search nested  objects as we want (if one of items matched it returns whole object).
// We reorder all items with fuzzy search. After we group items according to their group.
// We apply sort by score that returned by fuzzy search to reorder items in group correctly.
// We apply fuzzy search for second time to reorder groups.
export const useFilterPowerMenuItems = (
  groups: PowerMenuGroup[],
  inputValue: string
) => {
  // Flatten groups so we have an array of items to search through. Add the
  // group id so it'll be easier to reconstruct the groups.
  const items = groups
    .map((group) => group.items.map((item) => ({ ...item, groupId: group.id })))
    .flat()

  const filteredItems = useMemo(() => {
    const filteredElements = fuzzySearchPowerMenu(
      items,
      extractArgument(inputValue).argument
    )

    return filteredElements
  }, [items, inputValue])

  const groupsMap: Map<PowerMenuGroup["id"], PowerMenuGroup> = new Map()

  for (const item of filteredItems) {
    const groupId = item.groupId
    if (!groupsMap.has(groupId)) {
      const oldGroup = groups.find((g) => g.id === groupId)
      if (!oldGroup) break

      const newGroup = {
        ...oldGroup,
        items: [item],
      }
      groupsMap.set(groupId, newGroup)
    } else {
      const group = groupsMap.get(groupId)
      group?.items.push(item)
    }
  }
  const filteredGroups = Array.from(groupsMap.values())

  return filteredGroups
}
