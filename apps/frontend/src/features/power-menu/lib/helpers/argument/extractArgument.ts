const rxBetweenQuotes = new RegExp(`^".*"$`)
const rxOpenedQuote = new RegExp(`^".*`)

// Detect weather argument between quotes
export const extractArgument = (str: string) => {
  if (rxBetweenQuotes.test(str)) {
    return {
      isArgument: true,
      argument: str.substr(1, str.length - 2),
    }
  } else if (rxOpenedQuote.test(str)) {
    return {
      isArgument: false,
      argument: str.substr(1, str.length - 1),
    }
  }

  return {
    isArgument: true,
    argument: str,
  }
}
