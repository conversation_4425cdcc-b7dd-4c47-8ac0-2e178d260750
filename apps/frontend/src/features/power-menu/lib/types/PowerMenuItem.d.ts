import Fuse from "fuse.js"

import { PermissionKey } from "generated/graphql"

import { ArgumentOnExecute } from "./ArgumentOnExecute.type"
import { PowerMenuGroup } from "./PowerMenuGroup"

export interface PowerMenuItem {
  /**
   * The unique identifier for the item.
   * If the ID comes from the backend, it must be a unique name.
   */
  id: string

  /**
   * The title displayed in the menu.
   * This will later be translated using i18next.
   */
  title: string

  /**
   * A short description of the commands.
   * This field is optional.
   */
  description?: string

  /**
   * The global shortcut keyboard combination for the item.
   * This field is optional.
   */
  shortcutCombination?: string

  /**
   * Indicates whether the item has a submenu.
   * This field is optional.
   */
  hasSubMenu?: boolean

  // This shouldn't be here. It's for storing the
  // result of the fuzzy search to highlight the matching part of the title
  fuseResult?: Fuse.FuseResult<PowerMenuItem>

  /**
   * The list of permissions required to display this item.
   * If not provided, the item is always displayed.
   * If multiple permissions are provided, the user must have all of them.
   * Note: Only one of the two fields should be used.
   * @see usePermission
   */
  restrictedTo?: PermissionKey | PermissionKey[]

  /**
   * The list of permissions required to display this item.
   * Should be displayed if the user has at least one of the permissions.
   * Note: Only one of the two fields should be used.
   */
  restrictedToAny?: PermissionKey[]


  //  callback to execute command
  execute: (
    arg?: Partial<ArgumentOnExecute>
  ) => void | Promise<PowerMenuGroup[]>

  //We store previous groups started from argument 1 in order to not lose data
  previousGroups?: PowerMenuGroup[]
}
