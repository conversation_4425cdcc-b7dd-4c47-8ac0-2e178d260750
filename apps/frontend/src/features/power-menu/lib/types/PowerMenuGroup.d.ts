import { PowerMenuGroupKey } from "../enums/PowerMenuGroupKeys.enum"
import { PermissionKey } from "generated/graphql"
import { PowerMenuItem } from "./PowerMenuItem"

export interface PowerMenuGroup {
  id: keyof typeof PowerMenuGroupKey | string
  title?: string

  // can not be filtered | default filtrable value
  filtrable?: boolean

  // index of groups in power menu
  positionAtTop?: boolean
  items: PowerMenuItem[]

  /**
   * The list of permissions required to display this item.
   * If not provided, the item is always displayed.
   * If multiple permissions are provided, the user must have all of them.
   * Note: Only one of the two fields should be used.
   * @see usePermission
   */
    restrictedTo?: PermissionKey | PermissionKey[]

    /**
     * The list of permissions required to display this item.
     * Should be displayed if the user has at least one of the permissions.
     * Note: Only one of the two fields should be used.
     */
    restrictedToAny?: PermissionKey[]
}
