export enum PowerMenuSubMenuGroup {
  docSession = "docSession",
  subContextMenu = "subContextMenu",
  queryString = "queryString",
}

export enum MenuGroup {
  // header = "header",
  moderator = "moderator",
  provider = "provider",
  subject = "subject",
  subjectJournal = "subjectJournal",
  // intervalPeriod = "intervalPeriod",
  // documentationSession = "documentationSession",
  // documentationSessionGeneric = "documentationSessionGeneric",
  // documentationSessionItemGeneric = "documentationSessionItemGeneric",
  // tasks = "tasks",
  templates = "templates",
  teams = "teams",
  departments = "departments",
  contextMenu = "contextMenu",
  users = "users",
  calendar = "calendar",
  invoice = "invoice",
  locations = "locations",
  scheduling = "scheduling",
}

export const PowerMenuGroupKey = { ...MenuGroup, ...PowerMenuSubMenuGroup }
