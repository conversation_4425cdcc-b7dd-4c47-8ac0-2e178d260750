.scrollableWrapper {
  grid-column: 2;
  grid-row: 2;
  align-self: start;
  /* background: var(--color-800); */
  border-radius: 12px;
  display: grid;
  grid-template-rows: max-content 1fr max-content;
  overflow: hidden;
  width: min(90vw, 500px);
  max-height: 100%;
  box-shadow:
    0px 4px 10px rgba(0, 0, 0, 0.1),
    0px 20px 50px rgba(0, 0, 0, 0.3);
}

.buttonPrev {
  align-self: stretch;
  margin-left: -8px;
  border-radius: 8px;
}

.emptyResult {
  text-align: center;
}

.footerWrapper {
  color: var(--color-200);
  padding: 8px 16px;
  border-top: 1px solid var(--color-700);
  font-size: 14px;
}

.highlight {
  color: var(--color-800);
  flex: 1 1 auto;
  opacity: 0.85;
  white-space: nowrap;
}

.highlight::first-letter {
  text-transform: uppercase;
}

.highlight::after {
  content: " /";
}

.iconSearch {
  font-size: 20px;
}

.input {
  border: none;
  flex: 1 1 100%;
  padding: 9px 16px 9px 0;
}
.input:focus {
  outline: none;
}

.inputBlock {
  align-items: center;
  background-color: var(--color-white);
  border-radius: 8px;
  display: flex;
  gap: 8px;
  margin: 16px;
  padding: 0 8px;
  color: var(--color-initial-text);
}

.inputBlock:focus-within {
  box-shadow: 0 0 0 4px var(--color-500);
}

.list {
  color: var(--color-white);
  overflow-x: hidden;
  overflow-y: auto;
}

.scrollBar::-webkit-scrollbar {
  border-radius: 0 4px 4px 0;
  background-color: var(--color-800);
  width: 6px;
}

.scrollBar::-webkit-scrollbar-track {
  border-radius: 4px;
  padding-top: 3px;
  margin-top: 3px;
}

.scrollBar::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: var(--color-200);
  border: 1px solid var(--color-300);
}

.spinWrapper {
  align-items: center;
  display: flex;
  height: 80%;
  justify-content: center;
}
