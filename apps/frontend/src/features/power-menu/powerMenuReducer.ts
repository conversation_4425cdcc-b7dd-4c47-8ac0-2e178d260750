/* eslint-disable @typescript-eslint/no-empty-function */
import { uniqBy } from "lodash"
/* eslint-disable @typescript-eslint/no-empty-function */
import { Dispatch } from "react"

import { logException } from "lib/sentry/sentry"

import { isPromise } from "./isPromise"
import { PowerMenuItem, PowerMenuGroup } from "./lib/types"

export type PowerMenuStateType = {
  isOpen: boolean
  powerMenuGroups: PowerMenuGroup[]
  initialPowerMenuGroups: PowerMenuGroup[]
  inputValue: string
  args: { executedItem: PowerMenuItem; searchString: string }[]
  isLoading: boolean
}

export type Action =
  | ReturnType<typeof openAction>
  | ReturnType<typeof closeAction>
  | ReturnType<typeof addGroupAction>
  | ReturnType<typeof removeGroupAction>
  | ReturnType<typeof changeInputAction>
  | ReturnType<typeof setLoadingAction>
  | ReturnType<typeof setPowerMenuGroupsAction>

export const openAction = () => ({ type: "open" as const })

export const closeAction = () => ({ type: "close" as const })

export const addGroupAction = (powerMenuGroups: PowerMenuGroup[]) => ({
  type: "addPowerMenuGroups" as const,
  payload: powerMenuGroups,
})

export const removeGroupAction = (powerMenuGroups: PowerMenuGroup[]) => ({
  type: "removePowerMenuGroups" as const,
  payload: powerMenuGroups,
})

export const setLoadingAction = (isLoading: boolean) => ({
  type: "setLoading" as const,
  payload: isLoading,
})

export const changeInputAction = (
  inputValue: string,
  args?: { executedItem: PowerMenuItem; searchString: string }[]
) => ({
  type: "changeInputValues" as const,
  payload: { inputValue, args },
})

export const setPowerMenuGroupsAction = (
  powerMenuGroups: PowerMenuGroup[]
) => ({
  type: "setPowerMenuGroups" as const,
  payload: powerMenuGroups,
})

export const selectItemAction = async (
  currentOption: PowerMenuItem,
  powerMenu: PowerMenuStateType,
  dispatch: Dispatch<Action>
) => {
  const { inputValue } = powerMenu

  const items = currentOption.execute()
  if (!currentOption.hasSubMenu) {
    dispatch(closeAction())

    return
  }

  dispatch(
    changeInputAction("", [
      ...powerMenu.args,
      { executedItem: currentOption, searchString: inputValue },
    ])
  )

  if (items && isPromise(items)) {
    dispatch(setLoadingAction(true))
    dispatch(setPowerMenuGroupsAction(await items))
  }
}

export const setPrevArgumentAction = (
  callback: () => void,
  powerMenu: PowerMenuStateType,
  dispatch: Dispatch<Action>
) => {
  const { args, inputValue, initialPowerMenuGroups } = powerMenu

  if (args.length === 0) return

  const searchString = args[args.length - 1].searchString

  if (inputValue.length === 0) {
    dispatch(changeInputAction(searchString, args.slice(0, args.length - 1)))

    // Make sure that the state has been updated before moving the focus
    // Otherwise cursor will not be positioned end of inputValue
    setTimeout(callback, 0)
  }

  if (args.length === 1)
    return dispatch(setPowerMenuGroupsAction(initialPowerMenuGroups))

  const previousGroups = args[args.length - 1].executedItem.previousGroups

  //From second argument we should store previousGroups to easily to return  previous group state
  if (!previousGroups) {
    const error = new Error("Previous Groups should be defined in childItems")
    logException(error)
    throw error
  }

  dispatch(setPowerMenuGroupsAction(previousGroups))
}

const addPowerMenuGroups = (
  draftGroups: PowerMenuGroup[],
  actionGroups: PowerMenuGroup[]
) => {
  actionGroups.forEach((actionGroup) => {
    const index = draftGroups.findIndex((el) => el.id === actionGroup.id)

    if (index !== -1) {
      const uniqueItems = uniqBy(
        [...draftGroups[index].items, ...actionGroup.items],
        "id"
      )

      draftGroups[index] = actionGroup
      draftGroups[index].items = uniqueItems

      return
    }

    draftGroups.unshift(actionGroup)
  })
}

const removePowerMenuGroups = (
  draftGroups: PowerMenuGroup[],
  actionGroups: PowerMenuGroup[]
) => {
  actionGroups.forEach((actionGroup) => {
    const index = draftGroups.findIndex((el) => el.id === actionGroup.id)

    if (index === -1) return

    const actionItemIds = actionGroup.items.map((el) => el.id)

    draftGroups[index].items = draftGroups[index].items.filter(
      (el) => !actionItemIds.includes(el.id)
    )
  })
}

const resetPowerMenuState = (draft: PowerMenuStateType) => {
  draft.powerMenuGroups = draft.initialPowerMenuGroups
  draft.isOpen = false
  draft.args = []
  draft.inputValue = ""
}

export const reducer = (draft: PowerMenuStateType, action: Action) => {
  switch (action.type) {
    case "open":
      resetPowerMenuState(draft)
      draft.isOpen = true
      break

    case "close":
      draft.isOpen = false
      break

    case "addPowerMenuGroups":
      addPowerMenuGroups(draft.initialPowerMenuGroups, action.payload)
      break

    case "removePowerMenuGroups":
      removePowerMenuGroups(draft.initialPowerMenuGroups, action.payload)
      break

    case "changeInputValues":
      draft.inputValue = action.payload.inputValue
      draft.args = action.payload.args ?? draft.args
      break

    case "setLoading":
      draft.isLoading = action.payload
      break

    case "setPowerMenuGroups":
      draft.powerMenuGroups = action.payload
      draft.isLoading = false

      break
    default:
      break
  }
}
