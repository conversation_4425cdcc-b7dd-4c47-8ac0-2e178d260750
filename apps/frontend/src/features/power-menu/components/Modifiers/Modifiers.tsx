import { useTranslation } from "react-i18next"

import { Checkbox } from "ui/index"

import { Tabbable } from "../../lib/enums/Tabbable.enum"
import { modifiersTable } from "../../lib/helpers/modifiersTable/modifiersTable"
import { Modifier } from "../../lib/types/Modifier.type"

type Props = {
  onChange: (modifiers: Partial<Modifier>) => void
  id?: string
}

export const Modifiers = ({ id, onChange }: Props) => {
  const modifiers = modifiersTable[id as Tabbable]
  const { t } = useTranslation()

  if (!modifiers) return null

  const { selfIsOwner, favorite, favoriteModelGlobal, recentlyOpened } =
    modifiers

  return (
    <>
      <Checkbox
        label={t("selfIsOwner")}
        checked={selfIsOwner === true}
        onChange={({ currentTarget: { checked } }) =>
          onChange({ selfIsOwner: checked })
        }
      />
      <Checkbox
        label={t("favorite")}
        checked={favorite === true}
        onChange={({ currentTarget: { checked } }) =>
          onChange({ favorite: checked })
        }
      />
      <Checkbox
        label={t("favoriteModelsGlobal")}
        checked={favoriteModelGlobal === true}
        onChange={({ currentTarget: { checked } }) =>
          onChange({ favoriteModelGlobal: checked })
        }
      />
      <Checkbox
        label={t("recentlyOpened")}
        checked={recentlyOpened === true}
        onChange={({ currentTarget: { checked } }) =>
          onChange({ recentlyOpened: checked })
        }
      />
    </>
  )
}
