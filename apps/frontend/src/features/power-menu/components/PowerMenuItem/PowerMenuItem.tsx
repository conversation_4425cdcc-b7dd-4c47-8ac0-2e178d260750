import { CompositeItem, CompositeStore } from "@ariakit/react"
import { KeyboardEvent } from "react"

import Icon from "components/Icon/Icon"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { Text } from "ui"

import { PowerMenuItem as PowerMenuItemType } from "../../lib/types"
import { HighlightWords } from "../HighlightWords/HighlightWords"
import styles from "./PowerMenuItem.module.css"
import { replaceHotKeyChar } from "./replaceHotkeyChar"

type Props = {
  composite: CompositeStore
  onItemClick: (item: PowerMenuItemType) => void
  item: PowerMenuItemType
  onKeyDown: (
    e: KeyboardEvent<HTMLButtonElement>,
    item: PowerMenuItemType
  ) => void
}

export const PowerMenuItem = ({
  onKeyDown,
  composite,
  onItemClick,
  item,
}: Props) => {
  const {
    id,
    title,
    hasSubMenu,
    description,
    shortcutCombination,
    fuseResult,
    restrictedTo,
    restrictedToAny,
  } = item

  const { activeId } = composite.useState()
  const restrictedProps = restrictedToAny
    ? { toAny: restrictedToAny }
    : // if neither restrictedTo nor restrictedToAny is provided, we want to restrict to nothing
      // Empty array is used to restrict to nothing
      { to: restrictedTo || [] }

  return (
    <Restricted {...restrictedProps}>
      <li data-is-selected={id === activeId} className={styles.listItem}>
        <CompositeItem
          data-testid="power-menu-item"
          render={<button />}
          className={styles.button}
          aria-keyshortcuts={
            shortcutCombination
              ? replaceHotKeyChar(shortcutCombination)
              : undefined
          }
          onKeyDown={(e: KeyboardEvent<HTMLButtonElement>) =>
            onKeyDown(e, item)
          }
          id={id}
          onClick={() => onItemClick(item)}
          onMouseEnter={() => composite.move(id)}
        >
          <Text className={styles.title}>
            <HighlightWords
              title={title.charAt(0).toUpperCase() + title.slice(1)}
              Indices={fuseResult?.matches?.[0].indices}
            />
          </Text>
          {hasSubMenu && (
            <Icon className={styles.icon} name={"arrow-right-double-line"} />
          )}
          {description && (
            <span className={styles.description}>{description}</span>
          )}
        </CompositeItem>
      </li>
    </Restricted>
  )
}
