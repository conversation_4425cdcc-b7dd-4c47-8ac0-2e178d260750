import isMac from "utils/os"

export const replaceHotKeyChar = (shortcutCombination: string): string => {
  if (!isMac) return shortcutCombination

  const modifiers = {
    ctrl: "⌃",
    command: "⌘",
    alt: "⌥",
    shift: "⇧",
    option: "⌥",
  }

  for (const [key, value] of Object.entries(modifiers)) {
    const regex = new RegExp(key, "gi")
    shortcutCombination = shortcutCombination
      .replace(regex, value)
      .replaceAll("+", " ")
  }

  return shortcutCombination
}
