.button {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
  padding: 4px 16px 4px 32px;
  text-overflow: ellipsis;
  white-space: break-spaces;
  --color-text: var(--color-200);
}
.button:focus-visible {
  outline: none;
}
.button[aria-keyshortcuts]::after {
  align-items: center;
  bottom: 0;
  content: attr(aria-keyshortcuts);
  display: flex;
  font-size: 14px;
  font-weight: 300;
  opacity: 0.7;
  position: absolute;
  right: 16px;
  top: 0;
}

.title {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.description {
  font-size: 12px;
  font-weight: 300;
  margin-bottom: 4px;
  opacity: 0.7;
  width: 100%;

  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.icon {
  font-size: 20px;
  margin-left: auto;
}

.listItem {
  background-color: transparent;
  display: grid;
  position: relative;
}

.listItem[data-is-selected="true"] {
  background-color: var(--color-main);
}

.listItem:hover,
.listItem:focus {
  background-color: var(--color-main);
}
.listItem:active {
  background-color: var(--color-700);
}
