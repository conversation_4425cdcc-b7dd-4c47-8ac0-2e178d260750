import styles from "./HighlightWords.module.css"

type Props = {
  title: string
  i?: number
  Indices?: readonly [number, number][]
}

//Indices contains of starting match index and ending match index.
export const HighlightWords = ({
  title,
  i = 1,
  Indices,
}: Props): JSX.Element => {
  if (!Indices || Indices.length - i < 0) return <>{title}</>

  const currentMatchIndex = Indices[Indices.length - i]

  return (
    <>
      <HighlightWords
        title={title.substring(0, currentMatchIndex[0])}
        i={i + 1}
        Indices={Indices}
      />
      <mark className={styles.match}>
        {title.substring(currentMatchIndex[0], currentMatchIndex[1] + 1)}
      </mark>
      <span>{title.substring(currentMatchIndex[1] + 1)}</span>
    </>
  )
}
