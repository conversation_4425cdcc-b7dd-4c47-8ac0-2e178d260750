import { ReactNode, useEffect, useId, useRef, MouseEvent } from "react"

import { AnimatedDialog } from "components/AnimateMount/AnimateMount"
import { Portal } from "ui/components/Portal"

import { usePowerMenu } from "../../PowerMenu.context"
import styles from "./PowerMenuModal.module.css"

type PowerMenuModalProps = {
  children: ReactNode
}

export const PowerMenuModal = ({ children }: PowerMenuModalProps) => {
  const { isOpen, close } = usePowerMenu()
  const id = useId()
  const modalInnerRef = useRef<HTMLDialogElement>(null)
  useEffect(() => {
    isOpen && modalInnerRef.current?.showModal()
  }, [isOpen])

  const clickHandler = (e: MouseEvent<HTMLDialogElement>) => {
    if (e.target !== e.currentTarget) return

    close()
  }

  return (
    <Portal id={id}>
      <AnimatedDialog
        onClick={clickHandler}
        onClose={close}
        className={styles.dialog}
        ref={modalInnerRef}
        show={isOpen}
        onAnimationEnd={() => {
          if (!isOpen) modalInnerRef.current?.close()
        }}
        animation="fadeUp"
      >
        {children}
      </AnimatedDialog>
    </Portal>
  )
}
