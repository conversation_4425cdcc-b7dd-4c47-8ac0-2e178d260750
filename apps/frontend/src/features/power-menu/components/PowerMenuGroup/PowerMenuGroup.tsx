import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { PowerMenuGroupKey } from "features/power-menu/lib/enums/PowerMenuGroupKeys.enum"
import { PowerMenuGroup as PowerMenuGroupType } from "features/power-menu/lib/types/PowerMenuGroup"
import { Text } from "ui"

import styles from "./PowerMenuGroup.module.css"

export type PowerMenuGroupProps = {
  children: React.ReactNode
} & Omit<PowerMenuGroupType, "items">

export const PowerMenuGroup = ({
  title,
  children,
  id,
  restrictedTo,
  restrictedToAny,
}: PowerMenuGroupProps) => {
  const isSensitive = id === PowerMenuGroupKey.subject
  const restrictedProps = restrictedToAny
    ? { toAny: restrictedToAny }
    : // if neither restrictedTo nor restrictedToAny is provided, we want to restrict to nothing
      // Empty array is used to restrict to nothing
      { to: restrictedTo || [] }
  return (
    <Restricted {...restrictedProps}>
      <li>
        <PiiSensitive
          as={Text}
          isNnMode={isSensitive ? undefined : false}
          className={styles.title}
          data-testid="power-menu-group"
          data-skip={true}
          size="small"
        >
          {title}
        </PiiSensitive>
        <ul>{children}</ul>
      </li>
    </Restricted>
  )
}
