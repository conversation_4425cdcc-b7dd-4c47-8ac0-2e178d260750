import { Composite } from "@ariakit/react"
import { CompositeItem, useCompositeStore } from "@ariakit/react/composite"
import c from "classnames"
import React, { KeyboardEvent, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { Key } from "ts-keycode-enum"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { useGlobalPowerMenuGroups } from "features/power-menu/ActionGroups/useGlobalPowerMenuItems"
import { usePowerMenuGroups } from "features/power-menu/usePowerMenuGroups"
import { color } from "styles/colors"
import Button from "ui/components/Button/Button"

import { GlobalDataQuery } from "generated/graphql"

import { usePowerMenu } from "./PowerMenu.context"
import styles from "./PowerMenu.module.css"
import { PowerMenuModal } from "./components/ModalMenu/PowerMenuModal"
import { PowerMenuGroup } from "./components/PowerMenuGroup/PowerMenuGroup"
import { PowerMenuItem } from "./components/PowerMenuItem/PowerMenuItem"
import { PowerMenuItem as PowerMenuItemType } from "./lib/types"

export type Config = Pick<
  GlobalDataQuery["config"],
  "leviosaKindId" | "appVersion" | "build"
>

type PowerMenuProps = {
  config: Config
}

const setFocusToEndOfInput = (
  title: string | null,
  input?: HTMLInputElement | null
) => {
  if (title === null) return

  input?.setSelectionRange(title.length, title.length)
}

export const PowerMenu = ({ config }: PowerMenuProps) => {
  const { globalData } = useGlobalState()
  const { actor } = globalData
  const parentPowerMenuGroups = useGlobalPowerMenuGroups(actor)
  const { view } = useCalendarState()

  const {
    isOpen,
    changeInputValues,
    selectItem,
    setPrevArgument,
    inputValue,
    args,
    isLoading,
    filteredGroups,
  } = usePowerMenu()

  usePowerMenuGroups(parentPowerMenuGroups, [
    actor.lastSubjectInteraction?.id,
    window.location,
    isOpen,
    view,
  ])

  const argumentTitle =
    args.length > 0 ? args[args.length - 1].executedItem.title : null

  const { leviosaKindId, appVersion, build } = config

  const { t } = useTranslation()
  const composite = useCompositeStore({
    focusLoop: "vertical",
    orientation: "vertical",
  })

  const { items } = composite.useState()

  const focusInput = () => {
    composite.move(composite.first())
  }

  const parentItem = args.length === 0 ? null : args[0]

  useEffect(() => {
    focusInput()
  }, [isOpen, items, composite.first()])

  const onHandleChange = ({
    target: { value },
  }: React.ChangeEvent<HTMLInputElement>) => changeInputValues(value)

  const onSymbolAdd = (char: string) => changeInputValues(inputValue + char)

  const onSymbolRemove = () =>
    changeInputValues(inputValue.substring(0, inputValue.length - 1))

  const handleInputBackspacePress = () => {
    setPrevArgument(() =>
      setFocusToEndOfInput(argumentTitle, items[0].element as HTMLInputElement)
    )
  }

  const selectPowerMenuItem = async (currentOption: PowerMenuItemType) => {
    await selectItem(currentOption)

    focusInput()
  }

  const setPreviousItems = () => {
    if (parentItem === null) return

    setPrevArgument(() =>
      setFocusToEndOfInput(argumentTitle, items[0].element as HTMLInputElement)
    )
  }

  const onKeyDown = (
    e: KeyboardEvent<HTMLButtonElement>,
    item: PowerMenuItemType
  ) => {
    switch (e.keyCode) {
      case Key.Backspace:
        setTimeout(focusInput, 0)
        onSymbolRemove()

        handleInputBackspacePress()

        return
      case Key.RightArrow:
        return selectPowerMenuItem(item)
      case Key.Enter:
        selectPowerMenuItem(item)
        e.preventDefault()
        return

      case Key.LeftArrow:
        setPreviousItems()

        focusInput()

        return
    }

    if (e.key.length !== 1) return

    onSymbolAdd(e.key)

    focusInput()
    e.preventDefault()
    return
  }

  const onKeyDownInput = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.keyCode === Key.Backspace && inputValue === "") {
      e.preventDefault()

      return setPreviousItems()
    }
    if (e.keyCode === Key.LeftArrow) return setPreviousItems()
    if (e.keyCode === Key.DownArrow) composite.next()
    if (e.keyCode === Key.UpArrow) composite.previous()
  }

  const onItemClick = (item: PowerMenuItemType) => selectPowerMenuItem(item)

  return (
    <PowerMenuModal>
      <Composite
        data-testid="power-menu"
        store={composite}
        aria-label="Power Menu"
        className={c(styles.scrollableWrapper, styles.scrollBar, color.dark)}
      >
        <div onClick={focusInput} className={styles.inputBlock}>
          {parentItem ? (
            <Button
              onClick={() => {
                if (parentItem === null) return

                setPrevArgument(() =>
                  setFocusToEndOfInput(
                    argumentTitle,
                    items[0].element as HTMLInputElement
                  )
                )
              }}
              className={styles.buttonPrev}
              variant="clear"
              icon={<Icon name="arrow-left-double-line" />}
            />
          ) : (
            <Icon className={styles.iconSearch} name="search-line" />
          )}
          {args.length > 0 && (
            <span className={styles.highlight}>
              {args[args.length - 1].executedItem.title}
            </span>
          )}
          <CompositeItem
            as="input"
            className={styles.input}
            onKeyDown={(e: KeyboardEvent<HTMLInputElement>) => {
              onKeyDownInput(e)
            }}
            placeholder={t("Search or jump to")}
            value={inputValue}
            onChange={onHandleChange}
            data-testid="search-input"
            autoComplete="off"
          />
        </div>
        {/* hide modifiers till full functionality is added */}
        {/* {parentItem && (
        <Modifiers
          id={parentItem?.id}
          // eslint-disable-next-line no-console
          onChange={(value) => console.log(value)}
        />
          )} */}
        <ul className={c(styles.list, styles.scrollBar)}>
          {isLoading && (
            <div className={styles.spinWrapper} data-testid="power-menu-loader">
              <Icon name="loader-4-line" spin />
            </div>
          )}

          {!isLoading &&
            filteredGroups.map(({ items, ...rest }) => (
              <PowerMenuGroup key={rest.id} {...rest}>
                {items.map((item) => (
                  <PowerMenuItem
                    key={item.id}
                    onKeyDown={onKeyDown}
                    composite={composite}
                    onItemClick={onItemClick}
                    item={item}
                  />
                ))}
              </PowerMenuGroup>
            ))}

          {filteredGroups.length === 0 && !isLoading && (
            <div className={styles.emptyResult}>
              {t("routes:powerMenu.noMatchingCommands")}
            </div>
          )}
        </ul>
        <div className={styles.footerWrapper}>
          {leviosaKindId === "LITE" ? "LeviosaLite" : "Leviosa EHR"} | Version{" "}
          {appVersion} | Build ({build})
        </div>
      </Composite>
    </PowerMenuModal>
  )
}
