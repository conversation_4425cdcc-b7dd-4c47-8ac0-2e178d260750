# Component specific usage

In order to register group of power menu elements on specific route we need to register power menu group with elements.
![image](https://user-images.githubusercontent.com/31973508/184069575-f970dc11-8d36-4d36-a7fe-4274f194709a.png)

# Route specific usage

in `ActionGroups` folder power menu groups route specific. `RootSubjectJournal` -> PowerMenu elements for specific for RootSubjectJournal routes. `RootTemplate` -> PowerMenu elements for RootTemplate routes.
![image](https://user-images.githubusercontent.com/31973508/184070394-5a6df99d-1839-4798-8ce9-099452ea914d.png)

# Usage

Use `Component specific` method if you whant some information that needs to be passed to power Menu Element. e.x: modal.
Use `Route Specific` method if you whant to register for specific route and you don't have any dependencies with Compoent. e.x api request.
