import { useLocation } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import usePermissions from "features/authentication/hooks/usePermissions"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"

import { PermissionKey } from "generated/graphql"

import { PowerMenuGroup } from "../lib/types"
import { useCalendarGroup } from "./RootTemplate/useCalendarGroup"
import { useDepartmentTeamGroup } from "./RootTemplate/useDepartmentTeamGroup"
import { useInvoiceGroup } from "./RootTemplate/useInvoiceGroup"
import { useModeratorGroup } from "./RootTemplate/useModeratorGroup"
import { useProviderGroup } from "./RootTemplate/useProviderGroup"
import { useSchedulingGroup } from "./RootTemplate/useSchedulingGroup"
import { useSubjectMenuItemsGroup } from "./RootTemplate/useSubjectMenuItemsGroup"

export const useGlobalPowerMenuGroups = (
  actor: GlobalDataWithNonNullableActor["actor"]
): PowerMenuGroup[] => {
  const { hasPermission } = usePermissions()
  const location = useLocation()

  const {
    globalState: { currentTeamId, isNnMode },
  } = useGlobalState()

  const isCalendarRoute = location.pathname.includes("/calendar")

  const calendarGroups = useCalendarGroup(actor.id)

  const powerMenuItems: PowerMenuGroup[] = [
    useModeratorGroup(),
    useProviderGroup({ actor }),
    useDepartmentTeamGroup({ actor, currentTeamId }),
    useInvoiceGroup(),
    useSchedulingGroup(actor.id),
    useSubjectMenuItemsGroup(actor, isNnMode),
    hasPermission(PermissionKey.CalendarView) && isCalendarRoute
      ? calendarGroups
      : undefined,
  ].filter(Boolean) as PowerMenuGroup[]

  return powerMenuItems
}
