import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { HotKeys } from "features/power-menu/lib/hotkeys"
import { RouteStrings } from "routes/RouteStrings"

import { PermissionKey } from "generated/graphql"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { PowerMenuGroup } from "../../lib/types"

export const useSchedulingGroup = (actorId: string): PowerMenuGroup => {
  const navigate = useNavigate()
  const navigateCalendar = useNavigateCalendar()

  const { t } = useTranslation()

  return {
    id: PowerMenuGroupKey.scheduling,
    title: t("Scheduling"),
    items: [
      {
        id: "Calendar",
        title: t("Calendar"),
        execute: () => navigateCalendar(RouteStrings.calendar),
        shortcutCombination: HotKeys.calendar,
        restrictedTo: PermissionKey.CalendarView,
      },
      {
        id: "AvailabilitySchedule",
        title: t("Availability schedule"),
        execute: () =>
          navigate(
            generatePath(RouteStrings.calendarSchedule, {
              providerId: actorId,
            })
          ),
        restrictedTo: PermissionKey.CalendarView,
      },
      {
        id: "WaitingList",
        title: t("Waiting list"),
        execute: () =>
          navigate(
            generatePath(RouteStrings.waitingList, {
              pageNumber: 1,
            })
          ),
        restrictedTo: PermissionKey.ListsWaitingListView,
      },
    ],
  } satisfies PowerMenuGroup
}
