import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"

import { GlobalState } from "components/GlobalDataContext/GlobalData.context"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { getTeamPathFromServiceType } from "utils/getTeamPathFromServiceType"
import { renderDepartmentAndTeam } from "utils/renderDepartmentAndTeam"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { HotKeys } from "../../lib/hotkeys"
import { PowerMenuGroup } from "../../lib/types"

export const useDepartmentTeamGroup = ({
  actor: { teamsAndDepartments },
  currentTeamId,
}: Pick<GlobalState, "currentTeamId"> &
  Pick<GlobalDataWithNonNullableActor, "actor">): PowerMenuGroup => {
  const navigate = useNavigate()

  const { t } = useTranslation()

  const emptyPowerMenuGroup: PowerMenuGroup = {
    id: "CurrentDepartmentAndTeam",
    title: t("routes:manageTeam.providerNotInAnyTeam"),
    items: [],
  }

  if (!currentTeamId) return emptyPowerMenuGroup

  const currentTeamAndDepartment = teamsAndDepartments.find(
    ({ id }) => id === currentTeamId
  )
  if (!currentTeamAndDepartment) return emptyPowerMenuGroup

  return {
    ...emptyPowerMenuGroup,
    title: renderDepartmentAndTeam(
      currentTeamAndDepartment.name,
      currentTeamAndDepartment.department?.name
    ),
    items: [
      {
        id: "TeamHome",
        title: "Home",
        execute: () =>
          navigate(
            getRecordPagePath(RouteStrings.team, currentTeamAndDepartment.id)
          ),
      },
      ...(currentTeamAndDepartment.serviceType !== null
        ? [
            {
              id: "Dashboard",
              title: "Dashboard",
              shortcutCombination: HotKeys.dashboard,
              execute: () => {
                navigate(
                  getTeamPathFromServiceType(
                    currentTeamAndDepartment.serviceType,
                    currentTeamAndDepartment.id
                  )
                )
              },
            },
          ]
        : []),
      {
        id: PowerMenuGroupKey.teams,
        title: "My Teams",
        hasSubMenu: true,
        execute: async () => {
          const items =
            teamsAndDepartments.map(
              ({ id, name, description, department, serviceType }) => ({
                id,
                title: renderDepartmentAndTeam(name, department?.name),
                description: description ?? undefined,
                execute: () =>
                  navigate(getTeamPathFromServiceType(serviceType, id)),
              })
            ) ?? []

          return [{ id: PowerMenuGroupKey.teams, title: "Teams", items }]
        },
      },
    ],
  }
}
