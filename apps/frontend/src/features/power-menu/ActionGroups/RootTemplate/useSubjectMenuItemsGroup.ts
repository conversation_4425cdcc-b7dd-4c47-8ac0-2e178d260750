import { useNavigate } from "react-router-dom"

import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { usePrintSubjectLabel } from "features/mainLayout/components/PrintSubjectLabel/PrintSubjectLabelContext"
import { HotKeys } from "features/power-menu/lib/hotkeys"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"

import { PermissionKey } from "generated/graphql"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"

export const useSubjectMenuItemsGroup = (
  { lastSubjectInteraction }: GlobalDataWithNonNullableActor["actor"],
  isNnMode: boolean
) => {
  const { setShowPrintSubjectLabel } = usePrintSubjectLabel()

  const navigate = useNavigate()

  return lastSubjectInteraction
    ? {
        id: PowerMenuGroupKey.subject,
        title: !isNnMode ? lastSubjectInteraction.subject.name : "Subject",
        items: [
          {
            id: "Journal",
            title: "Journal",
            execute: () =>
              navigate(
                getRecordPagePath(
                  RouteStrings.subjectJournal,
                  lastSubjectInteraction.subject.id
                )
              ),
            shortcutCombination: HotKeys.journal,
            restrictedToAny: [PermissionKey.SubjectJournalView],
          },
          {
            id: "EditSubject",
            title: "Update Info",
            execute: () =>
              navigate(
                getRecordPagePath(
                  RouteStrings.subjectEdit,
                  lastSubjectInteraction.subject.id
                )
              ),
          },
          {
            id: "PrintSubjectLabel",
            title: "Print Label",
            execute: () => setShowPrintSubjectLabel(true),
          },
        ],
      }
    : false
}
