import { useTranslation } from "react-i18next"

import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { PowerMenuGroup } from "../../lib/types"

export const useCalendarGroup = (actorId: string): PowerMenuGroup => {
  const navigateCalendar = useNavigateCalendar()

  const { t } = useTranslation()

  return {
    id: PowerMenuGroupKey.calendar,
    title: t(`Calendar`),
    items: [
      {
        id: "CalendarDay",
        title: t("day"),
        execute: () =>
          navigateCalendar(RouteStrings.calendar, {
            view: "day",
          }),
      },
      {
        id: "CalendarWorkWeek",
        title: t("Work week"),
        execute: () =>
          navigateCalendar(RouteStrings.calendar, {
            view: "work_week",
          }),
      },
      {
        id: "CalendarWeek",
        title: t("week"),
        execute: () =>
          navigateCalendar(RouteStrings.calendar, {
            view: "week",
          }),
      },
      {
        id: "CalendarMonth",
        title: t("month"),
        execute: () =>
          navigateCalendar(RouteStrings.calendar, {
            view: "month",
          }),
      },
    ],
  }
}
