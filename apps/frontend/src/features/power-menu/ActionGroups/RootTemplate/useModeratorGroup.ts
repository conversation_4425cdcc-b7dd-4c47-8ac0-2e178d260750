import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { View } from "features/user-management/UserManagement"
import { RouteStrings } from "routes/RouteStrings"

import { JournalTemplateType, PermissionKey } from "generated/graphql"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { PowerMenuGroup } from "../../lib/types"

export const useModeratorGroup = (): PowerMenuGroup => {
  const navigate = useNavigate()

  const { t } = useTranslation()

  return {
    id: PowerMenuGroupKey.moderator,
    title: "Moderator",
    items: [
      {
        id: "OrganisationManage",
        title: "Organisation overview",
        execute: () => navigate(RouteStrings.organisationManage),
      },
      {
        id: "Create Team",
        title: t("teamCreate"),
        execute: () => {
          navigate(
            generatePath(RouteStrings.organisationManage, {
              action: "createGlobalTeam",
            })
          )
        },
      },
      {
        id: "UserManagement",
        title: "User management",
        execute: () =>
          navigate(generatePath(RouteStrings.userManagement, View.Activated)),
        restrictedTo: PermissionKey.AccountsProviderAdminEdit,
      },
      {
        id: "InviteProvider",
        title: "Invite provider",
        execute: () => navigate(RouteStrings.providerCreateInvite),
        restrictedToAny: [
          PermissionKey.AccountsProviderCreate,
          PermissionKey.AccountsProviderInvite,
        ],
      },
      {
        id: "JournalTemplates",
        title: "Journal Templates",
        execute: () =>
          navigate(
            generatePath(RouteStrings.journalTemplates, {
              templateType: JournalTemplateType.InlineTemplate.toLowerCase(),
              templateId: null,
            })
          ),
        restrictedTo: PermissionKey.SubjectJournalTemplateView,
      },
      {
        id: "Locations",
        title: t("locations"),
        execute: () => {
          navigate(generatePath(RouteStrings.locations))
        },
      },
      {
        id: "ServiceTypes",
        title: t("Services"),
        execute: () => navigate(RouteStrings.serviceTypes),
      },
    ],
  }
}
