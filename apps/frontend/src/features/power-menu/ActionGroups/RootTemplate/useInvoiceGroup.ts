import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { HotKeys } from "features/power-menu/lib/hotkeys"
import { RouteStrings } from "routes/RouteStrings"

import { PermissionKey } from "generated/graphql"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { PowerMenuGroup, PowerMenuItem } from "../../lib/types"

export const useInvoiceGroup = (): PowerMenuGroup => {
  const navigate = useNavigate()

  const { t } = useTranslation()

  const items = [
    {
      id: "Organisation price list",
      title: t("Organisation price list"),
      execute: () => {
        navigate(RouteStrings.organisationPriceList)
      },
    },
    {
      id: "NHI price list",
      title: t("NHI price list"),
      execute: () => {
        navigate(RouteStrings.nationalHealthInsurancePriceList)
      },
    },
    {
      id: "Invoice overview",
      title: t("Invoice overview"),
      execute: () => {
        navigate(generatePath(RouteStrings.invoiceOverview))
      },
      shortcutCombination: HotKeys.invoiceOverview,
    },
  ] satisfies PowerMenuItem[]

  return {
    id: PowerMenuGroupKey.invoice,
    title: t(`Billing`),
    items: items,
    restrictedTo: PermissionKey.BillingBillingCodeView,
  } satisfies PowerMenuGroup
}
