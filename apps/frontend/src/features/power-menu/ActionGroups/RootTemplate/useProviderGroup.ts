import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { useAuth } from "features/authentication/AuthProvider"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import { RouteStrings } from "routes/RouteStrings"

import { PowerMenuGroupKey } from "../../lib/enums/PowerMenuGroupKeys.enum"
import { HotKeys } from "../../lib/hotkeys"
import { PowerMenuGroup } from "../../lib/types"

export const useProviderGroup = ({
  actor: { id, name, specialty },
}: Pick<GlobalDataWithNonNullableActor, "actor">): PowerMenuGroup => {
  const navigate = useNavigate()
  const { deauthenticate } = useAuth()

  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })

  const {
    globalState: { isNnMode },
    setGlobalState,
  } = useGlobalState()

  return {
    id: PowerMenuGroupKey.provider,
    title: `${name} – ${tEnum(specialty)}`,
    items: [
      {
        id: "ProviderWorklist",
        title: "Worklist",
        shortcutCombination: HotKeys.worklist,
        execute: () => navigate(RouteStrings.worklist),
      },
      {
        id: "ActorInfo",
        title: "Info",
        execute: () =>
          navigate(generatePath(RouteStrings.providerView, { providerId: id })),
      },
      {
        id: "ActorSettings",
        title: "Settings",
        execute: () => navigate(RouteStrings.actorSettings),
      },
      {
        id: "NnMode",
        title: isNnMode
          ? "Show sensitive information"
          : "Hide sensitive information",
        execute: () => setGlobalState("isNnMode", !isNnMode),
      },
      {
        id: "LogOut",
        title: "Log out",
        shortcutCombination: HotKeys.logout,
        execute: () => deauthenticate(),
      },
    ],
  }
}
