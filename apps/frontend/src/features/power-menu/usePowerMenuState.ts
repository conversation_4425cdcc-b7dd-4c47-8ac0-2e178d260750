/* eslint-disable @typescript-eslint/no-empty-function */
import { useEffect, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { useImmerReducer } from "use-immer"

import {
  translatePowerMenuItems,
  useFilterPowerMenuItems,
} from "./lib/helpers/powerMenuElements"
import { usePowerMenuHotKeys } from "./lib/hooks/usePowerMenuHotKeys"
import { PowerMenuItem, PowerMenuGroup } from "./lib/types"
import {
  addGroupAction,
  changeInputAction,
  closeAction,
  openAction,
  PowerMenuStateType,
  reducer,
  removeGroupAction,
  selectItemAction,
  setPowerMenuGroupsAction,
  setPrevArgumentAction,
} from "./powerMenuReducer"

const initialState: PowerMenuStateType = {
  isOpen: false,
  powerMenuGroups: [],
  initialPowerMenuGroups: [],
  inputValue: "",
  args: [],
  isLoading: false,
}

export const usePowerMenuState = () => {
  const [powerMenu, dispatch] = useImmerReducer(reducer, initialState)

  const { t } = useTranslation()

  const {
    args,
    powerMenuGroups,
    inputValue,
    initialPowerMenuGroups,
    ...restPowerMenuValues
  } = powerMenu

  const open = () => dispatch(openAction())

  const close = () => dispatch(closeAction())

  const addPowerMenuGroups = (powerMenuGroups: PowerMenuGroup[]) =>
    dispatch(addGroupAction(powerMenuGroups))

  const removePowerMenuGroups = (powerMenuGroups: PowerMenuGroup[]) =>
    dispatch(removeGroupAction(powerMenuGroups))

  const changeInputValues = (
    inputValue: string,
    args?: { executedItem: PowerMenuItem; searchString: string }[]
  ) => dispatch(changeInputAction(inputValue, args))

  const selectItem = async (currentOption: PowerMenuItem) =>
    await selectItemAction(currentOption, powerMenu, dispatch)

  const setPrevArgument = (callback: () => void) =>
    setPrevArgumentAction(callback, powerMenu, dispatch)

  const translatedInitialPowerMenuGroups = useMemo(() => {
    return translatePowerMenuItems(initialPowerMenuGroups, t)
  }, [initialPowerMenuGroups])

  useEffect(() => {
    dispatch(setPowerMenuGroupsAction(translatedInitialPowerMenuGroups))
  }, [translatedInitialPowerMenuGroups])

  const filteredGroups = useFilterPowerMenuItems(powerMenuGroups, inputValue)

  usePowerMenuHotKeys(initialPowerMenuGroups, {
    open,
    close,
    isOpen: powerMenu.isOpen,
  })

  return {
    open,
    close,
    addPowerMenuGroups,
    removePowerMenuGroups,
    changeInputValues,
    selectItem,
    setPrevArgument,
    filteredGroups,
    inputValue,
    args,
    ...restPowerMenuValues,
  }
}
