import { DependencyList, useEffect } from "react"

import { usePowerMenu } from "./PowerMenu.context"
import { PowerMenuGroup } from "./lib/types/PowerMenuGroup"

// dependencies type not defined
export const usePowerMenuGroups = (
  powerMenuGroups: PowerMenuGroup[],
  dependencies?: DependencyList
) => {
  const { addPowerMenuGroups, removePowerMenuGroups } = usePowerMenu()

  useEffect(() => {
    addPowerMenuGroups(powerMenuGroups)

    return () => {
      removePowerMenuGroups(powerMenuGroups)
    }
  }, dependencies ?? [])
}
