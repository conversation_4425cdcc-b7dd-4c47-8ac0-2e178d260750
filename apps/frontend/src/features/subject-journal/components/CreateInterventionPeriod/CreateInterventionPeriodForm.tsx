import { FormEvent, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import z, { ZodIssue } from "zod"

import { FormGrid, Input, Modal } from "ui"
import Button from "ui/components/Button/Button"

import { useCreateInterventionPeriodMutation } from "generated/graphql"

import styles from "../RegisterSubject/RegisterSubject.module.css"
import formStyles from "./CreateInterventionPeriodForm.module.css"

const schema = z.object({
  title: z.string().min(3),
})

type FormDataErrors = z.inferFlattenedErrors<
  typeof schema,
  { message: string; code: string }
>

export type CreateInterventionPeriodFormProps = {
  onCompleted?: () => void
  onCancel?: () => void
  subjectId: string
}

export default function CreateInterventionPeriodForm({
  onCompleted,
  onCancel,
  subjectId,
}: CreateInterventionPeriodFormProps) {
  const personaIdRef = useRef<HTMLInputElement>(null)
  const [validationError, setValidationError] = useState<FormDataErrors | null>(
    null
  )

  const { t } = useTranslation()

  useEffect(() => {
    personaIdRef.current?.focus()
  }, [])

  const [createInterventionPeriod, { loading }] =
    useCreateInterventionPeriodMutation({
      onCompleted: (data) => {
        if (data) {
          onCompleted?.()
        }
      },
    })

  const handleSubmitCreateInterventionPeriod = (
    event: FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = schema.safeParse(data)
    if (!validatedInput.success) {
      setValidationError(
        validatedInput.error.flatten((issue: ZodIssue) => ({
          message: issue.message,
          code: issue.code,
        }))
      )
      console.error(validatedInput.error)

      return
    }

    createInterventionPeriod({
      variables: {
        interventionPeriodInput: {
          title: validatedInput.data.title,
          subjectId,
        },
      },
    })
  }

  const handleFocus = (fieldName: "title") => {
    setValidationError((prevValidationError) => {
      if (prevValidationError) {
        const newValidationError = { ...prevValidationError }
        delete newValidationError?.fieldErrors[fieldName]
        return newValidationError
      }
      return prevValidationError
    })
  }

  return (
    <Modal
      title={t("New intervention period")}
      isOpen={true}
      onClose={() => onCancel?.()}
      contentClassName={formStyles.modal}
    >
      <FormGrid
        as="form"
        onSubmit={handleSubmitCreateInterventionPeriod}
        colSpan={4}
        className={styles.form}
      >
        <Input
          ref={personaIdRef}
          label="Title"
          name="title"
          required={true}
          status={
            validationError?.fieldErrors?.title?.[0] ? "error" : "default"
          }
          message={validationError?.fieldErrors?.title?.[0].message || null}
          onFocus={() => handleFocus("title")}
          minLength={3}
        />

        <div className={formStyles.submitRow}>
          {onCancel && (
            <Button
              size="large"
              disabled={loading}
              onClick={onCancel}
              data-testid="intervention-period-cancel"
            >
              {t("Cancel")}
            </Button>
          )}
          <Button
            type="submit"
            variant="filled"
            size="large"
            disabled={loading}
            data-testid="intervention-period-submit"
          >
            {t("Register")}
          </Button>
        </div>
      </FormGrid>
    </Modal>
  )
}
