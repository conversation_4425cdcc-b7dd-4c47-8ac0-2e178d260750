import c from "classnames"
import { ReactNode, useCallback, useEffect, useState } from "react"

import { useJournalFocus } from "features/subject-journal/components/JournalFocus/JournalFocus"
import { JournalGrid } from "ui"
import { scrollToElement } from "utils/scrollToElement"

import { InterventionPeriodFragmentFragment } from "generated/graphql"

import styles from "./InterventionPeriod.module.css"
import { InterventionPeriodHeader } from "./InterventionPeriodHeader"

type InterventionPeriodProps = {
  children: ReactNode
  title?: string
  id: InterventionPeriodFragmentFragment["id"]
  journalData?: string[]
  subjectId: string
}

export const InterventionPeriod = ({
  children,
  title,
  id,
  journalData,
  subjectId,
}: InterventionPeriodProps) => {
  const [collapsed, setCollapsed] = useState(false)
  const { focusedItemId } = useJournalFocus()

  useEffect(() => {
    let scrollTimeoutId: NodeJS.Timeout

    if (focusedItemId && journalData?.includes(focusedItemId)) {
      setCollapsed(false)
      scrollTimeoutId = setTimeout(() => {
        scrollToElement(focusedItemId)
      }, 100)
    }

    return () => clearTimeout(scrollTimeoutId)
  }, [focusedItemId, journalData])

  const toggleCollapsed = useCallback(() => {
    setCollapsed((c) => !c)
  }, [])

  return (
    <JournalGrid
      rowGap
      className={c(styles.journalGrid, {
        [styles.collapsed]: collapsed,
      })}
      id={id}
    >
      <InterventionPeriodHeader
        subjectId={subjectId}
        title={title}
        collapsed={collapsed}
        onClick={toggleCollapsed}
        journalData={journalData}
      />
      {collapsed ? null : children}
    </JournalGrid>
  )
}
