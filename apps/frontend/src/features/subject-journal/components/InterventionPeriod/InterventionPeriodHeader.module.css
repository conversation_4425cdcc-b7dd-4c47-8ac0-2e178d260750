.wrap.wrap {
  display: flex;
  align-items: center;
  position: relative;
  width: 100%;
  gap: 16px;
  margin-left: -24px;
}

.wrap.inboundDataIPHeader {
  margin-left: 0;
}

.title {
  display: flex;
  gap: 6px;
  width: fit-content;
  flex-grow: 0;
  cursor: pointer;
  width: 100%;
}

.title > h2 {
  margin-right: 10px;
}

.folderIcon {
  flex-shrink: 0;
  margin-top: 1px;
}

.arrowIcon {
  margin-right: -44px;
  margin-left: 8px;
  flex-shrink: 0;
}
.collapsableRuler {
  background-color: var(--color-lev-blue-200);
  height: 1px;
  flex-grow: 1;
  align-self: center;
}

.ruler {
  background-color: var(--color-lev-blue-200);
  height: 1px;
  flex-grow: 1;
  margin-right: -46px;
}

.wrap.inboundDataIPHeader .ruler {
  margin-right: 0;
}

.addInterventionPeriod {
  display: flex;
  gap: 8px;
  align-items: center;
}
