import c from "classnames"
import { useEffect } from "react"
import { useTranslation } from "react-i18next"

import { useMenuStore } from "components/Ariakit/hooks"
import { FilterableMenu } from "components/FilterableMenu/FilterableMenu"
import Icon from "components/Icon/Icon"
import { Heading } from "ui"
import { Center } from "ui/components/Layout/Center"

import {
  GetSubjectJournalQuery,
  useGetUnlinkedJournalDataQuery,
  useUpdateEncounterMutation,
  useUpdateInboundDataMutation,
} from "generated/graphql"

import { useJournalDataInView } from "../JournalDataInView/JournalDataInView.context"
import styles from "./InterventionPeriodHeader.module.css"

type InterventionPeriodHeaderProps = {
  subjectId: string
  interventionPeriods?: GetSubjectJournalQuery["subjectJournal"]["interventionPeriods"]
  journalData?: string[]
  encounterId?: string
  inboundDataId?: string
  title?: string
  collapsed?: boolean
  isInboundData?: boolean
  onClick?: () => void
}

export const InterventionPeriodHeader = ({
  title,
  interventionPeriods = [],
  journalData = [],
  encounterId,
  inboundDataId,
  subjectId,
  collapsed,
  isInboundData,
  onClick,
}: InterventionPeriodHeaderProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "interventionPeriod",
  })
  const { t } = useTranslation()
  const [updateEncounter] = useUpdateEncounterMutation()
  const [updateInboundData] = useUpdateInboundDataMutation()
  const { refetch } = useGetUnlinkedJournalDataQuery({
    variables: { subjectId },
  })

  const { markJournalDataInView } = useJournalDataInView()

  useEffect(() => {
    // If the intervention period is collapsed, mark all encounters as not in view
    if (collapsed && journalData.length > 0) {
      journalData.map((journalData) => {
        markJournalDataInView(journalData, false)
      })
    }
  }, [collapsed])

  const menuStore = useMenuStore()
  const menuOptions = interventionPeriods.map((ip) => {
    return {
      label: ip.title || t("untitled"),
      value: ip.id,
    }
  })

  const handleAddToInterventionPeriod = (value: string) => {
    if (encounterId) {
      updateEncounter({
        variables: {
          id: encounterId,
          input: {
            interventionPeriodId: value,
          },
        },
        onCompleted: () => {
          refetch()
        },
      })
    }
    if (inboundDataId) {
      updateInboundData({
        variables: {
          id: inboundDataId,
          input: {
            interventionPeriodId: value,
          },
        },
        onCompleted: () => {
          refetch()
        },
      })
    }
  }

  return (
    <Center
      className={c(styles.wrap, {
        [styles.inboundDataIPHeader]: isInboundData,
      })}
    >
      <>
        {!encounterId && !inboundDataId ? (
          <button className={styles.title} onClick={onClick}>
            <Icon
              name="folder-line"
              fontSize={20}
              className={styles.folderIcon}
            />
            <Heading size="default">{title || t("untitled")}</Heading>
            <div className={styles.collapsableRuler} />
            <Icon
              name={collapsed ? "arrow-down-s-line" : "arrow-up-s-line"}
              fontSize={20}
              className={styles.arrowIcon}
            />
          </button>
        ) : (
          <>
            <div className={styles.addInterventionPeriod}>
              <FilterableMenu
                menuStore={menuStore}
                options={menuOptions}
                onSelect={(option) => {
                  menuStore.setOpen(false)
                  handleAddToInterventionPeriod(option.value)
                }}
                label={tRoutes("addToInterventionPeriod")}
              />
            </div>
            <div className={styles.ruler} />
          </>
        )}
      </>
    </Center>
  )
}
