import { useState } from "react"
import { useTranslation } from "react-i18next"
import { v4 as uuid } from "uuid"

import { Icon } from "@leviosa/components"

import { useMenuStore } from "components/Ariakit/hooks"
import { FilterableMenu } from "components/FilterableMenu/FilterableMenu"
import usePermissions from "features/authentication/hooks/usePermissions"

import {
  ClinicalCodingType,
  PermissionKey,
  useCreateDrugPrescriptionMutation,
  useCreateMedicalCertificateMutation,
  useCreateOutboundDoctorsLetterMutation,
  useCreateOutboundReferralMutation,
} from "generated/graphql"

import ClinicalCodingForm from "../ClinicalCodingForm/ClinicalCodingForm"
import { useJournalEntryContext } from "../JournalEntry/JournalEntry"
import styles from "./AddSupplements.module.css"

const orderedCodingTypes = [
  ClinicalCodingType.Diagnosis,
  ClinicalCodingType.Operation,
  ClinicalCodingType.Allergy,
  ClinicalCodingType.Behavior,
  ClinicalCodingType.TreatmentRestriction,
]

export type AddSupplementsProps = {
  inlineButton?: boolean
  onSelectBilling?: () => void
  onSelectAttachment?: () => void
}

export const AddSupplements = ({
  inlineButton = false,
  onSelectBilling,
  onSelectAttachment,
}: AddSupplementsProps) => {
  const journalEntryId: string = useJournalEntryContext().id

  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "addSupplements",
  })

  const [showClinicalCodingForm, setShowClinicalCodingForm] =
    useState<ClinicalCodingType | null>(null)

  const menuStore = useMenuStore({
    placement: "right-start",
  })

  const { hasPermission } = usePermissions()

  const [createOutboundReferralMutation, { loading: referralLoading }] =
    useCreateOutboundReferralMutation()

  const createOutboundReferral = (id: string) => {
    createOutboundReferralMutation({
      variables: {
        id,
        input: {
          journalEntryId,
        },
      },
      onError: (error) => {
        console.error("Failed to create outbound referral:", error)
      },
    })
  }

  const [
    createOutboundDoctorsLetterMutation,
    { loading: doctorsLetterLoading },
  ] = useCreateOutboundDoctorsLetterMutation()

  const createOutboundDoctorsLetter = (id: string) => {
    createOutboundDoctorsLetterMutation({
      variables: {
        id,
        input: {
          journalEntryId,
        },
      },
      onError: (error) => {
        console.error("Failed to create outbound doctors letter:", error)
      },
    })
  }

  const [createDrugPrescriptionMutation, { loading: drugPrescriptionLoading }] =
    useCreateDrugPrescriptionMutation()

  const createDrugPrescription = (id: string) => {
    createDrugPrescriptionMutation({
      variables: {
        id,
        input: {
          journalEntryId,
          // Apply default values which apply most of the time, saves mouse-click. May be configured per drug in future.
          packageCount: 1,
          validFrom: new Date(),
          // one year between
          validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
        },
      },
      onError: (error) => {
        console.error("Failed to create drug prescription:", error)
      },
    })
  }

  const [
    createMedicalCertificateMutation,
    { loading: medicalCertificateLoading },
  ] = useCreateMedicalCertificateMutation()

  const createMedicalCertificate = (
    id: string,
    certType: string,
    content: { [key: string]: string }
  ) => {
    return createMedicalCertificateMutation({
      variables: {
        createMedicalCertificateId: id,
        input: {
          journalEntryId: journalEntryId,
          certType: certType,
          version: 1,
          content: content,
        },
      },
      onError: (error) => {
        console.error("Failed to create medical certificate:", error)
      },
    })
  }

  const isAnyMutationLoading =
    referralLoading ||
    doctorsLetterLoading ||
    drugPrescriptionLoading ||
    medicalCertificateLoading

  const menuOptions = orderedCodingTypes.map((coding) => {
    return {
      label: tEnum(`ClinicalCoding_CodingType.${coding}`),
      value: tEnum(`ClinicalCoding_CodingType.${coding}`),
      onSelect: () => {
        setShowClinicalCodingForm(coding)
      },
    }
  })

  if (hasPermission(PermissionKey.BillingIssuedItemCreate) && onSelectBilling) {
    menuOptions.unshift({
      label: tRoutes("billing"),
      value: tRoutes("billing"),
      onSelect: onSelectBilling,
    })
  }

  menuOptions.unshift({
    label: tRoutes("certificate"),
    value: tRoutes("certificate"),
    onSelect: () =>
      createMedicalCertificate(uuid(), "Absence", {
        employerOrSchool: "",
        fromDate: "",
        toDate: "",
        description: "",
      }),
  })

  menuOptions.unshift({
    label: tRoutes("freeTextDocument"),
    value: tRoutes("freeTextDocument"),
    onSelect: () =>
      createMedicalCertificate(uuid(), "Free-text", {
        title: "",
        description: "",
      }),
  })

  // TODO: use correct permission key
  if (hasPermission(PermissionKey.SubjectJournalDrugPrescriptionEdit)) {
    menuOptions.unshift({
      label: tRoutes("clinicalReferral"),
      value: tRoutes("clinicalReferral"),
      onSelect: () => createOutboundReferral(uuid()),
    })

    menuOptions.unshift({
      label: tRoutes("clinicalCorrespondence"),
      value: tRoutes("clinicalCorrespondence"),
      onSelect: () => createOutboundDoctorsLetter(uuid()),
    })
  }

  if (hasPermission(PermissionKey.SubjectJournalDrugPrescriptionEdit)) {
    menuOptions.unshift({
      label: tRoutes("drugPrescription"),
      value: tRoutes("drugPrescription"),
      onSelect: () => createDrugPrescription(uuid()),
    })
  }

  if (onSelectAttachment) {
    menuOptions.unshift({
      label: tRoutes("addAttachment"),
      value: tRoutes("addAttachment"),
      onSelect: onSelectAttachment,
    })
  }
  return (
    <>
      <FilterableMenu
        menuStore={menuStore}
        options={menuOptions}
        data-testid="add-supplements-menu"
        disabled={isAnyMutationLoading}
        onSelect={(option: {
          label: string
          value: string
          onSelect?: () => void
        }) => {
          menuStore.setOpen(false)
          option.onSelect?.()
        }}
        className={!inlineButton ? styles.menuWrap : ""}
        label={inlineButton ? undefined : tRoutes("addSupplements")}
        {...(isAnyMutationLoading && {
          icon: <Icon name="loader-4-line" spin />,
        })}
      />

      {showClinicalCodingForm !== null && (
        <div className={styles.clinicalCodingForm}>
          <ClinicalCodingForm
            referenceType={showClinicalCodingForm}
            onSelectOption={() => setShowClinicalCodingForm(null)}
            onCancel={() => setShowClinicalCodingForm(null)}
          />
        </div>
      )}
    </>
  )
}
