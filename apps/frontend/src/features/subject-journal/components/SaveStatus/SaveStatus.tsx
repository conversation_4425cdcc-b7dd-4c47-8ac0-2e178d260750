import dayjs from "dayjs"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Text } from "ui"

import styles from "./SaveStatus.module.css"

export type Status = "none" | "saving" | "saved" | "lastSavedAt" | "error"

type SaveStatus = {
  status: Status
  updatedAt: string | null
  className?: string
}

type GetSaveStatus = {
  isLoading: boolean
  isError: boolean
  isCalled: boolean
  isContentSync: boolean
}

export const getSaveStatus = (status: GetSaveStatus): Status => {
  const { isLoading, isError, isCalled, isContentSync } = status

  if (isLoading) return "saving"
  if (isError) return "error"

  if (isCalled) {
    return isContentSync ? "saved" : "lastSavedAt"
  }

  return "none"
}

export default function SaveStatus({
  status,
  updatedAt,
  className = "",
}: SaveStatus) {
  const { t } = useTranslation()

  if (status === "none") return null

  let icon
  let message

  switch (status) {
    case "error":
      icon = <Icon name="error-warning-line" />
      message = t("routes:journalEntryStatuses.error")
      break

    case "lastSavedAt":
      icon = <Icon name="check-line" />
      message = `Last saved ${dayjs
        .duration(dayjs().diff(dayjs(updatedAt)))
        .humanize()} ago`
      break

    case "saved":
      icon = <Icon name="checkbox-circle-line" />
      message = t("routes:journalEntryStatuses.saved")
      break

    case "saving":
      icon = <Icon name="more-line" />
      message = t("routes:journalEntryStatuses.saving")
  }

  return (
    <p className={`${styles.wrapper} ${className}`}>
      <span className={styles.icon}>{icon}</span>
      <Text as="span" size="small">
        {message}
      </Text>
    </p>
  )
}
