import { isArray, sortBy } from "lodash"
import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Select from "components/Select/Select"
import { UserInfo } from "features/calendar/components/UserInfo/UserInfo"
import { Button } from "ui"

import { ProviderInfoFragmentFragment } from "generated/graphql"

import styles from "./AddResponsibleProviders.module.css"

export type AddResponsibleProvidersProps = {
  responsibleProviders: string[]
  members: ProviderInfoFragmentFragment[]
  onAddResponsibleProvider: (providerId: string) => void
  onDeleteResponsibleProvider: (providerId: string) => void
  onRequestClose?: () => void
  className?: string
  hideFooter?: boolean
}

export default function AddResponsibleProviders({
  responsibleProviders: responsibleProviders,
  members,
  onRequestClose,
  onAddResponsibleProvider,
  onDeleteResponsibleProvider,
  className,
  hideFooter = false,
}: AddResponsibleProvidersProps) {
  const { t } = useTranslation("routes", {
    keyPrefix: "addResponsibleProviders",
  })
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ProviderSpecialty",
  })
  const { globalData } = useGlobalState()
  const actorId = globalData.actor.id

  const responsibleProviderSelectStore = useSelectStore({
    value: "",
  })

  const filteredTeamMembers = members.filter(
    (member) => !responsibleProviders.includes(member.id)
  )

  const primaryTeamMembersOptions = sortBy(filteredTeamMembers, "name").map(
    ({ id, name }) => ({
      value: id,
      label: name,
    })
  )

  const handleSelectMe = () => {
    onAddResponsibleProvider?.(actorId)
    onRequestClose?.()
  }

  return (
    <div className={className}>
      <Select
        className={styles.span4}
        label={t("providers")}
        placeholder={t("addProviders")}
        selectStore={responsibleProviderSelectStore}
        options={primaryTeamMembersOptions}
        onSelectChange={(selected) => {
          if (isArray(selected)) return

          onAddResponsibleProvider?.(selected === null ? "" : selected)
        }}
        message={
          !responsibleProviders.includes(actorId) && (
            <Button
              variant="clear"
              onClick={handleSelectMe}
              className={styles.addMeButton}
            >
              {t("addMe")}
            </Button>
          )
        }
      />
      <div className={styles.responsibleProviders}>
        {responsibleProviders.map((providerId) => {
          const provider = members.find((member) => member.id === providerId)
          return (
            provider && (
              <UserInfo
                key={provider.id}
                id={provider.id}
                name={provider.name}
                description={tEnum(provider.specialty)}
                onRemove={() => {
                  onDeleteResponsibleProvider?.(provider.id)
                }}
                isAvailable={true}
              />
            )
          )
        })}
      </div>
      {!hideFooter && (
        <div className={styles.footer}>
          <Button variant="filled" onClick={onRequestClose}>
            Done
          </Button>
        </div>
      )}
    </div>
  )
}
