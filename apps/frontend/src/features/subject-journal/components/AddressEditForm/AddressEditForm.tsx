import { Dispatch } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { CountrySelect } from "components/SelectCountry/SelectCountry"
import { Input } from "ui"

import "generated/graphql"

import { zodFieldValidation } from "../../../../utils/zodFieldValidation"
import { FormState } from "../SubjectEdit/SubjectEdit"
import styles from "../SubjectEdit/SubjectEdit.module.css"

type Props = {
  subjectId: string
  formState: FormState
  setFormState: Dispatch<Partial<FormState>>
}

/* Simple & generic validations b/c low-level validation would be very complicated and needs lots of r/d */
export const addressSchema = {
  addressLine1: z.string().min(3, "validationErrors.generic"),
  addressLine2: z
    .string()
    .nullable()
    .refine((value) => !value || value.length >= 3, {
      message: "validationErrors.generic",
    }),
  postalCode: z.string().min(2, "validationErrors.generic"),
  city: z.string().min(2, "validationErrors.generic"),
  region: z
    .string()
    .nullable()
    .refine((value) => !value || value.length >= 2, {
      message: "validationErrors.generic",
    }),
  country: z.string().refine((value) => value.length >= 2, {
    message: "validationErrors.generic",
  }),
}

/* 
  This is a separate "pure component" because:
   1) Address is a separate entity in API thus needs separate mutations (separation of concerns).
   2) Address is not specific for Subject (e.g. may be used for Organisation, Provider etc).
  It is a "pure component" i.e. only responsible for rendering the form.
*/
export const AddressEditForm = ({
  formState,
  formState: { validationResult },
  setFormState,
}: Props) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "manageSubject",
  })

  return (
    <>
      <Input
        /* For more seamless editing experience when redirected from SubjectSummary */
        autoFocus={window.location.hash.includes("#address")}
        label={tRoutes("addressLine1")}
        required
        type="text"
        value={formState.addressLine1}
        onChange={({ currentTarget }) =>
          setFormState({ addressLine1: currentTarget.value })
        }
        {...zodFieldValidation("addressLine1", validationResult, tRoutes)}
      />

      <Input
        label={tRoutes("addressLine2")}
        type="text"
        value={formState.addressLine2 || ""}
        onChange={({ currentTarget }) =>
          setFormState({ addressLine2: currentTarget.value })
        }
        {...zodFieldValidation("addressLine2", validationResult, tRoutes)}
      />

      <Input
        label={tRoutes("postalCode")}
        type="text"
        required
        className={styles.span2}
        value={formState.postalCode}
        onChange={({ currentTarget }) =>
          setFormState({ postalCode: currentTarget.value })
        }
        {...zodFieldValidation("postalCode", validationResult, tRoutes)}
      />

      <Input
        label={tRoutes("city")}
        type="text"
        required
        className={styles.span4}
        value={formState.city}
        onChange={({ currentTarget }) =>
          setFormState({ city: currentTarget.value })
        }
        {...zodFieldValidation("city", validationResult, tRoutes)}
      />

      <Input
        label={tRoutes("region")}
        type="text"
        value={formState.region || ""}
        className={styles.span2}
        onChange={({ currentTarget }) =>
          setFormState({ region: currentTarget.value })
        }
        {...zodFieldValidation("region", validationResult, tRoutes)}
      />

      <CountrySelect
        required
        label={tRoutes("country")}
        className={styles.span4}
        onChange={(value) => setFormState({ country: value || "" })}
        defaultValue={formState.country}
        {...zodFieldValidation("country", validationResult, tRoutes)}
      />
    </>
  )
}
