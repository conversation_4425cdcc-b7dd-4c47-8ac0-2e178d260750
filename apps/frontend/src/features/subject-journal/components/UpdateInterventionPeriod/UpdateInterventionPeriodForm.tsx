import { FormEvent, useEffect, useRef, useState } from "react"
import z, { ZodIssue } from "zod"

import Panel from "components/Panel/Panel"
import { FormGrid, Input, Modal } from "ui"
import Button from "ui/components/Button/Button"

import { useUpdateInterventionPeriodMutation } from "generated/graphql"

import styles from "../RegisterSubject/RegisterSubject.module.css"

const personalInfoSchema = z.object({
  title: z.string().min(5),
})

type FormDataErrors = z.inferFlattenedErrors<
  typeof personalInfoSchema,
  { message: string; code: string }
>

export type UpdateInterventionPeriodFormProps = {
  onClose?: () => void
  interventionPeriodId: string | null
  currentTitle: string
  showModal: boolean
}

export default function UpdateInterventionPeriodForm({
  onClose,
  interventionPeriodId,
  currentTitle,
  showModal,
}: UpdateInterventionPeriodFormProps) {
  const titleRef = useRef<HTMLInputElement>(null)
  const [validationError, setValidationError] = useState<FormDataErrors | null>(
    null
  )

  useEffect(() => {
    if (showModal) {
      titleRef.current?.select()
    }
  }, [showModal])

  const [updateInterventionPeriod, { loading, error }] =
    useUpdateInterventionPeriodMutation()

  const handleSubmitUpdateInterventionPeriod = async (
    event: FormEvent<HTMLFormElement>
  ) => {
    event.preventDefault()
    setValidationError(null)

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = personalInfoSchema.safeParse(data)
    if (!validatedInput.success) {
      setValidationError(
        validatedInput.error.flatten((issue: ZodIssue) => ({
          message: issue.message,
          code: issue.code,
        }))
      )
      console.error(validatedInput.error)

      return
    }

    if (!interventionPeriodId) return

    const res = await updateInterventionPeriod({
      variables: {
        updateInterventionPeriodId: interventionPeriodId,
        input: {
          title: validatedInput.data.title,
        },
      },
    })

    if (res.data) {
      onClose?.()
    }
  }

  const handleFocus = (fieldName: "title") => {
    setValidationError((prevValidationError) => {
      if (prevValidationError) {
        const newValidationError = { ...prevValidationError }
        delete newValidationError?.fieldErrors[fieldName]
        return newValidationError
      }
      return prevValidationError
    })
  }

  return (
    <Modal
      isOpen={showModal}
      title="Edit Intervention Period"
      closeOnClickOutside
      onClose={onClose}
    >
      <FormGrid
        as="form"
        onSubmit={handleSubmitUpdateInterventionPeriod}
        colSpan={4}
        className={styles.form}
      >
        <Input
          ref={titleRef}
          label="Title"
          name="title"
          required={true}
          status={
            validationError?.fieldErrors?.title?.[0] ? "error" : "default"
          }
          defaultValue={currentTitle}
          message={validationError?.fieldErrors?.title?.[0].message || null}
          onFocus={() => handleFocus("title")}
          minLength={5}
        />
        {error && <Panel status="error">{error.message}</Panel>}
        <div className={styles.submitRow}>
          {onClose && (
            <Button
              size="large"
              disabled={loading}
              variant="clear"
              onClick={onClose}
              data-testid="intervention-period-cancel"
            >
              Cancel
            </Button>
          )}
          <Button
            type="submit"
            variant="filled"
            size="large"
            disabled={loading}
            data-testid="intervention-period-submit"
          >
            Update
          </Button>
        </div>
      </FormGrid>
    </Modal>
  )
}
