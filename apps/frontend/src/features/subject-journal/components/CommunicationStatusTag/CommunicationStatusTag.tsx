import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"

import { Color } from "styles/colors"
import { Tag } from "ui"

import { CommunicationStatus } from "generated/graphql"

export const statusMap: {
  [key in CommunicationStatus]: {
    color: Color
    icon: IconName
  }
} = {
  SENT: {
    color: "orange",
    icon: "time-line",
  },
  READ: {
    color: "levGreen",
    icon: "checkbox-circle-line",
  },
  RECEIVED: {
    color: "levGreen",
    icon: "inbox-2-line",
  },
  DECLINED: {
    color: "critical",
    icon: "close-line",
  },
  FAILED: {
    color: "critical",
    icon: "error-warning-line",
  },
  CANCELLED: {
    color: "critical",
    icon: "close-line",
  },
  // Currently were not rendering a tag for Draft
  DRAFT: {
    color: "levBlue",
    icon: "edit-line",
  },
}

type CommunicationStatusTagProps = {
  className?: string
  status: CommunicationStatus
}
export const CommunicationStatusTag = ({
  className,
  status,
}: CommunicationStatusTagProps) => {
  const { t: tEnum } = useTranslation("enums")

  return (
    <Tag
      color={statusMap[status].color}
      className={className}
      size="small"
      iconName={statusMap[status].icon}
    >
      {tEnum(`CommunicationStatus.${status}`)}
    </Tag>
  )
}
