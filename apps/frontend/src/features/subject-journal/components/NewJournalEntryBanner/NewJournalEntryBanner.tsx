import c from "classnames"
import { matchSorter } from "match-sorter"
import { useDeferredValue, useMemo, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, <PERSON> } from "react-router-dom"

import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import { RouteStrings } from "routes/RouteStrings"
import { Button, ButtonText, Heading, Input, Text } from "ui"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateType,
} from "generated/graphql"

import JournalTemplateTile from "./JournalTemplateTile"
import styles from "./NewJournalEntryBanner.module.css"

type NewJournalEntryBannerProps = {
  journalTemplates: JournalTemplateFragmentFragment[]
  encounterId: string
}

export default function NewJournalEntryBanner({
  journalTemplates,
  encounterId,
}: NewJournalEntryBannerProps) {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.journalEntries",
  })

  const [showTemplateGallery, setShowTemplateGallery] = useState(false)
  const [searchValue, setSearchValue] = useState("")
  const deferredSearchValue = useDeferredValue(searchValue)
  const [templateError, setTemplateError] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)

  const matches = useMemo(() => {
    return deferredSearchValue
      ? matchSorter(journalTemplates, deferredSearchValue, {
          keys: ["name"],
        })
      : journalTemplates
  }, [deferredSearchValue])

  const templatesToShow = showTemplateGallery
    ? deferredSearchValue
      ? matches
      : journalTemplates
    : journalTemplates.slice(0, 4)

  if (journalTemplates.length === 0) {
    return (
      <div className={styles.wrap}>
        <Heading size="xsmall">{tRoutes("createNewEntry")}</Heading>
        <div className={styles.emptyState}>
          <Heading size="small">
            {t("You don't have any published templates")}
          </Heading>
          <Text size="small">{t("Create templates to start journaling")}</Text>
          <Button
            as={Link}
            to={generatePath(RouteStrings.journalTemplates, {
              templateType: JournalTemplateType.DocumentTemplate.toLowerCase(),
            })}
            size="small"
          >
            {t("Create templates")}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className={styles.wrap}>
      <div className={styles.header}>
        <Heading size="xsmall">{tRoutes("createNewEntry")}</Heading>
        {journalTemplates.length > 3 && (
          <Button
            variant="clear"
            size="small"
            iconEnd={
              <Icon
                name={
                  showTemplateGallery ? "arrow-up-s-line" : "arrow-down-s-line"
                }
              />
            }
            className={styles.galleryButton}
            onClick={() => {
              // Clear search value when closing the template gallery
              if (showTemplateGallery) {
                setSearchValue("")
              }
              setShowTemplateGallery(!showTemplateGallery)
              searchInputRef.current?.focus()
            }}
          >
            <ButtonText size="small">{tRoutes("templateGallery")}</ButtonText>
          </Button>
        )}
      </div>
      <Input
        label={t("search")}
        hideLabel
        hideMessage
        ref={searchInputRef}
        icon={<Icon name="search-line" />}
        onChange={(e) => setSearchValue(e.target.value)}
        value={deferredSearchValue}
        placeholder={tRoutes("filterTemplates")}
        clearable
        className={c(styles.searchInput, {
          [styles.searchInputVisible]: showTemplateGallery,
        })}
      />

      <div className={styles.options}>
        {templatesToShow.map((template) => (
          <JournalTemplateTile
            key={template.id}
            journalTemplate={template}
            data-testid="journal-template-tile"
            encounterId={encounterId}
            onApplyTemplate={() => {
              setShowTemplateGallery(false)
              setSearchValue("")
              setTemplateError(false)
            }}
            onError={() => setTemplateError(true)}
          />
        ))}
      </div>
      {templateError && (
        <Panel status="error">
          {tRoutes("createEntryError")}
          <a href="mailto:<EMAIL>"><EMAIL></a>
        </Panel>
      )}
    </div>
  )
}
