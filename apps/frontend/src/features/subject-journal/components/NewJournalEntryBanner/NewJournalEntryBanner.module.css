.wrap {
  display: grid;
  width: 100%;
  padding-top: 24px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: baseline;
}

.options {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  grid-gap: 8px;
  width: 100%;
  margin-top: 16px;
}

.tile {
  display: flex;
  flex-direction: column;
  gap: 4px;
  border-radius: var(--radius-button-half);
  box-shadow: 0px 0px 10px 0px rgba(198, 201, 219, 0.3);
  height: 88px;
  padding: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}
.tile:not(:hover, :active) {
  background-color: var(--color-white);
}

.label {
  /* add text ellipsis after 2 lines */
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.subLabel {
  color: var(--color-lev-blue);
}

.galleryButton {
  width: fit-content;
}

.searchInput {
  width: 50%;
  max-height: 0;
  opacity: 0;
  transition:
    max-height 0.2s ease,
    opacity 0.2s ease,
    padding-top 0.2s ease;
  margin-left: auto;
}

.searchInputVisible {
  max-height: 100px;
  opacity: 1;
  padding-top: 16px;
}

.templateHovercard {
  width: 270px;
  display: grid;
  gap: 8px;
  padding: 12px 16px 16px;
}

.emptyState {
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: center;
  align-items: center;
  padding-top: 16px;
}
