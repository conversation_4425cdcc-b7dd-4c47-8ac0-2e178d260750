import { HovercardProvider } from "@ariakit/react"
import { useTranslation } from "react-i18next"

import {
  Ho<PERSON>card,
  HovercardAnchor,
  useHovercardStore,
} from "components/Ariakit/Hovercard/Hovercard"
import { Label, Text } from "ui"

import {
  JournalTemplateFragmentFragment,
  namedOperations,
  useApplyJournalTemplateMutation,
} from "generated/graphql"

import { useJournalFocus } from "../JournalFocus/JournalFocus"
import styles from "./NewJournalEntryBanner.module.css"

type JournalTemplateTileProps = {
  journalTemplate: JournalTemplateFragmentFragment
  encounterId: string
  "data-testid"?: string
  disabled?: boolean
  onApplyTemplate: () => void
  onError: () => void
}

export default function JournalTemplateTile({
  journalTemplate,
  encounterId,
  "data-testid": testid,
  disabled = false,
  onApplyTemplate,
  onError,
}: JournalTemplateTileProps) {
  const { t: tEnum } = useTranslation("enums")

  const { setFocusedBlockId } = useJournalFocus()

  const hovercardStore = useHovercardStore()

  const [applyJournalTemplate, { loading: loadingApplyTemplate }] =
    useApplyJournalTemplateMutation({
      refetchQueries: [namedOperations.Query.ProviderBox],
    })

  return (
    <HovercardProvider store={hovercardStore}>
      <HovercardAnchor
        render={(props) => (
          <button
            key={journalTemplate.id}
            className={styles.tile}
            disabled={loadingApplyTemplate || disabled}
            data-testid={testid}
            onClick={() => {
              applyJournalTemplate({
                variables: {
                  id: journalTemplate.id,
                  encounterId: encounterId,
                  existingJournalEntry: null,
                },
                onCompleted: (data) => {
                  // Focus on the newly created section
                  onApplyTemplate()
                  const sectionId = data.applyJournalTemplate.sections?.[0]?.id
                  if (sectionId) {
                    setFocusedBlockId(sectionId)
                  }
                },
                onError: () => {
                  onError()
                },
              })
            }}
            {...props}
          >
            <Text
              size="small"
              className={styles.label}
              title={journalTemplate.name}
            >
              {journalTemplate.name}
            </Text>
            <Label className={styles.subLabel}>
              {tEnum(`DocumentType.${journalTemplate.documentType}.label`)}
            </Label>
          </button>
        )}
      />
      <Hovercard className={styles.templateHovercard} portal>
        <Text size="large" weight="bold">
          {journalTemplate.name}
        </Text>
        <Text size="small">{journalTemplate.description}</Text>
      </Hovercard>
    </HovercardProvider>
  )
}
