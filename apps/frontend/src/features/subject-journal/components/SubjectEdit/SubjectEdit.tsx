import { <PERSON><PERSON><PERSON>, useEffect, useReducer } from "react"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"
import { ZodError, z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import DateInput from "components/DateInput/DateInput"
import Icon from "components/Icon/Icon"
import Select from "components/Select/Select"
import {
  Button,
  FormGrid,
  Heading,
  Input,
  PhoneNumberInput,
  notification,
} from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"
import { Center } from "ui/components/Layout/Center"
import { zodFieldValidation } from "utils/zodFieldValidation"

import {
  GenderId,
  SubjectEditQuery,
  useCreateAddressMutation,
  useCreateSubjectInteractionMutation,
  useSubjectEditQuery,
  useUpdateAddressMutation,
  useUpdateSubjectMutation,
} from "generated/graphql"

import {
  AddressEditForm,
  addressSchema,
} from "../AddressEditForm/AddressEditForm"
import styles from "./SubjectEdit.module.css"

// API doesn't allow updating birthDate & gender thus disabled. But included for future changes.
const subjectSchema = {
  // BE has more validations e.g. no numbers in name which are very unlikely to occur and should ideally be captured by FE and rendered (COMEBACK)
  name: z.string().min(3, "validationErrors.generic"),
  gender: z.string().min(1, "validationErrors.requiredField"),
  email: z
    .string()
    .email("validationErrors.generic")
    .or(z.literal(""))
    .nullable(),
  phoneNumber: z
    .string()
    // Phonenumber component handles validation internally BUT cannot prevent submit!
    // Since min/max are affected by country code etc this is just a naive simple check and needs to be optimised later (and ideally BE validated)
    .min(7, "validationErrors.generic")
    .or(z.literal(""))
    .nullable(),
  // backend takes care of precision validation, just checking for string
  birthDate: z.string().min(3, "validationErrors.generic"),
}

const formSchema = z.object({ ...subjectSchema, ...addressSchema })

type FormSchema = z.infer<typeof formSchema>

/* 
  The challenge here is to manage both forms from a single component, it would not be good UX to have 2x submit buttons. The forms therefor have to work in sync e.g. validation and submission.
  Additionally, Address is a separate entity in API and is created on demand thus uses 2x separate API calls Create/Update leading to more complexity.
*/
export type FormState = FormSchema & {
  validationResult: { success: boolean; error?: ZodError }
}

const SE = ({ data: { subject } }: { data: SubjectEditQuery }) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "manageSubject",
  })

  const [setSelectedSubject] = useCreateSubjectInteractionMutation({
    variables: { subjectId: subject.id },
  })

  // Editing Subject is considered as interaction although not in SJ. It is also helpful for user to immediately open Journal and or SubjectSummary.
  useEffect(() => {
    setSelectedSubject()
  }, [subject.id])

  const [updateSubjectMutation, { loading: updateSubjectLoading }] =
    useUpdateSubjectMutation()
  const [updateAddressMutation, { loading: updateAddressLoading }] =
    useUpdateAddressMutation()
  const [createAddressMutation, { loading: createAddressMutationLoading }] =
    useCreateAddressMutation()

  const isLoading =
    updateSubjectLoading || updateAddressLoading || createAddressMutationLoading

  const initialValues = {
    ...subject,
    ...(subject.address || {
      id: null,
      addressLine1: "",
      addressLine2: null,
      city: "",
      postalCode: "",
      region: null,
      country: "",
    }),
    validationResult: formSchema.safeParse(null),
  }

  const [formState, setFormState] = useReducer<
    (p: FormState, a: Partial<FormState>) => FormState
  >((prev, next) => ({ ...prev, ...next }), { ...initialValues })

  const selectGenderStore = useSelectStore({
    defaultValue: subject.gender,
  })

  const validateAndSubmit = () => {
    // we can only validate address if it has non-empty values
    const formHasSetAddressValues = Object.entries(formState).some(
      ([key, value]) => Object.keys(addressSchema).includes(key) && value
    )

    // If no Address then only validate Subject
    const validationResult = (
      formHasSetAddressValues ? formSchema : z.object(subjectSchema)
    ).safeParse(formState)

    setFormState({ validationResult })

    if (validationResult.success) {
      const { addressLine1, addressLine2, postalCode, city, region, country } =
        formState

      updateSubjectMutation({
        variables: {
          id: subject.id,
          input: {
            name: formState.name,
            email: { set: formState.email || null },
            phoneNumber: { set: formState.phoneNumber || null },
          },
        },
        onCompleted: () => {
          // Update Address when Subject has been updated to avoid wrong state of subject data

          // If address does not exist then create it
          if (subject.address === null && formHasSetAddressValues) {
            createAddressMutation({
              variables: {
                id: subject.id,
                input: {
                  addressLine1,
                  addressLine2,
                  postalCode,
                  city,
                  region,
                  country,
                },
              },
              onError: (error) =>
                notification.create({
                  status: "error",
                  message: error.message,
                }),
            })
          } else if (subject.address) {
            updateAddressMutation({
              variables: {
                id: subject.address.id,
                input: {
                  addressLine1,
                  addressLine2: { set: addressLine2 || null },
                  postalCode,
                  city,
                  region: { set: region || null },
                  country,
                },
              },
              onError: (error) =>
                notification.create({
                  status: "error",
                  message: error.message,
                }),
            })
          }

          notification.create({
            status: "success",
            message: tRoutes("subjectUpdateComplete"),
          })
        },
        onError: (error) =>
          notification.create({
            status: "error",
            message: error.message,
          }),
      })
    }
  }

  return (
    <Center className={styles.wrapper}>
      {/* Cannot use FormSections here to divide because it breaks Grid */}
      {/* Ideally, for each control there is an onBlur handler which resets validations but that is code bloat and suggests we need a proper form library */}
      <FormGrid
        colSpan={6}
        className={styles.fromGrid}
        onSubmit={(e: { preventDefault: () => void }) => {
          e.preventDefault()
          validateAndSubmit()
        }}
      >
        <Heading size="display" as="h1">
          {tRoutes("editSubject")}
        </Heading>

        <Heading as="h3">{tRoutes("basicInfo")}</Heading>

        <Input
          label={t("Name")}
          required
          value={formState.name}
          onChange={({ currentTarget }) =>
            setFormState({ name: currentTarget.value })
          }
          {...zodFieldValidation("name", formState.validationResult, tRoutes)}
        />

        <Select
          label="Gender"
          disabled
          required
          selectStore={selectGenderStore}
          options={Object.entries(GenderId).map(([label, value]) => ({
            label,
            value,
          }))}
          onSelectChange={(value) =>
            setFormState({ gender: value as GenderId })
          }
          {...zodFieldValidation("gender", formState.validationResult, tRoutes)}
        />

        <DateInput
          disabled
          label={tRoutes("dateOfBirth")}
          min={"1900-01-01"}
          max={new Date().toDateString()}
          value={formState.birthDate}
          onChange={(value) => setFormState({ birthDate: value })}
          {...zodFieldValidation(
            "birthDate",
            formState.validationResult,
            tRoutes
          )}
        />

        <PhoneNumberInput
          autoFocus={window.location.hash.includes("#phoneNumber")}
          label={tRoutes("phoneNumber")}
          value={formState.phoneNumber || ""}
          onChange={({ currentTarget }) =>
            setFormState({ phoneNumber: currentTarget.value })
          }
          {...zodFieldValidation(
            "phoneNumber",
            formState.validationResult,
            tRoutes
          )}
        />

        <Input
          type="email"
          label={t("email")}
          value={formState.email || ""}
          onChange={({ currentTarget }) =>
            setFormState({ email: currentTarget.value })
          }
          {...zodFieldValidation("email", formState.validationResult, tRoutes)}
        />

        <Heading as="h3">{tRoutes("address")}</Heading>
        <AddressEditForm
          subjectId={subject.id}
          formState={formState}
          setFormState={setFormState}
        />

        <FormFooter className={styles.formFooter}>
          <Button
            variant="outline"
            type="reset"
            onClick={(e: FormEvent<HTMLButtonElement>) => {
              e.preventDefault()
              setFormState({ ...initialValues })
            }}
          >
            {t("cancel")}
          </Button>
          <Button
            variant="filled"
            disabled={isLoading}
            icon={isLoading && <Icon name="loader-4-line" spin />}
            type="submit"
          >
            {t("Submit")}
          </Button>
        </FormFooter>
      </FormGrid>
    </Center>
  )
}

export default function SubjectEdit() {
  const { subjectId } = useParams<{ subjectId: string }>()

  const { data } = useSubjectEditQuery({
    variables: { id: subjectId || "" },
  })

  if (!subjectId || !data) return null

  // pass subjectId as key to reset the state, so we always show the correct data
  return <SE data={data} key={subjectId} />
}
