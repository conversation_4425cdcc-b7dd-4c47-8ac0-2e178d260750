.wrapper {
  position: sticky;
  top: calc(var(--header-height) + var(--grid-gap));

  grid-column: center-start / center-end;
  z-index: var(--z-index-sticky-header);

  margin: 0 -24px;
  /* prevents the bottom-border from disappearing when the encounter is scrolled out of view */
  margin-bottom: var(--encounter-header-wrapper-mb);

  --color-background-hover: var(--color-lev-blue-300);
  --color-background-active: var(--color-lev-blue-400);
}

.wrapper::before {
  content: "";
  position: absolute;
  top: calc(-1 * var(--grid-gap));
  left: calc(-1 * var(--grid-gap));
  right: calc(-1 * var(--grid-gap));
  bottom: 0;
  background-color: white;
  z-index: var(--z-index-behind);
}
.wrapper::after {
  content: "";
  position: absolute;
  left: 0;
  right: 0;
  bottom: -24px;
  height: 24px;
  background: linear-gradient(
    180deg,
    var(--encounter-background) 0%,
    color-mix(in srgb, var(--encounter-background) 50%, transparent 50%) 66%,
    rgba(0, 0, 0, 0) 100%
  );
  pointer-events: none;
}

.container {
  position: relative;
  gap: 8px;
  background-color: var(--encounter-background);

  padding-bottom: 8px;
  border-top-right-radius: var(--radius-button-half);
  border-top-left-radius: var(--radius-button-half);
}

.container::after {
  content: "";
  position: absolute;
  top: var(--encounter-negative-border-width);
  left: var(--encounter-negative-border-width);
  right: var(--encounter-negative-border-width);
  bottom: 0;
  border: var(--encounter-border);
  border-bottom: none;
  border-top-right-radius: calc(
    var(--radius-button-half) + 3px
  ); /* replaced the encounter-border-width with 3px, because it looks nicer, otherwise there's a small gap */
  border-top-left-radius: calc(var(--radius-button-half) + 3px);
  pointer-events: none;
}

.mainContent {
  padding: 0 24px;
  display: grid;
  gap: 8px;
}

.header {
  display: grid;
  grid-auto-columns: auto 1fr;
  grid-auto-flow: column;
  align-items: center;
  gap: 12px;
  --card-gap: 5px;
}

.header.withDisposition {
  grid-auto-columns: auto auto 1fr;
}

.popoverDisclosure {
  padding: 4px;
  margin-left: -4px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  cursor: pointer;
  border-radius: var(--radius-button-half);
  justify-self: start;
  max-width: 100%;
  transition: background-color 75ms;
}
.popoverDisclosure:hover {
  background-color: var(--color-lev-blue-300);
}
.popover {
  width: 600px;
  padding: 24px;
  background-color: white;
  border-radius: var(--radius-button-half);
  border-width: 2px;
  border-style: solid;
  border-color: var(--color-lev-blue);

  display: grid;
  grid-gap: 24px;
}

.heading {
  padding-top: 8px;
}

.informationContainer {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: -1px;
}

.informationItem {
  display: flex;
  gap: 8px;
}

.arrivalIndicator {
  padding: 0;
  margin: 0;
  color: var(--color-lev-blue);
  border-radius: 16px;
  height: 20px;
  width: 20px;
}

a.informationItem {
  color: var(--color-lev-blue);
}

a.informationItem:hover {
  background-color: var(--color-lev-blue-on-white-hover);
  padding: 4px;
  margin: -4px;
  border-radius: 16px;
  cursor: pointer;
}
