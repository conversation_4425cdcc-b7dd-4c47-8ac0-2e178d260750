import { Popover, PopoverDisclosure, usePopoverStore } from "@ariakit/react"
import { Ref, useState } from "react"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import ArrivalIndicator from "features/calendar/components/ArrivalIndicator/ArrivalIndicator"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import AddResponsibleProviders from "features/subject-journal/components/AddResponsibleProviders/AddResponsibleProviders"
import { useEncounterActions } from "hooks/useEncounterActions"
import { RouteStrings } from "routes/RouteStrings"
import { Button, ButtonText, Heading, Text } from "ui"
import { CardActionMenu } from "ui/components/CardActionMenu/CardActionMenu"
import { Dialog } from "ui/components/Dialog/Dialog"
import { EditableNote } from "ui/components/EditableNote/EditableNote"
import { isTypename } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  EncounterDisposition,
  EncounterFragmentFragment,
  EncounterStatus,
  EncounterStatusWithAllowedTransitions,
  ProviderInfoFragmentFragment,
  useAddResponsibleProvidersMutation,
  useDeleteResponsibleProvidersMutation,
  useUpdateEncounterDispositionMutation,
  useUpdateEncounterMutation,
} from "generated/graphql"

import EditableText from "../EditableText/EditableText"
import styles from "./EncounterHeader.module.css"

interface EncounterHeaderProps {
  id: string
  reason: string
  fromDate: string
  toDate: string | null
  primaryTeam: string
  teamMembers: ProviderInfoFragmentFragment[]
  responsibleProviders: ProviderInfoFragmentFragment[]
  note: string | null
  status: EncounterStatusWithAllowedTransitions
  disposition: EncounterDisposition | null
  canDiscardEncounter: boolean
  eventInstances: EncounterFragmentFragment["eventInstances"]
}

export default function EncounterHeader({
  id,
  reason,
  fromDate,
  toDate,
  primaryTeam,
  teamMembers,
  responsibleProviders,
  note,
  status,
  disposition,
  canDiscardEncounter,
  eventInstances,
}: EncounterHeaderProps) {
  const [showNoteForm, setShowNoteForm] = useState(false)

  const popoverStore = usePopoverStore({ placement: "bottom-start" })

  const { t } = useTranslation()
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const getCalendarPath = useGetCalendarPathObject()

  const {
    encounterTransitions,
    encounter: {
      isDialogForDeleteOpen,
      setIsDialogForDeleteOpen,
      discardEncounter,
    },
  } = useEncounterActions({
    encounterId: id,
  })

  const [updateEncounterDisposition] = useUpdateEncounterDispositionMutation()
  const [updateEncounter] = useUpdateEncounterMutation()
  const [addResponsibleProviders] = useAddResponsibleProvidersMutation()

  const [deleteResponsibleProviders] = useDeleteResponsibleProvidersMutation()

  // Date manipulation for the encounter header

  const toDateObj = toDate ? new Date(toDate) : null
  const fromDateObj = fromDate ? new Date(fromDate) : null

  const fromDateStr = fromDate && fromDateObj && dateFormat(fromDateObj)
  const toDateStr =
    toDate &&
    toDateObj &&
    fromDateStr !== dateFormat(toDateObj) &&
    dateFormat(toDateObj)

  const fromDateTimeStr = fromDate && fromDateObj && timeFormat(fromDateObj)
  const toDateTimeStr = toDate && toDateObj && timeFormat(toDateObj)

  const providers = responsibleProviders.map((rp) => rp.name).join(", ")

  const providersStr = providers && `: ${providers}`
  const teamAndProviders = `${primaryTeam}${providersStr}`

  const linkedEvent = eventInstances.length > 0 ? eventInstances[0] : null

  const linkedEventSubjects =
    linkedEvent?.participants.filter(isTypename("ParticipantSubject")) || []
  const linkedEventProviders =
    linkedEvent?.participants.filter(isTypename("ParticipantProvider")) || []

  const eventLink =
    linkedEvent && linkedEventProviders.length > 0
      ? getCalendarPath(RouteStrings.calendarViewEventInstance, {
          eventId: linkedEvent.id,
          search: {
            provider: linkedEventProviders[0].objId,
            date: linkedEvent.fromDate.split("T")[0],
          },
        })
      : ""

  const handleNoteSubmit = (value: string) => {
    updateEncounter({
      variables: {
        id,
        input: {
          note: value,
        },
      },
    })
    setShowNoteForm(false)
  }

  return (
    <div className={styles.wrapper}>
      <div className={styles.container}>
        <div className={styles.header}>
          <CardActionMenu
            fromDate={fromDate}
            toDate={toDate}
            allowedTransitions={status.allowedTransitions}
            disposition={disposition}
            status={status.status}
            updateEncounterStatus={(status) => encounterTransitions[status]()}
            // COMEBACK; misnomer, this is not "disposition"! disposition is only about discharge/transfer, see https://leviosa.atlassian.net/wiki/spaces/DOC/pages/130351124/Encounter
            updateEncounterDisposition={(disposition) =>
              updateEncounterDisposition({
                variables: {
                  updateEncounterDispositionId: id,
                  disposition,
                },
              })
            }
            canDiscardEncounter={canDiscardEncounter}
          />
          <PopoverDisclosure
            store={popoverStore}
            className={styles.popoverDisclosure}
            title={teamAndProviders}
            render={(p) => (
              <ButtonText
                {...p}
                as="button"
                ref={p.ref as Ref<HTMLButtonElement>}
              />
            )}
            disabled={status.status === EncounterStatus.Concluded}
          >
            {teamAndProviders}
          </PopoverDisclosure>
          <Popover
            hideOnEscape
            hideOnInteractOutside
            unmountOnHide
            portal
            store={popoverStore}
          >
            <div className={styles.popover}>
              <Heading size="large">Clinically responsible providers</Heading>
              <AddResponsibleProviders
                responsibleProviders={responsibleProviders.map((rp) => rp.id)}
                onRequestClose={() => popoverStore.hide()}
                members={teamMembers}
                onAddResponsibleProvider={(selected) =>
                  addResponsibleProviders({
                    variables: {
                      encounterId: id,
                      providerIds: [selected],
                    },
                  })
                }
                onDeleteResponsibleProvider={(selected) => {
                  deleteResponsibleProviders({
                    variables: {
                      encounterId: id,
                      providerIds: [selected],
                    },
                  })
                }}
              />
            </div>
          </Popover>
        </div>
        {/* COMEBACK ideally className should be placed on wrapper, not currently possible. */}
        <div className={styles.mainContent}>
          <EditableText
            as="h2"
            viewModeClassName={styles.heading}
            editModeClassName={styles.heading}
            textSize="large"
            isEditable={status.status !== EncounterStatus.Concluded}
            title={reason}
            wrapperElement={Heading}
            onSave={(value) =>
              updateEncounter({
                variables: { id, input: { reason: value } },
              })
            }
          />
          <div className={styles.informationContainer}>
            {linkedEvent ? (
              <>
                {[EncounterStatus.Planned, EncounterStatus.InProgress].includes(
                  status.status
                ) && (
                  <ArrivalIndicator
                    className={styles.arrivalIndicator}
                    subjectParticipants={linkedEventSubjects}
                  />
                )}
                <Tooltip tooltipContent={t("Click to open event in calendar")}>
                  <Text
                    className={styles.informationItem}
                    size="small"
                    as={Link}
                    to={eventLink}
                  >
                    <Icon name="calendar-2-line" fontSize={16} />
                    {fromDateStr}
                    {toDateStr && ` - ${toDateStr}`}, {fromDateTimeStr}{" "}
                    {toDateTimeStr && ` - ${toDateTimeStr}`}
                  </Text>
                </Tooltip>
              </>
            ) : (
              <Text className={styles.informationItem} size="small">
                {fromDateStr}
                {toDateStr && ` - ${toDateStr}`}, {fromDateTimeStr}{" "}
                {toDateTimeStr && ` - ${toDateTimeStr}`}
              </Text>
            )}
          </div>
          <EditableNote
            note={note}
            showForm={showNoteForm}
            setShowForm={setShowNoteForm}
            disabled={status.status === EncounterStatus.Concluded}
            handleNoteSubmit={handleNoteSubmit}
            size="small"
          />
        </div>
      </div>

      <Dialog
        title={t("Are you sure you want to delete this encounter?")}
        isOpen={isDialogForDeleteOpen}
        onClose={() => {
          setIsDialogForDeleteOpen(false)
        }}
        actions={
          <>
            <Button onClick={() => setIsDialogForDeleteOpen(false)}>
              {t("Cancel")}
            </Button>
            <Button onClick={() => discardEncounter()} variant="filled">
              {t("Delete")}
            </Button>
          </>
        }
      ></Dialog>
    </div>
  )
}
