import c from "classnames"
import { orderBy } from "lodash"
import { useTranslation } from "react-i18next"
import { v4 as uuid } from "uuid"

import Panel from "components/Panel/Panel"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { AddBillingItem } from "features/subject-journal/components/BillingSupplement/AddBillingItem/AddBillingItem"
import BillingSupplementInvoice from "features/subject-journal/components/BillingSupplement/BillingSupplementInvoice/BillingSupplementInvoice"
import { color } from "styles/colors"
import { Center, Heading, Text, TextWithIcon } from "ui"

import {
  EncounterFragmentFragment,
  PermissionKey,
  useCreateInvoiceWithLineMutation,
} from "generated/graphql"

import styles from "./EncounterBilling.module.css"

type EncounterBillingProps = {
  encounterId: string
  invoices: EncounterFragmentFragment["invoices"]
  canCreateInvoice: boolean
}

export default function EncounterBilling({
  encounterId,
  invoices,
  canCreateInvoice,
}: EncounterBillingProps) {
  const { t } = useTranslation()

  const [createInvoiceWithLine, { error, loading }] =
    useCreateInvoiceWithLineMutation()

  return (
    <Restricted to={PermissionKey.BillingBillingCodeView}>
      <Center
        className={c(styles.billing, color.light)}
        id={`${encounterId}-billing`}
      >
        <Heading>{t("Billing")}</Heading>
        {orderBy(invoices, ["invoiceNumber"]).map((invoice) => (
          <BillingSupplementInvoice
            key={invoice.id}
            invoice={invoice}
            encounterId={encounterId}
            canCreateInvoice={canCreateInvoice}
            draftInvoices={invoices.filter(
              (i) => i.issued === false && i.id !== invoice.id
            )}
          />
        ))}
        {canCreateInvoice && (
          <div className={styles.billingFooter}>
            <Heading size="xsmall">{t("New Invoice")}</Heading>
            <Restricted to={PermissionKey.BillingInvoiceCreate}>
              {error && (
                <Panel status="warning">
                  <TextWithIcon iconName="alert-line">
                    {t(
                      "Could not create invoice. Please try again. If the problem persists, please contact support."
                    )}
                    <Text size="small" className={styles.createError}>
                      {error.message}
                    </Text>
                  </TextWithIcon>
                </Panel>
              )}

              <AddBillingItem
                className={styles.addItem}
                newInvoice
                error={error?.message}
                isLoading={loading}
                onSelectItem={(billingCodeId, billingCodeType) => {
                  const invoiceId = uuid()
                  createInvoiceWithLine({
                    variables: {
                      invoiceId,
                      encounterId,
                      billingCodeId,
                      billingCodeType,
                      quantity: 1,
                    },
                  })
                }}
              />
            </Restricted>
          </div>
        )}
      </Center>
    </Restricted>
  )
}
