import c from "classnames"
import { createContext, ReactNode } from "react"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Panel from "components/Panel/Panel"
import Restricted from "features/authentication/components/Restricted/Restricted"
import EncounterBilling from "features/subject-journal/components/Encounter/EncounterBilling"
import { JournalGrid } from "ui"
import { Center } from "ui/components/Layout/Center"
import { mergeRefs } from "ui/lib/mergeRefs"
import { isTypename } from "utils/isTypename"

import {
  EncounterFragmentFragment,
  EncounterStatus,
  JournalEntryBlockStatus,
  JournalEntryStatus,
  JournalTemplateFragmentFragment,
  LeviosaKindId,
  PermissionKey,
} from "generated/graphql"

import { useJournalDataInView } from "../JournalDataInView/JournalDataInView.context"
import { useJournalBlockFocus } from "../JournalFocus/JournalFocus"
import NewJournalEntryBanner from "../NewJournalEntryBanner/NewJournalEntryBanner"
import styles from "./Encounter.module.css"
import EncounterHeader from "./EncounterHeader"

const EncounterContext = createContext<
  (EncounterFragmentFragment & { resolutionNoteId: string | null }) | null
>(null)

export type EncounterProps = {
  journalTemplates: JournalTemplateFragmentFragment[]
  children: ReactNode
} & EncounterFragmentFragment

export default function Encounter({
  journalTemplates,
  children,
  ...encounter
}: EncounterProps) {
  const { globalData } = useGlobalState()
  const { leviosaKindId } = globalData.config

  const resolutionNoteId: string | null =
    encounter.journalEntries
      .flatMap((e) => e.sections || [])
      .filter(isTypename("Note"))
      .find((b) => b.isResolutionNote)?.id || null

  const availableJournalTemplates = journalTemplates.filter(
    ({ sections }) => !(sections[0]?.isResolutionNote && resolutionNoteId)
  )

  const {
    id,
    reason,
    fromDate,
    toDate,
    primaryTeam,
    responsibleProviders,
    disposition,
    note,
    status,
    journalEntries,
    invoices,
    closedAt,
  } = encounter

  const concludedOrCancelled =
    status.status === EncounterStatus.Concluded ||
    status.status === EncounterStatus.Cancelled

  const canDiscardEncounter = journalEntries.every(
    (entry) =>
      entry.blocks.every((el) => el.status === JournalEntryBlockStatus.Draft) &&
      entry.status !== JournalEntryStatus.Completed
  )

  // Date one week ago
  const oneWeekAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)

  const concludedLessThanWeekAgo =
    concludedOrCancelled && closedAt && new Date(closedAt) > oneWeekAgo

  // User can create invoice for:
  // 1. All encounters that are not concluded or cancelled
  // 2. Concluded encounters that were closed within a week from now
  const canCreateInvoice = !concludedOrCancelled || concludedLessThanWeekAgo

  // When encounters are concluded or cancelled more than a week ago and don't have any invoices, we hide the billing component
  const hideBilling =
    concludedOrCancelled && !concludedLessThanWeekAgo && invoices.length === 0

  const { useJournalDataInViewRef } = useJournalDataInView()

  const journalDataRef = useJournalDataInViewRef(id)
  const { ref: refFocus, isFocused } = useJournalBlockFocus<HTMLDivElement>({
    id,
  })

  return (
    <EncounterContext.Provider value={{ ...encounter, resolutionNoteId }}>
      <JournalGrid
        ref={mergeRefs([journalDataRef, refFocus])}
        rowGap
        id={id}
        data-id={id}
        className={c(
          "inboundData",
          styles.encounter,
          isFocused && styles.focused,
          concludedOrCancelled && styles.concluded
        )}
      >
        <EncounterHeader
          id={id}
          reason={reason}
          fromDate={fromDate}
          toDate={toDate}
          primaryTeam={primaryTeam.name}
          teamMembers={[...primaryTeam.members]}
          responsibleProviders={[...responsibleProviders]}
          note={note || null}
          status={status}
          disposition={disposition}
          canDiscardEncounter={canDiscardEncounter}
          eventInstances={encounter.eventInstances}
        />

        {children}

        <Center className={styles.addJournalRow} id={`${id}-end`}>
          <Restricted to={PermissionKey.SubjectJournalInterventionPeriodEdit}>
            {!concludedOrCancelled && (
              <Panel className={styles.encounterPanel}>
                <NewJournalEntryBanner
                  journalTemplates={availableJournalTemplates}
                  encounterId={id}
                />
              </Panel>
            )}
          </Restricted>
        </Center>

        {leviosaKindId !== LeviosaKindId.Lite && !hideBilling && (
          <EncounterBilling
            encounterId={id}
            invoices={invoices}
            canCreateInvoice={!!canCreateInvoice}
          />
        )}

        <Center className={styles.backdrop} />
      </JournalGrid>
    </EncounterContext.Provider>
  )
}
