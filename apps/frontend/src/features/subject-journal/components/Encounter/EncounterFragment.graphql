fragment EncounterFragment on Encounter {
  id
  reason
  status {
    allowedTransitions
    status
  }
  fromDate
  toDate
  disposition
  note
  closedAt
  journalEntries {
    ...JournalEntryFragment
  }
  subject {
    id
    subjectHealthProfile {
      id
      dietaryAllowanceId
    }
  }
  primaryTeam {
    id
    name
    members {
      ...ProviderInfoFragment
    }
  }

  invoices {
    ...EncounterInvoiceFragment
  }

  responsibleProviders {
    ...ProviderInfoFragment
  }

  eventInstances {
    id
    fromDate
    toDate
    participants {
      objId
      participantId
      attendanceState {
        id
        state
      }
      ... on ParticipantSubject {
        subject {
          id
          name
        }
      }
    }
  }
}
