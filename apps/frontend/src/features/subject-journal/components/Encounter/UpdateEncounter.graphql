mutation UpdateEncounter($id: UUID!, $input: EncounterUpdateInput!) {
  updateEncounter(id: $id, input: $input) {
    id
    reason
    note
    priority
    interventionPeriod {
      id
      journalData {
        ... on Encounter {
          id
        }
        ... on InboundData {
          id
        }
      }
    }
    subject {
      id
      location {
        id
        label
      }
    }
  }
}
