.billing {
  composes: addJournalRow from "./Encounter.module.css";
  margin-top: -42px -24px 0;
  padding: 30px 16px 16px;
  border-radius: 0 0 var(--radius-button-half) var(--radius-button-half);
  scroll-margin-block: 280px;
}

.billingFooter {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px 16px;
  padding: 16px 0 0;
}
.billingFooter > * {
  grid-column: 1 / -1;
}

.addItem {
  grid-column: 1;
  align-self: center;
}
.createError {
  margin-top: 8px;
}
.createInvoice {
  grid-column: 2;
  justify-self: end;
}
