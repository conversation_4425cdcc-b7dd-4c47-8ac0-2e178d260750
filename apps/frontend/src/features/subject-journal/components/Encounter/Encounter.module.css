.encounter {
  --encounter-border-width: 2px;
  --encounter-negative-border-width: -2px;
  --encounter-border: none;
  --encounter-header-wrapper-mb: 40px;
  --encounter-background: var(--color-lev-blue-200);
}

.encounter.concluded {
  --encounter-background: var(--color-neutral-200);
}

.encounter.focused {
  --encounter-border: var(--encounter-border-width) solid var(--color-500);
}

.heading {
  padding-top: 16px;
  padding-bottom: 8px;
}
.addJournalRow.addJournalRow {
  display: flex;
  flex-direction: column;
  gap: 16px;
  position: relative;
  margin: 0 -24px;
  z-index: 1;
  /* clear the margin-bottom defined in the EncounterHeader .wrapper */
  margin-top: calc(-1 * var(--encounter-header-wrapper-mb));
}
.backdrop.backdrop {
  grid-row: 1 / span 10000;
  position: absolute;
  width: calc(100% + 48px);
  border-radius: var(--radius-button-half);
  margin: 0 -24px;
  top: 0;
  bottom: 0;
  left: 0;
  pointer-events: none;
}
.backdrop::before,
.backdrop::after {
  content: "";
  display: block;
  position: absolute;
  border-radius: inherit;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.backdrop::before {
  background-color: var(--encounter-background);
  z-index: -1;
}
.backdrop::after {
  z-index: 1;
  border: var(--encounter-border);
  top: var(--encounter-negative-border-width);
  left: var(--encounter-negative-border-width);
  right: var(--encounter-negative-border-width);
  bottom: var(--encounter-negative-border-width);
}

.addJournalRow .encounterPanel {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
  border: unset;
  padding-top: 0;
  border-top-left-radius: unset;
  border-top-right-radius: unset;
}
