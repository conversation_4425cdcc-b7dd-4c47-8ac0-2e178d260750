import { ReactNode } from "react"
import { useTranslation } from "react-i18next"

import { Text } from "ui"
import Supplement from "ui/components/Supplement/Supplement"
import { SupportedLanguages } from "utils/SupportedLanguages"

import {
  JournalEntryBlockStatus,
  MedicalCertificateSupplementFragmentFragment,
} from "generated/graphql"

import { FreeTextForm } from "./FreeTextForm/FreeTextForm"
import styles from "./JournalEntryFreeText.module.css"
import { ViewFreeText } from "./ViewFreeText/ViewFreeText"

type JournalEntryFreeTextProps =
  MedicalCertificateSupplementFragmentFragment & {
    languageId: SupportedLanguages
    canEdit: boolean
    onDelete?: () => void
    contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
  }

const FreeTextSupplement = ({
  id,
  status,
  children,
  canEdit,
  contentJson,
}: {
  id: string
  status: JournalEntryBlockStatus
  children: ReactNode
  canEdit: boolean
  contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
}) => {
  const { t } = useTranslation()

  const isCompleted = status === JournalEntryBlockStatus.Completed

  return (
    <Supplement
      id={id}
      icon="file-list-line"
      heading={contentJson.title || t("Untitled document")}
      minimizedHeading={
        <div>
          {contentJson.title || t("Untitled document")}

          {!isCompleted && (
            <Text as="span" className={styles.status}>
              {canEdit ? t("Draft") : t("Saved")}
            </Text>
          )}
        </div>
      }
      color="blue"
      isMinimized={status === JournalEntryBlockStatus.Completed || !canEdit}
      confirmed={status === JournalEntryBlockStatus.Completed}
    >
      {children}
    </Supplement>
  )
}

export const JournalEntryFreeText = ({
  id,
  canEdit,
  status,
  onDelete,
  contentJson,
  updatedAt,
}: JournalEntryFreeTextProps) => {
  const showForm = canEdit && status !== JournalEntryBlockStatus.Completed

  return (
    <FreeTextSupplement
      id={id}
      status={status}
      canEdit={canEdit}
      contentJson={contentJson}
    >
      {showForm ? (
        <FreeTextForm
          id={id}
          onDelete={onDelete}
          contentJson={contentJson}
          updatedAt={updatedAt}
        />
      ) : (
        <ViewFreeText id={id} status={status} contentJson={contentJson} />
      )}
    </FreeTextSupplement>
  )
}
