import { useTranslation } from "react-i18next"

import { AttachmentButton } from "components/AttachmentButton/AttachmentButton"
import { AttachmentsPreview } from "components/AttachmentsPreview/AttachmentsPreview"
import { getTokens } from "features/authentication/utils/tokenStorage"
import { FormGrid, Label, Text } from "ui"

import {
  JournalEntryBlockStatus,
  MedicalCertificateSupplementFragmentFragment,
} from "generated/graphql"

import styles from "./ViewFreeText.module.css"

// Initial values should come from backend
type ViewFreeTextProps = {
  id: string
  status: JournalEntryBlockStatus
  contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
}

export const ViewFreeText = ({
  id,
  contentJson,
  status,
}: ViewFreeTextProps) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal",
  })
  const baseUrl = import.meta.env.VITE_BACKEND_API_URL || window.origin

  const { title, description } = contentJson

  const { accessToken } = getTokens() || {}

  const url = `${baseUrl}/api/pdf/free-text-document/${encodeURIComponent(
    id
  )}?token=${encodeURIComponent(accessToken || "")}`

  const attachments = [
    {
      url,
      name: `${title || "Document"}.pdf`,
    },
  ]

  return (
    <div className={styles.wrap}>
      <FormGrid as="div" colSpan={12} className={styles.content}>
        <div className={styles.fieldValueWrap}>
          <Label>{t("Document Text")}</Label>
          <Text size="small" className="whitespace-pre-wrap">
            {description || "-"}
          </Text>
        </div>
        <div>
          {status === JournalEntryBlockStatus.Completed && (
            <AttachmentsPreview attachments={attachments}>
              <AttachmentButton fileUrl={url} extension="pdf" color="blue">
                {tRoutes("viewDocument")}
              </AttachmentButton>
            </AttachmentsPreview>
          )}
        </div>
      </FormGrid>
    </div>
  )
}
