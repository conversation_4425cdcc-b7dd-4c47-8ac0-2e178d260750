import { useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FormGrid, Input, notification, Textarea } from "ui"

import {
  MedicalCertificateSupplementFragmentFragment,
  useCompleteMedicalCertificateMutation,
  useUpdateMedicalCertificateMutation,
} from "generated/graphql"

import SaveStatus, { getSaveStatus } from "../../SaveStatus/SaveStatus"
import styles from "./FreeTextForm.module.css"

type FreeTextFormProps = {
  onDelete?: () => void
  contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
  id: string
  updatedAt: string
}

export const contentSchema = z.object({
  title: z.string().optional(),
  description: z.string(),
})

export const FreeTextForm = ({
  id,
  onDelete,
  updatedAt,
  contentJson,
}: FreeTextFormProps) => {
  const { t } = useTranslation()
  const [isContentSync, setIsContentSync] = useState<boolean>(true)

  const [_updateMedicalCertificate, { loading, error, called }] =
    useUpdateMedicalCertificateMutation()

  const [completeMedicalCertificate] = useCompleteMedicalCertificateMutation({
    onCompleted: () => {
      notification.create({
        message: t("Freetext document submitted"),
        status: "success",
      })
    },
  })

  const updateMedicalCertificate = async ({
    contentKey,
    value,
  }: {
    contentKey: keyof z.infer<typeof contentSchema>
    value: string
  }) => {
    await _updateMedicalCertificate({
      variables: {
        updateMedicalCertificateId: id,
        input: {
          content: {
            ...contentJson,
            [contentKey]: value,
          },
        },
      },
    })

    setIsContentSync(true)
  }

  const saveStatus = getSaveStatus({
    isLoading: loading,
    isError: !!error,
    isCalled: called,
    isContentSync,
  })

  return (
    <FormGrid
      colSpan={12}
      onSubmit={(e: { preventDefault: () => void }) => {
        e.preventDefault()

        completeMedicalCertificate({
          variables: {
            completeMedicalCertificateId: id,
          },
        })
      }}
    >
      <Input
        label={t("Document Title")}
        defaultValue={contentJson.title}
        data-testid="document-title"
        onBlur={(e) => {
          updateMedicalCertificate({
            contentKey: "title",
            value: e.target.value,
          })
        }}
        onChange={() => setIsContentSync(false)}
        required
      />

      <Textarea
        rows={3}
        defaultValue={contentJson.description}
        label={t("Document Text")}
        data-testid="document-description"
        onBlur={(e) => {
          updateMedicalCertificate({
            contentKey: "description",
            value: e.target.value,
          })
        }}
        onChange={() => setIsContentSync(false)}
        required
      />

      <FormFooter>
        <SaveStatus
          className={styles.saveStatus}
          status={saveStatus}
          updatedAt={updatedAt}
        />

        {onDelete && (
          <Button
            variant="clear"
            data-testid="document-delete"
            onClick={onDelete}
          >
            {t("Delete")}
          </Button>
        )}

        <Button type="submit" data-testid="document-submit" variant="filled">
          {t("Submit")}
        </Button>
      </FormFooter>
    </FormGrid>
  )
}
