import { ReactNode, createContext, useContext } from "react"
import { useInView } from "react-intersection-observer"
import { useImmer } from "use-immer"

type JournalDataInViewContextType = {
  firstVisibleJournalData: string | null
  markJournalDataInView: (id: string, inView: boolean) => void
  useJournalDataInViewRef: (id: string) => (node: HTMLElement | null) => void
}

const JournalDataInView = createContext<JournalDataInViewContextType | null>(
  null
)

export const useJournalDataInView = () => {
  const journalDataInView = useContext(JournalDataInView)
  if (!journalDataInView) {
    throw new Error(
      "journalDataInView has to be used within <JournalDataInView.Provider>"
    )
  }

  return journalDataInView
}

export const JournalDataInViewProvider = ({
  children,
  journalDataIds,
}: {
  children: ReactNode
  journalDataIds: string[]
}) => {
  const [visibleJournalData, setVisibleJournalData] = useImmer<
    Record<string, boolean>
  >({})

  const firstVisibleJournalData =
    journalDataIds.find((id) =>
      Object.entries(visibleJournalData)
        .filter(([, isVisible]) => isVisible)
        .map(([key]) => key)
        .includes(id)
    ) || null

  const markJournalDataInView = (id: string, inView: boolean) =>
    setVisibleJournalData((draft) => {
      draft[id] = inView
    })

  const useJournalDataInViewRef = (id: string) => {
    const { ref } = useInView({
      rootMargin: "-80px 0px 0px 0px",
      onChange: (inView) => {
        markJournalDataInView(id, inView)
      },
    })

    return ref
  }

  return (
    <JournalDataInView.Provider
      value={{
        firstVisibleJournalData,
        markJournalDataInView,
        useJournalDataInViewRef,
      }}
    >
      {children}
    </JournalDataInView.Provider>
  )
}
