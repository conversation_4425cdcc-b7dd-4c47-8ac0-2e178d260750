import dayjs from "dayjs"
import { t } from "i18next"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import DatePicker from "components/DatePicker/DatePicker"
import Panel from "components/Panel/Panel"
import { Button, FormGrid, Input, Textarea, notification, FormFooter } from "ui"

import {
  MedicalCertificateSupplementFragmentFragment,
  useCompleteMedicalCertificateMutation,
  useUpdateMedicalCertificateMutation,
} from "generated/graphql"

import SaveStatus, { getSaveStatus } from "../../SaveStatus/SaveStatus"
import styles from "./MedicalCertificateForm.module.css"

type MedicalCertificateFormProps = {
  version: number
  contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
  id: string
  onDelete?: () => void
  updatedAt: string
}

export const contentSchema = z
  .object({
    employerOrSchool: z.string().optional(),
    fromDate: z.string(),
    toDate: z.string().optional(),
    description: z.string().optional(),
  })
  .refine(
    (data) => {
      const { fromDate, toDate } = data

      if (!toDate) return true

      return (
        dayjs(fromDate).isBefore(dayjs(toDate)) ||
        dayjs(fromDate).isSame(dayjs(toDate))
      )
    },
    {
      message: t('toDate cannot be before fromDate"'),
    }
  )

type ContentKey = keyof z.infer<typeof contentSchema>

type UpdateMedicalCertificateArg = {
  contentKey: ContentKey
  value: string
}

export const MedicalCertificateForm = ({
  contentJson,
  version,
  id,
  updatedAt,
  onDelete,
}: MedicalCertificateFormProps) => {
  const { t } = useTranslation()
  const [errorMessage, setErrorMessage] = useState<string | null>(null)
  const [isContentSync, setIsContentSync] = useState<boolean>(true)
  // Temprary hack. We need generic way to handle this with hook. Should be same as in DrugPrescriptionForm
  const [updatedContentJson, setContentJson] = useState({
    ...contentJson,
    fromDate: contentJson?.fromDate || dayjs().format("YYYY-MM-DD"),
    toDate: contentJson?.toDate || dayjs().format("YYYY-MM-DD"),
  })

  const [_updateMedicalCertificate, { loading, error, called }] =
    useUpdateMedicalCertificateMutation()

  const [completeMedicalCertificate] = useCompleteMedicalCertificateMutation({
    onCompleted: () => {
      notification.create({
        message: t("Medical certificate submitted"),
        status: "success",
      })
    },
  })

  if (version === 1) {
    const updateMedicalCertificate = ({
      contentKey,
      value,
    }: UpdateMedicalCertificateArg) => {
      setErrorMessage(null)

      const result = contentSchema.safeParse({
        ...updatedContentJson,
        [contentKey]: value,
      })

      setContentJson((prev) => ({
        ...prev,
        [contentKey]: value,
      }))

      if (!result.success) return

      _updateMedicalCertificate({
        variables: {
          updateMedicalCertificateId: id,
          input: {
            content: result.data,
          },
        },
      })

      setIsContentSync(true)
    }

    const saveStatus = getSaveStatus({
      isLoading: loading,
      isError: !!error,
      isCalled: called,
      isContentSync,
    })

    return (
      <FormGrid
        colSpan={12}
        onSubmit={(e: { preventDefault: () => void }) => {
          e.preventDefault()

          const result = contentSchema.safeParse({
            ...updatedContentJson,
          })

          if (!result.success) {
            setErrorMessage(result.error.message)
            return
          }

          completeMedicalCertificate({
            variables: {
              completeMedicalCertificateId: id,
            },
          })
        }}
      >
        <Input
          defaultValue={contentJson?.employerOrSchool}
          data-testid="certificate-employer-school"
          onBlur={(e) => {
            updateMedicalCertificate({
              contentKey: "employerOrSchool",
              value: e.target.value,
            })
          }}
          onChange={() => setIsContentSync(false)}
          label={t("Employer/school")}
        />

        <DatePicker
          value={updatedContentJson.fromDate}
          label={t("From")}
          required
          onBlur={(e) => {
            updateMedicalCertificate({
              contentKey: "fromDate",
              value: e.target.value,
            })
          }}
          className={styles.date}
          data-testid="certificate-from-date"
          onChange={(e) => {
            setContentJson((prev) => ({
              ...prev,
              fromDate: e.target.value,
            }))
          }}
        />

        <DatePicker
          value={updatedContentJson.toDate}
          label={t("to")}
          onBlur={(e) => {
            updateMedicalCertificate({
              contentKey: "toDate",
              value: e.target.value,
            })
          }}
          data-testid="certificate-to-date"
          onChange={(e) => {
            setContentJson((prev) => ({
              ...prev,
              toDate: e.target.value,
            }))
          }}
          className={styles.date}
          status={
            dayjs(updatedContentJson.fromDate).isAfter(
              dayjs(updatedContentJson.toDate)
            )
              ? "error"
              : "default"
          }
          hideStatusIcon
          message={
            dayjs(updatedContentJson.fromDate).isAfter(
              dayjs(updatedContentJson.toDate)
            )
              ? t("toDate cannot be before fromDate")
              : undefined
          }
        />

        <Textarea
          defaultValue={contentJson?.description}
          rows={3}
          label={t("Description")}
          data-testid="certificate-description"
          onBlur={(e) => {
            updateMedicalCertificate({
              contentKey: "description",
              value: e.target.value,
            })
          }}
          onChange={() => setIsContentSync(false)}
        />

        {errorMessage && <Panel status="error">{errorMessage}</Panel>}

        <FormFooter>
          <SaveStatus
            className={styles.saveStatus}
            status={saveStatus}
            updatedAt={updatedAt}
          />

          {onDelete && (
            <Button
              variant="clear"
              data-testid="certificate-delete"
              onClick={onDelete}
            >
              {t("Delete")}
            </Button>
          )}

          <Button
            type="submit"
            data-testid="certificate-submit"
            variant="filled"
          >
            {t("Submit")}
          </Button>
        </FormFooter>
      </FormGrid>
    )
  }

  return null
}
