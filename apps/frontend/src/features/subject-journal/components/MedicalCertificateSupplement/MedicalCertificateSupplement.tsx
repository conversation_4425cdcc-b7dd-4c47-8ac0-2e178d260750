import { useTranslation } from "react-i18next"

import { Text } from "ui"
import Supplement from "ui/components/Supplement/Supplement"
import { SupportedLanguages } from "utils/SupportedLanguages"

import {
  JournalEntryBlockStatus,
  MedicalCertificateSupplementFragmentFragment,
} from "generated/graphql"

import { MedicalCertificateForm } from "./MedicalCertificateForm/MedicalCertificateForm"
import styles from "./MedicalCertificateSupplement.module.css"
import { ViewMedicalCertificate } from "./ViewMedicalCertificate/ViewMedicalCertificate"

type MedicalCertificateSupplementProps =
  MedicalCertificateSupplementFragmentFragment & {
    languageId: SupportedLanguages
    canEdit: boolean
    onDelete?: () => void
  }

export default function MedicalCertificateSupplement({
  id,
  contentJson,
  version,
  canEdit,
  updatedAt,
  status,
  onDelete,
}: MedicalCertificateSupplementProps) {
  const { t } = useTranslation()

  const isCompleted = status === JournalEntryBlockStatus.Completed
  const showForm = canEdit && !isCompleted

  return (
    <Supplement
      id={id}
      icon={"file-list-line"}
      heading={t("Medical certificate")}
      subHeading={t("Due to absence from work or school")}
      minimizedHeading={
        <div>
          {t("Medical certificate")}

          {!isCompleted && (
            <Text as="span" className={styles.status}>
              {canEdit ? t("Draft") : t("Saved")}
            </Text>
          )}
        </div>
      }
      color="blue"
      isMinimized={status === JournalEntryBlockStatus.Completed || !canEdit}
      confirmed={status === JournalEntryBlockStatus.Completed}
    >
      {showForm ? (
        <MedicalCertificateForm
          id={id}
          version={version}
          contentJson={contentJson}
          onDelete={onDelete}
          updatedAt={updatedAt}
        />
      ) : (
        <ViewMedicalCertificate
          id={id}
          version={version}
          contentJson={contentJson}
          status={status}
        />
      )}
    </Supplement>
  )
}
