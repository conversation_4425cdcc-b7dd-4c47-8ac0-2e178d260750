import { format } from "date-fns"
import { useTranslation } from "react-i18next"

import { AttachmentButton } from "components/AttachmentButton/AttachmentButton"
import { AttachmentsPreview } from "components/AttachmentsPreview/AttachmentsPreview"
import { getTokens } from "features/authentication/utils/tokenStorage"
import { FormGrid, LabeledValue } from "ui"

import {
  JournalEntryBlockStatus,
  MedicalCertificateSupplementFragmentFragment,
} from "generated/graphql"

import { contentSchema } from "../MedicalCertificateForm/MedicalCertificateForm"
import styles from "./ViewMedicalCertificate.module.css"

type MedicalCertificateFormProps = {
  version: number
  contentJson: MedicalCertificateSupplementFragmentFragment["contentJson"]
  id: string
  status: JournalEntryBlockStatus
}

const formatDate = (date?: string) => {
  if (!date) return "-"

  return format(new Date(date), "dd.MM.yyyy")
}

export const ViewMedicalCertificate = ({
  contentJson,
  id,
  status,
}: MedicalCertificateFormProps) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal",
  })

  const baseUrl = import.meta.env.VITE_BACKEND_API_URL || window.origin

  const parsedContent = contentSchema.parse(contentJson)

  const { accessToken } = getTokens() || {}

  const url = `${baseUrl}/api/pdf/medical-certificate/${encodeURIComponent(
    id
  )}?token=${encodeURIComponent(accessToken || "")}`

  const validDate =
    formatDate(parsedContent.fromDate) +
    " - " +
    (formatDate(parsedContent.toDate) || "-")

  const attachments = [
    {
      url,
      name: "Læknisvottorð vegna fjarvista.pdf",
    },
  ]

  return (
    <div className={styles.wrap}>
      <FormGrid as="dl" colSpan={12} className={styles.content}>
        <LabeledValue
          label={t("Employer or school")}
          value={parsedContent.employerOrSchool}
          className={styles.span6}
        />

        <LabeledValue
          label={t("Valid")}
          value={validDate}
          className={styles.span6}
        />

        <LabeledValue
          label={t("Description")}
          value={parsedContent.description}
          contentClassName="whitespace-pre-wrap"
        />

        <div>
          {status === JournalEntryBlockStatus.Completed && (
            <AttachmentsPreview attachments={attachments}>
              <AttachmentButton
                fileUrl={url}
                color="blue"
                extension="pdf"
                isSensitiveData
              >
                {tRoutes("viewDocument")}
              </AttachmentButton>
            </AttachmentsPreview>
          )}
        </div>
      </FormGrid>
    </div>
  )
}
