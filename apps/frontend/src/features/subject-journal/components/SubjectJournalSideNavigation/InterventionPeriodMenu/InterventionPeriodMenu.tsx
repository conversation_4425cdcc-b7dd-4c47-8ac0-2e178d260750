import { useTranslation } from "react-i18next"

import { <PERSON>u, MenuButton, MenuProvider } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import Icon from "components/Icon/Icon"

type InterventionPeriodMenuProps = {
  className?: string
  onCreateNewEncounter: () => void
}

export default function InterventionPeriodMenu({
  className = "",
  onCreateNewEncounter,
}: InterventionPeriodMenuProps) {
  const { t } = useTranslation()

  return (
    <MenuProvider placement="bottom-end">
      <MenuButton
        className={className}
        size="large"
        aria-label="Intervention Period Menu"
        icon={<Icon name="more-line" />}
        variant="clear"
      />
      <Menu gutter={8} portal>
        <MenuItem onClick={onCreateNewEncounter}>
          {t("Add New Encounter")}
        </MenuItem>
      </Menu>
    </MenuProvider>
  )
}
