// import { PopoverDisclosure, usePopoverStore } from "components/Ariakit"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import usePermissions from "features/authentication/hooks/usePermissions"
import { PowerMenuGroupKey } from "features/power-menu/lib/enums/PowerMenuGroupKeys.enum"
import { usePowerMenuGroups } from "features/power-menu/usePowerMenuGroups"
import { Heading } from "ui"
import Button from "ui/components/Button/Button"
import {
  SideNavGroup,
  SideNavList,
  SideNavWrap,
} from "ui/components/SideNav/SideNav"
import { isTypename } from "utils/isTypename"

import {
  GetSubjectJournalQuery,
  PermissionKey,
  useUpdateInterventionPeriodMutation,
} from "generated/graphql"

import EditableText from "../EditableText/EditableText"
import InterventionPeriodMenu from "./InterventionPeriodMenu/InterventionPeriodMenu"
// import { InvoicePopoverFilter } from "./InvoicePopoverFilter/InvoicePopoverFilter"
import { SubjectJournalSearchInput } from "./SubjectJournalSearchInput/SubjectJournalSearchInput"
import { SubjectJournalSideNavItems } from "./SubjectJournalSideNavItems/SubjectJournalSideNavItems"
import styles from "./SubjectJournalSideNavigation.module.css"

type SubjectJournalSideNavigationProps = {
  unlinkedEncounters: GetSubjectJournalQuery["subjectJournal"]["unlinkedJournalData"]
  interventionPeriods: GetSubjectJournalQuery["subjectJournal"]["interventionPeriods"]
  onSearchChange: (value: string) => void
  onCreateInteractionPeriod: () => void
  onCreateNewEncounter: (interventionPeriodId: string) => void
}

export const SubjectJournalSideNavigation = ({
  unlinkedEncounters,
  interventionPeriods,
  onSearchChange,
  onCreateInteractionPeriod,
  onCreateNewEncounter,
}: SubjectJournalSideNavigationProps) => {
  const { t } = useTranslation()
  const { hasPermission } = usePermissions()

  const [interventionPeriodInfo, setInterventionPeriodInfo] = useState<{
    id: string | null
    currentTitle: string | undefined
  }>({
    id: null,
    currentTitle: undefined,
  })

  const [updateInterventionPeriod] = useUpdateInterventionPeriodMutation()

  useEffect(() => {
    if (
      interventionPeriodInfo.id &&
      interventionPeriodInfo.currentTitle !== undefined
    ) {
      updateInterventionPeriod({
        variables: {
          updateInterventionPeriodId: interventionPeriodInfo.id,
          input: {
            title: interventionPeriodInfo.currentTitle,
          },
        },
        onCompleted: () => {
          setInterventionPeriodInfo({
            id: null,
            currentTitle: undefined,
          })
        },
        onError: () => {
          setInterventionPeriodInfo({
            id: null,
            currentTitle: undefined,
          })
        },
      })
    }
  }, [interventionPeriodInfo.currentTitle])

  usePowerMenuGroups([
    {
      id: PowerMenuGroupKey.subjectJournal,
      items: [
        {
          id: "new-intervention-period",
          title: "New Intervention Period",
          execute: onCreateInteractionPeriod,
        },
      ],
    },
  ])

  const hideNavigationList =
    unlinkedEncounters.length === 0 && interventionPeriods.length === 0

  return (
    <SideNavWrap aria-label={t("Page")}>
      {/* Enable filter when api is ready */}
      <div className={styles.filters}>
        <SubjectJournalSearchInput onSearchChange={onSearchChange} />
      </div>

      <Button
        variant="clear"
        icon={<Icon name="add-line" />}
        onClick={onCreateInteractionPeriod}
        className={styles.createNewInterventionPeriod}
      >
        {t("Intervention period")}
      </Button>

      {!hideNavigationList && (
        <SideNavList>
          {unlinkedEncounters.length > 0 && (
            <SideNavGroup>
              <SubjectJournalSideNavItems
                encounters={unlinkedEncounters.filter(isTypename("Encounter"))}
              />
            </SideNavGroup>
          )}

          {interventionPeriods.map((interventionPeriod) => {
            const title =
              interventionPeriodInfo.id === interventionPeriod.id &&
              interventionPeriodInfo.currentTitle !== undefined
                ? interventionPeriodInfo.currentTitle
                : interventionPeriod.title
            return (
              <SideNavGroup
                key={interventionPeriod.id}
                title={
                  <EditableText
                    title={title}
                    textSize="default"
                    inputSize="small"
                    onSave={(value) => {
                      setInterventionPeriodInfo({
                        id: interventionPeriod.id,
                        currentTitle: value,
                      })
                    }}
                    isEditable={hasPermission(
                      PermissionKey.SubjectJournalInterventionPeriodEdit
                    )}
                    wrapperElement={Heading}
                    editModeClassName={styles.interventionPeriodTitleForm}
                  />
                }
                actions={
                  <InterventionPeriodMenu
                    onCreateNewEncounter={() =>
                      onCreateNewEncounter(interventionPeriod.id)
                    }
                    className={styles.menuDots}
                  />
                }
              >
                <SubjectJournalSideNavItems
                  encounters={interventionPeriod.journalData.filter(
                    isTypename("Encounter")
                  )}
                />
              </SideNavGroup>
            )
          })}
        </SideNavList>
      )}
    </SideNavWrap>
  )
}

export default SubjectJournalSideNavigation
