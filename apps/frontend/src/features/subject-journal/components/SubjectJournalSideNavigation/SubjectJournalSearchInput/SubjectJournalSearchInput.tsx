import { ChangeEvent, useCallback, useMemo, useTransition } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Input } from "ui"

import styles from "./SubjectJournalSearchInput.module.css"

type SubjectJournalSearchInputProps = {
  className?: string
  onSearchChange: (value: string) => void
}

export const SubjectJournalSearchInput = ({
  className = "",
  onSearchChange,
  ...rest
}: SubjectJournalSearchInputProps) => {
  const { t } = useTranslation()
  const [isPending, startTransition] = useTransition()

  const handleInputChange = useCallback(
    (e: ChangeEvent<HTMLInputElement>) => {
      startTransition(() => onSearchChange(e.target.value))
    },
    [onSearchChange, startTransition]
  )

  const inputProps = useMemo(() => {
    return {
      className,
    }
  }, [className])

  return (
    <Input
      label={"Search"}
      clearable
      hideLabel
      iconStart={<Icon name="search-line" className={styles.icon} />}
      icon={isPending && <Icon name="loader-4-line" spin />}
      placeholder={t("Search")}
      hideMessage
      className={styles.inputWrap}
      onChange={handleInputChange}
      inputProps={inputProps}
      {...rest}
    />
  )
}
