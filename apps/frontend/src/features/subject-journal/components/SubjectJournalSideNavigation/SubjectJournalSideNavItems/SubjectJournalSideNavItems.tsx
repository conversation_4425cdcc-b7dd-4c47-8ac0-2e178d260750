import { format, parseISO } from "date-fns"

import { Text, TextWithIcon } from "ui"
import { SideNavItem } from "ui/components/SideNav/SideNav"

import { InterventionPeriodFragmentFragment } from "generated/graphql"

import { useJournalDataInView } from "../../JournalDataInView/JournalDataInView.context"
import styles from "./SubjectJournalSideNavItems.module.css"

type Encounter = Extract<
  InterventionPeriodFragmentFragment["journalData"][number],
  { __typename: "Encounter" }
>

type SubjectJournalSideNavItemsProps = {
  encounters: Encounter[]
}

export const SubjectJournalSideNavItems = ({
  encounters,
}: SubjectJournalSideNavItemsProps) => {
  const { firstVisibleJournalData } = useJournalDataInView()

  return (
    <>
      {encounters.map((encounter) => {
        return (
          <SideNavItem
            key={encounter.id}
            isActive={firstVisibleJournalData === encounter.id}
            encounterId={encounter.id}
          >
            <Text>{encounter.reason}</Text>
            {"fromDate" in encounter && (
              <TextWithIcon
                size="small"
                className={styles.date}
                iconName="calendar-2-line"
                secondary
              >
                {format(parseISO(encounter?.fromDate), "MMMM d yyyy")}
              </TextWithIcon>
            )}
          </SideNavItem>
        )
      })}
    </>
  )
}
