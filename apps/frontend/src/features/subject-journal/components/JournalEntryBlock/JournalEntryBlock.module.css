.itemCenter.itemCenter {
  min-height: 24px;
  position: relative;
  scroll-margin-top: 260px;
  margin: 0 -12px;
  padding: 12px 12px 0;
}

.titleRow {
  display: flex;
}

.emptyRow {
  height: 0;
}

.resNote {
  margin-left: auto;
  align-self: flex-end;
}

.itemTitle {
  display: flex;
  align-items: center;
}
.hideButton {
  margin-left: 16px;
}

.chapterWrapper {
  position: absolute;
  left: 36px;
  top: 2px;
  display: flex;
  flex-direction: column;
}

.chevronWrapper {
  background: none;
  border: none;
  display: flex;
  justify-content: center;
  color: var(--color-text);
  cursor: pointer;
  height: 22px;
  padding: 0;
  align-items: center;
  transition: transform 150ms;
  visibility: hidden;
}

.chevronWrapper:hover {
  visibility: visible;
}

.chevronWrapper[data-disabled="true"] {
  pointer-events: none;
  color: var(--color-gray-50);
}

.chevronWrapper[data-direction="up"] {
  transform: rotate(180deg);
}

.chapterNumber {
  color: var(--color-white);
  background-color: var(--color-lev-blue);
  border-radius: 3px;
  width: 24px;
  height: 24px;
  text-align: center;
}

.actionButton {
  position: absolute;
  bottom: 0;
  left: -10px;
  opacity: 0;
  transition:
    opacity 200ms,
    left 200ms cubic-bezier(0.17, 0.84, 0.44, 1),
    visibility 200ms;
  transition-delay: 200ms;
  visibility: hidden;
}
.actionButton.show {
  opacity: 1;
  left: 0;
  transition:
    opacity 200ms,
    left 200ms cubic-bezier(0.17, 0.84, 0.44, 1),
    visibility 200ms;
  transition-delay: 200ms;
  visibility: visible;
}

/* tagged ClinicalCodings */
.tags {
  margin-top: 18px;
  display: flex;
  align-items: flex-start;
  flex-direction: column;
  flex-wrap: nowrap;
  /* to keep icon in line with Tag */
}

.tags div {
  margin: 1px 0;
}

.tags svg {
  margin-left: 4px;
  display: inline;
}

.tags svg {
  fill: var(--color-warning);
}
