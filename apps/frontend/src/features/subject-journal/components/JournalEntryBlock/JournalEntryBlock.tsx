import c from "classnames"
import { ReactNode } from "react"

import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import { PanelWithIcon } from "components/Panel/Panel"
import { useJournalBlockFocus } from "features/subject-journal/components/JournalFocus/JournalFocus"
import { Tag } from "ui"
import { Center } from "ui/components/Layout/Center"

import { JournalEntryBlockFragmentFragment } from "generated/graphql"

import styles from "./JournalEntryBlock.module.css"

type JournalEntryBlockProps = Partial<JournalEntryBlockFragmentFragment> & {
  id: string
  encounterId: string
  canEdit: boolean
  children: ReactNode
  updatedAt: string | null
}

const JournalEntryBlock = ({
  id,
  canEdit,
  children,
  updatedAt,
  title,
  ...rest
}: JournalEntryBlockProps) => {
  const { setFocused, isFocused, ref } = useJournalBlockFocus<HTMLDivElement>({
    id,
  })

  const isResolutionNote = rest.__typename === "Note" && rest.isResolutionNote

  return (
    <Center
      className={styles.itemCenter}
      ref={ref}
      id={id}
      onFocus={setFocused}
      data-is-focused={isFocused}
    >
      {updatedAt && isResolutionNote && (
        <div
          className={c(styles.titleRow, {
            [styles.emptyRow]:
              (!canEdit && !title) || (canEdit && !title && !isFocused),
          })}
        >
          <Tag color="pink" className={styles.resNote}>
            Resolution note
          </Tag>
        </div>
      )}
      <ErrorBoundary
        fallback={
          <PanelWithIcon status="warning">
            We've encountered and error while displaying this data. Please
            refresh the page.
          </PanelWithIcon>
        }
      >
        {children}
      </ErrorBoundary>
    </Center>
  )
}

export default JournalEntryBlock
