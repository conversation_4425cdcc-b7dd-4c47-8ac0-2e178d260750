import { useApolloClient } from "@apollo/client"
import { useTranslation } from "react-i18next"

import { JournalAttachmentButton } from "components/AttachmentButton/AttachmentButton"
import { AttachmentsPreview } from "components/AttachmentsPreview/AttachmentsPreview"
import { DropTarget } from "components/DropTarget/DropTarget"
import { useAttachmentRest } from "hooks/useAttachmentRest"
import { Heading, notification } from "ui"
import { formatFileName } from "utils/formatFileName"
import { splitFileNameAndExtension } from "utils/splitFileNameAndExtension"

import {
  JournalEntryAttachmentFragmentFragment,
  namedOperations,
} from "generated/graphql"

import styles from "./JournalAttachments.module.css"

type AttachmentsProps = {
  attachments: JournalEntryAttachmentFragmentFragment[]
  journalEntryId: string
  canUpload: boolean
}

export default function JournalAttachments({
  attachments,
  journalEntryId,
  canUpload,
}: AttachmentsProps) {
  const { t } = useTranslation()

  const { isAttachmentsLoading, attachFileToJournalEntry } = useAttachmentRest()

  const { getUrlForFile } = useAttachmentRest()
  const client = useApolloClient()

  return (
    <DropTarget
      canUpload={canUpload}
      className={styles.wrap}
      isLoading={isAttachmentsLoading}
      onDropFile={async (files) => {
        try {
          const response = await attachFileToJournalEntry(journalEntryId, files)

          if (response) {
            await client.refetchQueries({
              include: [namedOperations.Query.GetSubjectJournal],
            })

            notification.create({
              message: "File uploaded successfully",
              status: "success",
            })
          }
        } catch (e) {
          notification.create({
            message: e.message || t("Failed to attach file to journal entry"),
            status: "error",
          })
        }
      }}
    >
      <Heading size="xsmall">{t("Attachments")}</Heading>
      <div className={styles.attachments}>
        <AttachmentsPreview
          attachments={attachments.map(({ fileMetadata }) => ({
            id: fileMetadata.id,
            name: fileMetadata.fileName,
            url: getUrlForFile(fileMetadata.id),
          }))}
        >
          {attachments.map(({ id, fileMetadata }) => {
            const name = formatFileName(fileMetadata.fileName, 21)

            const { extension } = splitFileNameAndExtension(
              fileMetadata.fileName
            )

            return (
              <JournalAttachmentButton
                blockId={id}
                fileId={fileMetadata.id}
                key={id}
                color="white"
                extension={extension}
              >
                {name}
              </JournalAttachmentButton>
            )
          })}
        </AttachmentsPreview>
      </div>
    </DropTarget>
  )
}
