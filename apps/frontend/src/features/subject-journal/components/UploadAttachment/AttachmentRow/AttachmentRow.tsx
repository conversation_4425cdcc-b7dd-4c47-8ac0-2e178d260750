import { t } from "i18next"
import { useState } from "react"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { Button, Input, Loading, Text } from "ui"
import { getFileTypeFromName, getIcon } from "utils/attachmentIconMapper"
import { formatFileName } from "utils/formatFileName"
import { formatFileSize } from "utils/formatFileSize/formatFileSize"

import { Attachment } from "../UploadAttachment"
import styles from "./AttachmentRow.module.css"

type AttachmentRowProps = {
  attachment: Attachment
}

export const AttachmentRow = ({ attachment }: AttachmentRowProps) => {
  const [mode, setMode] = useState<"VIEW" | "EDIT">("VIEW")

  const {
    name,
    status,
    statusMessage: message,
    fileSizeInBytes,
    id,
  } = attachment

  const statusMessage =
    status === "error"
      ? message || t("Something went wrong while uploading")
      : t("Upload complete")

  const fileName = formatFileName(name, 35)

  const fileType = getFileTypeFromName(name)

  return (
    <div key={id} className={styles.file}>
      <div className={styles.fileText}>
        <Icon className={styles.fileIcon} name={getIcon(fileType)} />
        {mode === "VIEW" && (
          <>
            <Text>{fileName}</Text>
            {/* Hidden untill backend is ready */}
            {/* <Tooltip
              placement={"top"}
              portal={false}
              tooltipContent={t("Change filename")}
            >
              <Button
                variant="clear"
                icon={<Icon name="pencil-line" />}
                onClick={() => setMode("EDIT")}
              />
            </Tooltip> */}
          </>
        )}
        {mode === "EDIT" && (
          <form className={styles.fileText}>
            <Input
              label="File Edit"
              name="title"
              type="text"
              inputProps={{ className: styles.input }}
              defaultValue={name}
              hideLabel
              hideMessage
              autoFocus
            />
            <Button
              variant="filled"
              className={styles.checkMark}
              icon={<Icon name="check-line" />}
              aria-label="Save"
              onClick={() => setMode("VIEW")}
            />
            <Button
              variant="clear"
              className={styles.closeMark}
              aria-label="Close Edit Mode"
              icon={<Icon name="close-line" />}
              onClick={() => setMode("VIEW")}
            />
          </form>
        )}
      </div>

      <div className={styles.attachmentActions} data-status={status}>
        <Text className={styles.attachmentSizeText}>
          {formatFileSize(fileSizeInBytes)}
        </Text>
        {status === "loading" ? (
          <Loading showText={false} />
        ) : (
          <Tooltip
            tooltipContent={statusMessage}
            portal={false}
            placement="top"
            status={status}
          >
            <div className={styles.statusIconWrap} data-status={status}>
              <Icon
                className={styles.statusIcon}
                data-status={status}
                name={
                  status === "error"
                    ? "information-line"
                    : "checkbox-circle-line"
                }
              />
            </div>
          </Tooltip>
        )}
        {/* Enable when api for deleting attachment is ready */}
        {/* <Button
          variant="clear"
          className={styles.deleteButton}
          icon={<Icon name="delete-bin-line" />}
          onClick={() => onDeleteAttachment(id)}
        /> */}
      </div>
    </div>
  )
}
