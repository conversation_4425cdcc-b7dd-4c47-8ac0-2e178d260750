.file {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-radius: 8px;
  background: var(--color-white);
  box-shadow: 0px 0px 10px 0px rgba(198, 201, 219, 0.3);
  height: 20px;
}

.fileText {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fileIcon {
  width: 20px;
  height: 20px;
}

.statusIconWrap {
  border-radius: 8px;
  padding: 2px;
}

.statusIcon[data-status="error"] {
  width: 24px;
  height: 24px;
  color: var(--color-critical);
}

.statusIconWrap[data-status="error"]:hover {
  background-color: var(--Red-background, #ffecec);
  transition: background-color 0.3s;
}

.statusIcon[data-status="success"] {
  width: 24px;
  height: 24px;
  color: var(--color-green);
}

.statusIconWrap[data-status="success"]:hover {
  background-color: var(--Green-background, #cdede5);
  transition: background-color 0.3s;
}

.attachmentSizeText {
  color: var(--LEV-Blue-Gray-violet-dark, #8386b8);
}

.input.input {
  border-radius: 8px 0px 0px 8px;
  height: 40px;
}

.checkMark {
  border-radius: 0px 7px 7px 0px;
  margin-left: -8px;
  padding: 11px;
}

.closeMark {
  padding: 11px;
}

.deleteButton {
  padding: 4px;
  font-size: 24px;
  margin: -4px;
}

.attachmentActions {
  display: flex;
  align-items: center;
  gap: 8px;
}
