import { useApolloClient } from "@apollo/client"
import { useRef } from "react"
import { useTranslation } from "react-i18next"
import { useImmer } from "use-immer"
import { v4 as uuid } from "uuid"

import Icon from "components/Icon/Icon"
import { useAttachmentRest } from "hooks/useAttachmentRest"
import { But<PERSON>, Modal, Text } from "ui"

import { namedOperations } from "generated/graphql"

import { AttachmentRow } from "./AttachmentRow/AttachmentRow"
import styles from "./UploadAttachment.module.css"

type UploadAttachmentProps = {
  onClose: () => void
  journalEntryId: string | null
}

export type Attachment = {
  id: string
  name: string
  status: "success" | "error" | "loading"
  statusMessage?: string
  fileSizeInBytes: number
}

export const UploadAttachment = ({
  onClose,
  journalEntryId,
}: UploadAttachmentProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [attachments, setAttachments] = useImmer<Attachment[]>([])
  const { t } = useTranslation()
  const { attachFileToJournalEntry } = useAttachmentRest()

  const client = useApolloClient()

  const handleClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleClose = async () => {
    onClose()
    setAttachments((draft) => {
      draft.splice(0, draft.length)
    })
    await client.refetchQueries({
      include: [namedOperations.Query.GetSubjectJournal],
    })
  }

  return (
    <Modal
      contentClassName={styles.modalContent}
      className={styles.modal}
      title={t("Upload Attachments")}
      onClose={handleClose}
      isOpen={!!journalEntryId}
    >
      <div className={styles.wrap}>
        <div className={styles.uploadFileWrap}>
          <div className={styles.uploadText}>
            <Icon className={styles.documentIcon} name="file-list-line" />
            <Text>{t("Select attachment")}</Text>
          </div>

          <input
            type="file"
            multiple
            ref={fileInputRef}
            className={styles.inputFile}
            onChange={async (e) => {
              const files = e.target.files
              if (files && journalEntryId) {
                for (let i = 0; i < files.length; i++) {
                  const file = files[i]
                  const fileSizeInBytes = file.size ? Math.floor(file.size) : 0
                  const id = uuid()

                  const fileInMb = fileSizeInBytes / (1024 * 1024)

                  if (fileInMb >= 100) {
                    setAttachments((draft) => {
                      draft.push({
                        id,
                        name: file.name,
                        status: "error",
                        statusMessage: t("File size should be less than 100MB"),
                        fileSizeInBytes,
                      })
                    })

                    return
                  }

                  setAttachments((draft) => {
                    draft.push({
                      id,
                      name: file.name,
                      status: "loading",
                      fileSizeInBytes,
                    })
                  })

                  try {
                    await attachFileToJournalEntry(journalEntryId, files[i])
                    setAttachments((draft) => {
                      const attachment = draft.find((a) => a.id === id)
                      if (attachment) {
                        attachment.status = "success"
                      }
                    })
                  } catch (e) {
                    setAttachments((draft) => {
                      const attachment = draft.find((a) => a.id === id)
                      if (attachment) {
                        attachment.status = "error"
                        attachment.statusMessage = e.message
                      }
                    })
                  }
                }
              }
            }}
          />
          <Button variant="filled" onClick={handleClick}>
            {t("Browse File")}
          </Button>
        </div>
        {attachments.map((attachment) => (
          <AttachmentRow key={attachment.id} attachment={attachment} />
        ))}
        <div className={styles.action}>
          <Button onClick={handleClose}>{t("Done")}</Button>
        </div>
      </div>
    </Modal>
  )
}
