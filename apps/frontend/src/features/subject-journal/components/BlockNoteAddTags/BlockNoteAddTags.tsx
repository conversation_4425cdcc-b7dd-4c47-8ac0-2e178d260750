import { useSelectStore } from "@ariakit/react"
import { useTranslation } from "react-i18next"
import { v4 as uuid } from "uuid"

import Select from "components/Select/Select"

import {
  ClinicalCodingCriticalType,
  ClinicalCodingType,
  JournalEntryBlockTaggingQuery,
  LanguageId,
  useCreateJournalClinicalCodingMutation,
  useJournalEntryBlockTaggingQuery,
  useLinkClinicalCodingMutation,
} from "generated/graphql"

import styles from "./BlockNoteAddTags.module.css"

type BlockNoteAddTagsProps = {
  subjectId: string
  journalEntryId: string
  journalEntryBlockId: string

  data: JournalEntryBlockTaggingQuery
}

// Explicitly decide order of items in Select
const orderedCodingTypes = [
  ClinicalCodingType.Diagnosis,
  ClinicalCodingType.Operation,
  ClinicalCodingType.Allergy,
  ClinicalCodingType.Behavior,
  ClinicalCodingType.TreatmentRestriction,
]

export const BT = ({
  journalEntryBlockId,
  data: {
    subject: { clinicalCodings },
  },
}: BlockNoteAddTagsProps) => {
  const { t } = useTranslation()
  const selectClinicalCodingStore = useSelectStore({})

  const [linkClinicalCoding] = useLinkClinicalCodingMutation()

  const [createJournalClinicalCoding] = useCreateJournalClinicalCodingMutation()

  const parseSelectValue = (value: string) => {
    if (value.startsWith("_")) {
      createJournalClinicalCoding({
        variables: {
          id: uuid(),
          input: {
            journalEntryId: journalEntryBlockId,
            criticalType: ClinicalCodingCriticalType.NotCritical,
            code: { codingType: value.slice(1) as ClinicalCodingType },
          },
        },
      })
    } else {
      linkClinicalCoding({
        variables: {
          clinicalCodingId: value,
          noteId: journalEntryBlockId,
          languageId: LanguageId.Is,
        },
      })
    }
  }

  return (
    <div className={styles.wrapper}>
      <Select
        placeholder="#"
        label={t("routes:clinicalCoding.tagWithCcLabel")}
        selectStore={selectClinicalCodingStore}
        onSelectChange={(value) => parseSelectValue(value as string)}
        options={clinicalCodings
          .filter(({ code, signedAt }) => code !== null && signedAt !== null)
          .map(({ id, code }) => ({
            value: id,
            label: code?.displayLabel || "...",
          }))
          // header option
          .concat(
            { value: ".", label: t("routes:clinicalCoding.createNewCc") },
            orderedCodingTypes.map((type) => ({
              value: `_${type}`,
              label: t(`enums:ClinicalCoding_CodingType.${type}`),
            }))
          )}
      />
    </div>
  )
}

export const BlockNoteAddTags = ({
  subjectId,
  ...rest
}: Omit<BlockNoteAddTagsProps, "data">) => {
  const { data } = useJournalEntryBlockTaggingQuery({
    variables: {
      subjectId,
    },
  })

  if (!data) return null

  return <BT {...rest} subjectId={subjectId} data={data} />
}
