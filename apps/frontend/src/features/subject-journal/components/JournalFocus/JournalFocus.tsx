import {
  createContext,
  ReactNode,
  useContext,
  useEffect,
  useRef,
  useState,
  useLayoutEffect,
  useCallback,
} from "react"

import { usePowerMenu } from "features/power-menu/PowerMenu.context"
import { PowerMenuGroupKey } from "features/power-menu/lib/enums/PowerMenuGroupKeys.enum"
import isElementInViewport from "utils/isElementInViewport"

import {
  InterventionPeriodFragmentFragment,
  useSetSubjectJournalFocusedItemMutation,
} from "generated/graphql"

import styles from "./JournalFocus.module.css"

export enum Movement {
  SectionUp, //SJF: to topmost DSW-Item in DS
  SectionDown, //SJF: to bottommost DSW-Item in DS
  DocSessionUp,
  DocSessionDown,
  ItemUp,
  ItemDown,
}

const JournalFocusContext = createContext({
  focusedItemId: null as null | string,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  setFocusedBlockId: (id: string) => {
    return
  },
})

type Encounters = Extract<
  InterventionPeriodFragmentFragment["journalData"][number],
  { __typename: "Encounter" }
>[]

type EncounterIdTree = {
  id: Encounters[number]["id"]
  journalEntries: readonly {
    id: Encounters[number]["journalEntries"][number]["id"]
    sections:
      | readonly {
          id: NonNullable<
            Encounters[number]["journalEntries"][number]["sections"]
          >[number]["id"]
        }[]
      | null
    blocks: readonly {
      id: Encounters[number]["journalEntries"][number]["blocks"][number]["id"]
    }[]
  }[]
}

export type UseJournalFocusParams = {
  id: string
  onFocus?: () => void
}

export const useJournalBlockFocus = <T extends HTMLElement>({
  id,
  onFocus,
}: UseJournalFocusParams) => {
  const context = useContext(JournalFocusContext)
  const { focusedItemId, setFocusedBlockId } = context
  const ref = useRef<T>(null)

  useEffect(() => {
    if (focusedItemId === id) {
      if (ref.current && !isElementInViewport(ref.current, { top: 265 })) {
        ref.current.scrollIntoView({
          behavior: "smooth",
          block: "start",
        })
      }
      onFocus?.()
    }
  }, [focusedItemId, id, onFocus])

  const isInputFocused = document.activeElement?.tagName === "INPUT"

  return {
    ref,
    isFocused: isInputFocused ? false : id === focusedItemId,
    setFocused: () => setFocusedBlockId(id),
  }
}

export const useJournalFocus = () => {
  const { focusedItemId, setFocusedBlockId } = useContext(JournalFocusContext)

  return {
    focusedItemId,
    setFocusedBlockId,
  }
}

const getOptimisticMutationResponse = (id: string, focusedItemId: string) => ({
  __typename: "Mutation" as const,
  setSubjectJournalFocusedItem: {
    __typename: "SetSubjectJournalFocusedItemOutput" as const,
    subjectJournal: {
      __typename: "SubjectJournal" as const,
      id,
      focusedItemId,
    },
  },
})

type FocusContextProps = {
  children: ReactNode
  encounters: readonly EncounterIdTree[]
  focusedItemId?: string | null
  subjectId: string
}

export default function JournalFocus({
  children,
  encounters,
  focusedItemId = null,
  subjectId,
}: FocusContextProps) {
  const [setSubjectJournalFocusedItem] =
    useSetSubjectJournalFocusedItemMutation()

  const setFocusedBlockId = useCallback(
    (id: string) => {
      if (id !== focusedItemId) {
        setSubjectJournalFocusedItem({
          variables: { journalEntryBlockId: id, encounterId: null },
          optimisticResponse: getOptimisticMutationResponse(subjectId, id),
        })
      }
    },
    [setSubjectJournalFocusedItem, subjectId, focusedItemId]
  )

  const { addPowerMenuGroups, removePowerMenuGroups } = usePowerMenu()

  useEffect(() => {
    const flatBlockIds = encounters.flatMap(({ journalEntries }) =>
      journalEntries.flatMap(({ sections, blocks }) => [
        ...(sections || []).flatMap(({ id }) => id),
        ...blocks.flatMap(({ id }) => id),
      ])
    )
    const currentIndex = flatBlockIds.findIndex(
      (blockId) => blockId === focusedItemId
    )

    const move = {
      first() {
        setFocusedBlockId(flatBlockIds[0])
      },
      last() {
        setFocusedBlockId(flatBlockIds[flatBlockIds.length - 1])
      },
      up() {
        const previousIndex = Math.max(currentIndex - 1, 0)
        setFocusedBlockId(flatBlockIds[previousIndex])
      },
      down() {
        const nextIndex = Math.min(currentIndex + 1, flatBlockIds.length - 1)
        setFocusedBlockId(flatBlockIds[nextIndex])
      },
    }
    const powerMenuActions = [
      {
        title: "Subject Journal",
        id: PowerMenuGroupKey.subjectJournal,
        items: [
          {
            title: "Move up",
            id: "subject-journal.move-up",
            shortcutCombination: `pageup`,
            execute: move.up,
          },
          {
            title: "Move Down",
            id: "subject-journal.move-down",
            shortcutCombination: `pagedown`,
            execute: move.down,
          },
          // {
          //   title: "Move to first",
          //   id: "subject-journal.move-to-first",
          //   shortcutCombination: `${modifier}+ArrowUp`,
          //   execute: move.first,
          // },
          // {
          //   title: "Move to last",
          //   id: "subject-journal.move-to-last",
          //   shortcutCombination: `${modifier}+ArrowDown`,
          //   execute: move.last,
          // },
        ],
      },
    ]
    addPowerMenuGroups(powerMenuActions)
    return () => removePowerMenuGroups(powerMenuActions)
  }, [setSubjectJournalFocusedItem, focusedItemId, setFocusedBlockId])

  return (
    <JournalFocusContext.Provider value={{ focusedItemId, setFocusedBlockId }}>
      {children}
    </JournalFocusContext.Provider>
  )
}
const getFocusedElement = (focusedItemId?: string | null) => {
  if (!focusedItemId) return null

  const element = document.getElementById(focusedItemId)
  return element
}

const getElementPosition = (element?: HTMLElement) => {
  if (!element) return { top: 0, height: 0, left: 0 }

  const { top, height, left } = element.getBoundingClientRect()
  const scrollMarginTop = document.documentElement.scrollTop

  return { top: top + scrollMarginTop - 25, height, left }
}

export const JournalFocusIndicator = () => {
  const { focusedItemId } = useJournalFocus()
  const [position, setPosition] = useState({ top: 0, height: 0, left: 0 })

  useLayoutEffect(() => {
    const element = getFocusedElement(focusedItemId)
    const bodyElement = document.getElementsByTagName("body")
    // Since supplements are rendered in a different context, we need to find the
    // closest element that has the same parent as the focused
    // element ensure the left position is correct
    const leftElement =
      element?.closest<HTMLElement>(".inboundData > div") || null

    if (!element || !leftElement) {
      setPosition((p) => ({ ...p, height: 0 }))
      return
    }

    const observer = new ResizeObserver(() => {
      const left = getElementPosition(leftElement).left
      setPosition({ ...getElementPosition(element), left })
      // callback(element, entries[0]);
    })

    observer.observe(element)
    observer.observe(bodyElement[0])
    return () => {
      observer.disconnect()
    }
  }, [focusedItemId, setPosition])

  const { top, height, left } = position
  return (
    <div
      style={{ top: top + 25, height, left: left - 17 }}
      className={styles.focusIndicator}
    />
  )
}
