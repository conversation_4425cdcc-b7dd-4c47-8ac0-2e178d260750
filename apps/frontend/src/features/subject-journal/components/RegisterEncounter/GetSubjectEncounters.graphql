query GetSubjectEncounters($subjectId: UUID!) {
  subjectJournal(subjectId: $subjectId) {
    subject {
      id
      organisation {
        id
        members {
          ...ProviderInfoFragment
        }
      }
    }

    interventionPeriods {
      id
      title
      journalData {
        ... on Encounter {
          id
          reason
          fromDate
          toDate
          status {
            allowedTransitions
            status
          }
        }
      }
    }
  }
}
