.content {
  user-select: text;
}

.content[data-is-not-completed-entry="true"] {
  cursor: pointer;
  background-color: white;
  min-height: 24px;
  border-radius: var(--radius-button-half);
  padding: 14px 12px;
  margin: 0 -12px;
}

.content[data-is-empty="true"] {
  color: var(--color-blue-gray-violet-dark);
}

.sectionHeading {
  display: grid;
  gap: 8px;
  margin-bottom: 8px;
}

.sectionInlineGuide {
  margin: 0 -16px;
}

.editor {
  margin: 0 -13px;
  --encounter-header-height: 160px;
  --editor-sticky-toolbar-offset: calc(
    var(--header-height) + var(--grid-gap) + var(--encounter-header-height)
  );
}

.tags {
  margin-top: 4px;
}

.tagsCurrent {
  display: inline-flex;
  flex-direction: column;
  font-size: 0.95em;
}

.tagsCurrent div {
  margin: 1px 0;
}

.tags svg {
  fill: var(--color-warning);
  margin-left: 4px;
  display: inline;
}

.completeEntryButton {
  margin-right: -12px;
}

.focusedFooterWrap {
  display: flex;
  justify-content: flex-end;
  margin-top: 8px;
  position: relative;
}
.saveStatus {
  position: absolute;
  left: 0;
}
