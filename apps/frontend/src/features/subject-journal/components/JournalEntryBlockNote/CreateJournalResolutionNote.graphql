mutation CreateJournalResolutionNote(
  $id: UUID!
  $input: NoteCreateInput!
  $resolutionInput: AddResolutionNoteToEncounterInput!
) {
  createNote(id: $id, input: $input) {
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
        sections {
          id
        }
      }
    }
    ...JournalEntryBlockFragment
  }
  addResolutionNoteToEncounter(input: $resolutionInput) {
    id
    canCreateResolutionNote
    journalEntries {
      id
      blocks {
        id
        ... on Note {
          isResolutionNote
        }
      }
      sections {
        id
      }
    }
  }
  setSubjectJournalFocusedItem(
    input: { encounterId: null, journalEntryBlockId: $id }
  ) {
    subjectJournal {
      id
      focusedItemId
    }
    interventionPeriod {
      id
      focusedItemId
    }
  }
}
