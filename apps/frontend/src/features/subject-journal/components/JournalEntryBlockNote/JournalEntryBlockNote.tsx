import { useApolloClient } from "@apollo/client"
import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { useDebouncedCallback } from "use-debounce"

import AnimateMount from "components/AnimateMount/AnimateMount"
import { DropTarget } from "components/DropTarget/DropTarget"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { InlineGuidePanel } from "components/InlineGuide/InlineGuidePanel"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import usePermissions from "features/authentication/hooks/usePermissions"
import LexicalEditor from "features/rich-text-editor/LexicalEditor"
import { useJournalBlockFocus } from "features/subject-journal/components/JournalFocus/JournalFocus"
import { useAttachmentRest } from "hooks/useAttachmentRest"
import { Heading, notification } from "ui"
import { stripHtml } from "utils/stripHtml"

import {
  JournalEntryBlockFragmentFragment,
  JournalTemplateFragmentFragment,
  namedOperations,
  PermissionKey,
  SnippetType,
  useEditJournalEntryBlockNoteMutation,
} from "generated/graphql"

import SaveStatus, { getSaveStatus } from "../SaveStatus/SaveStatus"
import styles from "./JournalEntryBlockNote.module.css"

type JournalEntryBlockNoteProps = {
  isEditable: boolean
  id: string
  content: string
  updatedAt: null | string
  journalEntryId: string
  journalTemplates: JournalTemplateFragmentFragment[]
  isCompleted: boolean
  onAddBillingItem?: () => void
  onAddAttachment?: () => void
  ownedId?: string
} & Extract<JournalEntryBlockFragmentFragment, { __typename: "Note" }>

export default function JournalEntryBlockNote({
  isEditable,
  id,
  content,
  updatedAt,
  journalEntryId,
  signedAt,
  ownedId,
  isCompleted,
  journalTemplates,
  title,
  inlineGuide,
  onAddBillingItem,
  onAddAttachment,
}: JournalEntryBlockNoteProps) {
  const { t } = useTranslation()

  const { globalData } = useGlobalState()
  const { actor } = globalData

  const isOwner = ownedId === actor.id

  const [saveEdit, { called, loading, error }] =
    useEditJournalEntryBlockNoteMutation()

  const [localContent, setLocalContent] = useState(content)
  const { isFocused } = useJournalBlockFocus({ id })
  const { hasPermission } = usePermissions()

  const { attachFileToJournalEntry, isAttachmentsLoading } = useAttachmentRest()
  const client = useApolloClient()

  const saveContentCallback = (content: string) => {
    if (signedAt) return

    saveEdit({
      variables: {
        id,
        input: { content },
      },
    })
  }

  const debouncedSaveContent = useDebouncedCallback(saveContentCallback, 2000, {
    // 10 second max wait time
    maxWait: 10000,
  })

  const handleChange = (updatedContent: string) => {
    if (updatedContent === localContent) return

    setLocalContent(updatedContent)
    debouncedSaveContent(updatedContent)
  }

  const handleBlur = () => {
    if (content === localContent) return

    debouncedSaveContent.flush()
  }

  useEffect(
    () => () => {
      debouncedSaveContent.flush()
    },
    [debouncedSaveContent]
  )

  const saveStatus = getSaveStatus({
    isLoading: loading,
    isError: !!error,
    isCalled: called,
    isContentSync: localContent === content,
  })

  const canEdit =
    isEditable && hasPermission(PermissionKey.SubjectJournalJournalEntryEdit)

  const strippedHtml = stripHtml(content)

  const blockNoteContent =
    !strippedHtml.length && !isOwner
      ? t("This note is empty")
      : strippedHtml.length === 0 && !isCompleted
        ? t("Click to add description")
        : content

  return (
    <>
      {title && (
        <div className={styles.sectionHeading}>
          <Heading size="small">{title}</Heading>
          {inlineGuide && isFocused && (
            <InlineGuidePanel
              content={inlineGuide}
              className={styles.sectionInlineGuide}
            />
          )}
        </div>
      )}
      {canEdit && isEditable && isFocused ? (
        <DropTarget
          isLoading={isAttachmentsLoading}
          onDropFile={async (file) => {
            try {
              const resp = await attachFileToJournalEntry(journalEntryId, file)

              if (resp) {
                await client.refetchQueries({
                  include: [namedOperations.Query.GetSubjectJournal],
                })

                notification.create({
                  message: "File uploaded successfully",
                  status: "success",
                })
              }
            } catch (e) {
              notification.create({
                message:
                  e.message || t("Failed to attach file to journal entry"),
                status: "error",
              })
            }
          }}
        >
          <PiiSensitive>
            <LexicalEditor
              id={id}
              onChange={handleChange}
              onBlur={handleBlur}
              initialContent={content}
              className={styles.editor}
              journalEntryId={journalEntryId}
              sectionId={id}
              journalTemplates={journalTemplates}
              onAddBillingItem={onAddBillingItem}
              onAddAttachment={onAddAttachment}
              // there is a bug from backend which resolves all snippets of type NLP as having same id
              snippets={actor.snippets.map((snippet) => ({
                ...snippet,
                id:
                  snippet.snippetType === SnippetType.Nlp
                    ? snippet.matchText
                    : snippet.id,
              }))}
            />
          </PiiSensitive>
        </DropTarget>
      ) : (
        <PiiSensitive
          as="div"
          className={`${styles.content} editor`}
          data-is-not-completed-entry={!isCompleted}
          data-is-empty={!strippedHtml.length}
          tabIndex={0}
          dangerouslySetInnerHTML={{
            __html: blockNoteContent,
          }}
        />
      )}

      <AnimateMount
        className={styles.focusedFooterWrap}
        show={isFocused && isEditable}
      >
        <SaveStatus
          status={saveStatus}
          updatedAt={updatedAt}
          className={styles.saveStatus}
        />
      </AnimateMount>
    </>
  )
}
