fragment DrugPrescriptionBlockFragment on DrugPrescription {
  id
  shorthand
  dosingInstructions
  # customNotes @client
  reasoning
  maxDailyUnits
  daysBetweenDispension
  packageCount
  validFrom
  validTo
  prepareDosing
  commentToDispenser
  clinicalIndication
  status
  completable
  allowGenericSubstitution
  updatedAt
  sendError
  drugCode(languageId: IS) {
    id
    uid
    label
    details {
      ... on VnrDetails {
        id
        codesetVersionDate
        overTheCounter
        additionalRiskMinimisationMeasures
        requiresSpecialist
        onExemptionList
        addictive
        addictivePrescriptionQuantityLimit
        addictivePrescriptionQuantityUnit
        flags
        atc
        activeIngredient
        form
        packageSize
        packageSizeUnit
        strength
        strengthUnit
        numberOfPackages
        packageType
      }
    }
    codeset
  }
  title
  blockType
  signedBy {
    id
    name
  }
  signedAt
  createdBy {
    id
    name
  }
}
