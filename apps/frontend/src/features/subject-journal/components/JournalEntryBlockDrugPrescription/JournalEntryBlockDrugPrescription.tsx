import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { SupportedLanguages } from "utils/SupportedLanguages"

import { DrugPrescriptionBlockFragmentFragment } from "generated/graphql"

import DrugPrescriptionBlock from "../DrugPrescription/DrugPrescriptionBlock"

type JournalEntryBlockDrugPrescriptionProps =
  DrugPrescriptionBlockFragmentFragment & {
    languageId: SupportedLanguages
    canEdit: boolean
    onDelete?: () => void
  }

export default function JournalEntryBlockDrugPrescription(
  props: JournalEntryBlockDrugPrescriptionProps
) {
  const {
    globalData: { actor },
  } = useGlobalState()

  return (
    <DrugPrescriptionBlock
      drugPrescription={props}
      languageId={props.languageId}
      canEdit={props.canEdit}
      showDoctorNumberWarning={!actor.doctorNumber}
      onDelete={props.onDelete}
    />
  )
}
