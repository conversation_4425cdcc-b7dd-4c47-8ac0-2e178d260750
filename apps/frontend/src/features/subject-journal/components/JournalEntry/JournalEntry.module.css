.aside {
  position: relative;
  grid-column: 1 / 2;
  margin-right: calc(-1 * var(--grid-gap));
}

.aside::after {
  content: "";
  display: block;
  width: 6px;
  height: var(--focused-block-height);
  position: absolute;
  top: var(--focused-block-margin-top);
  right: -24px;
  background: var(--color-lev-blue);
  transition:
    top 200ms ease-in-out,
    height 200ms ease-in-out;
}

.aside[data-is-signed="true"]::after {
  background: var(--color-gray-30);
}

.header {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;
}

.heading.heading {
  /* clear the margin-bottom defined in the EncounterHeader .wrapper */
  margin-top: calc(-1 * var(--encounter-header-wrapper-mb));
  margin-bottom: -8px;
  line-height: unset;
}

.subHeading {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.tags {
  display: flex;
  gap: 4px;
  background-color: var(--encounter-background);
}

.headerMenu {
  position: absolute;
  right: 0;
  top: 4px;
}

.headerMenuButton {
  font-size: 20px;
}

.menuItem {
  display: flex;
  gap: 8px;
  align-items: center;
}

.menuItem svg {
  font-size: 20px;
}

.inlineGuide {
  margin: 8px -16px 0;
}

.completeEntryWrap.completeEntryWrap {
  position: relative;
  display: grid;
  gap: 36px;
  margin: 0 -12px 24px;
  align-items: center;
}

.infoPanel {
  display: flex;
  gap: 4px;
  justify-content: space-between;
  align-items: center;
}

.infoText {
  white-space: nowrap;
}

.closeButton.closeButton {
  font-size: 18px;
  border: none;
  background: transparent;
  padding: 0;
}

.journalEntryTitle {
  display: grid;
  gap: 8px;
  grid-template-columns: auto 1fr auto;
  margin-right: 40px;
  align-items: center;
}

.journalEntryTitleIcon {
  width: 24px;
  height: 24px;
  color: var(--color-text);
  margin-top: 3px;
  flex-shrink: 0;
}

.completed {
  color: var(--color-lev-green-5);
}

.completeEntryButtonBlock {
  display: flex;
  justify-content: end;
}

.authors {
  margin-block-start: 6px;
  opacity: 0.7;
}

.informationItem {
  display: flex;
  gap: 8px;
  align-items: center;
  margin-left: 8px;
}

.undoCompleteEntry {
  display: flex;
  align-items: center;
  margin-top: 8px;
  justify-self: flex-end;
}
