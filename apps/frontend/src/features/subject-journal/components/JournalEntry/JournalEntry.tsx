import c from "classnames"
import { ReactNode, createContext, useContext, useState } from "react"
import { useTranslation } from "react-i18next"

import { Menu, MenuButton, MenuProvider } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import Icon from "components/Icon/Icon"
import { InlineGuidePanel } from "components/InlineGuide/InlineGuidePanel"
import Panel, { PanelWithIcon } from "components/Panel/Panel"
import { useHtmlToMarkdown } from "hooks/useHtmlToMarkdown"
import { color } from "styles/colors"
import { Button, Heading, Tag, Text, TextWithIcon, notification } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import { Center } from "ui/components/Layout/Center"
import { ProviderHoverInfoLink } from "ui/components/ProviderBadgeMenu/ProviderHoverInfoLink/ProviderHoverInfoLink"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  JournalEntryStatus,
  ProviderInfoFragmentFragment,
  useCompleteJournalEntryMutation,
  useDeleteJournalEntryMutation,
  useUpdateJournalEntryMutation,
  DocumentType,
  useUndoConfirmJournalEntryMutation,
  JournalEntryBlockFragmentFragment,
  namedOperations,
} from "generated/graphql"

import EditableText from "../EditableText/EditableText"
import { EntryDate } from "../EntryDate/EntryDate"
import { useJournalFocus } from "../JournalFocus/JournalFocus"
import styles from "./JournalEntry.module.css"

type JournalEntryProps = {
  id: string
  title?: string
  createdBy: ProviderInfoFragmentFragment
  children: ReactNode
  journalEntryId: string
  createdAt: string
  status: JournalEntryStatus
  entryRulerSpan: number
  canActorEdit: boolean
  canActorSign: boolean
  isCompletable: boolean
  firstItemPreventingComplete: string | null
  documentType?: DocumentType | null
  inlineGuide?: string | null
  undoUntil?: string | null
  sections: JournalEntryBlockFragmentFragment[] | null
}

type JournalEntryContextType = JournalEntryProps & {
  getJournalEntryAsMarkdown: () => string
}

const JournalEntryContext = createContext<JournalEntryContextType | null>(null)

export const useJournalEntryContext = () => {
  const context = useContext(JournalEntryContext)

  if (!context) {
    throw new Error(
      "useJournalEntryContext must be used within a JournalEntryContextProvider"
    )
  }

  return context
}

const JournalEntryMenu = ({
  id,
  status,
  onCopyEntry,
}: {
  id: string
  status: JournalEntryStatus
  onCopyEntry: () => void
}) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.journalEntries",
  })
  const [isDialogForDeleteOpen, setIsDialogForDeleteOpen] = useState(false)

  const [deleteJournalEntry] = useDeleteJournalEntryMutation({
    variables: { id: id },
    onCompleted: () => {
      notification.create({
        status: "success",
        message: tRoutes("entryDeleted"),
      })
    },
    onError: (error) => {
      setIsDialogForDeleteOpen(false)
      notification.create({
        status: "error",
        message: tRoutes("deleteFailed") + ": " + error.message,
      })
    },
  })

  return (
    <div className={styles.headerMenu}>
      <MenuProvider placement="bottom-start">
        <MenuButton
          className={styles.headerMenuButton}
          size="large"
          aria-label={tRoutes("entryOptions")}
          icon={<Icon name="more-line" />}
          variant="clear"
        />
        <Menu gutter={8} portal>
          <MenuItem className={styles.menuItem} onClick={onCopyEntry}>
            <Icon name={"file-copy-line"} /> {tRoutes("copyEntry")}
          </MenuItem>
          {status !== JournalEntryStatus.Completed && (
            <MenuItem
              className={styles.menuItem}
              onClick={() => setIsDialogForDeleteOpen(true)}
            >
              <Icon name={"delete-bin-line"} /> {tRoutes("deleteEntry")}
            </MenuItem>
          )}
        </Menu>
      </MenuProvider>

      <Dialog
        title={tRoutes("deleteConfirmation")}
        isOpen={isDialogForDeleteOpen}
        onClose={() => {
          setIsDialogForDeleteOpen(false)
        }}
        actions={
          <>
            <Button onClick={() => setIsDialogForDeleteOpen(false)}>
              {t("cancel")}
            </Button>
            <Button onClick={() => deleteJournalEntry()} variant="filled">
              {t("doDelete")}
            </Button>
          </>
        }
      >
        <Text>{tRoutes("deletePermanentWarning")}</Text>
      </Dialog>
    </div>
  )
}

export default function JournalEntry(props: JournalEntryProps) {
  const {
    id,
    title,
    createdBy,
    children,
    createdAt,
    isCompletable,
    firstItemPreventingComplete,
    entryRulerSpan,
    status,
    canActorEdit,
    canActorSign,
    documentType,
    inlineGuide,
    undoUntil,
    sections,
  } = props

  const [isPanelOpen, setIsPanelOpen] = useState(false)
  const [jeTitle, setJeTitle] = useState(title)

  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.journalEntries",
  })
  const { t: tEnum } = useTranslation("enums")
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const { convertToMarkdown } = useHtmlToMarkdown()

  const [updateJournalEntry] = useUpdateJournalEntryMutation()
  const [undoCompleteJournalEntry] = useUndoConfirmJournalEntryMutation()
  const [completeJournalEntry] = useCompleteJournalEntryMutation({
    onCompleted: () => {
      notification.create({
        status: "success",
        message: tRoutes("entryCompleted"),
      })
    },
    refetchQueries: [namedOperations.Query.ProviderBox],
  })

  const { setFocusedBlockId } = useJournalFocus()

  const handleSaveTitle = (value: string) => {
    setJeTitle(value)
    updateJournalEntry({
      variables: {
        input: { title: { set: value || null } },
        updateJournalEntryId: id,
      },
    })
  }

  const getJournalEntryAsMarkdown = () => {
    let markdownContent = `# ${jeTitle || t("Án titils")}\n\n`

    sections?.forEach((section) => {
      if (section.__typename !== "Note" || !section.content) return

      markdownContent += `## ${section.title || t("Án titils")}\n\n`
      markdownContent += `${convertToMarkdown(section.content)}\n\n`
    })

    return markdownContent
  }

  const handleUndoComplete = () => {
    undoCompleteJournalEntry({
      variables: {
        id,
      },
      onError: () => {
        notification.create({
          status: "error",
          message: tRoutes("undoCompletionFailed"),
        })
      },
    })
  }

  const handleCopyEntry = () => {
    const markdownContent = getJournalEntryAsMarkdown()

    navigator.clipboard
      .writeText(markdownContent)
      .then(() => {
        notification.create({
          status: "success",
          message: tRoutes("entryCopied"),
        })
      })
      .catch(() => {
        notification.create({
          status: "error",
          message: tRoutes("copyFailed"),
        })
      })
  }

  // Check if undoUntil is still valid (not in the past), properly handling UTC timestamp
  const canUndoCompleteEntry = undoUntil
    ? new Date(undoUntil).getTime() > Date.now()
    : false

  return (
    <JournalEntryContext.Provider
      value={{
        ...props,
        getJournalEntryAsMarkdown,
      }}
    >
      <aside
        className={styles.aside}
        style={{
          gridRow: `span ${entryRulerSpan}`,
        }}
      />
      <Center className={styles.heading}>
        <header className={styles.header}>
          <div className={styles.journalEntryTitle}>
            <Icon
              className={c(styles.journalEntryTitleIcon, {
                [styles.completed]: status === JournalEntryStatus.Completed,
              })}
              name={
                status === JournalEntryStatus.Completed
                  ? "checkbox-circle-line"
                  : "draft-line"
              }
            />

            <EditableText
              as="h3"
              title={jeTitle}
              fallbackText={t("Án titils")}
              textSize="default"
              inputSize="small"
              testid="journal-entry-title"
              onSave={handleSaveTitle}
              isEditable={
                canActorEdit && status !== JournalEntryStatus.Completed
              }
              wrapperElement={Heading}
            />
          </div>
          <div className={styles.subHeading}>
            <div className={c(styles.tags, color.light)}>
              {documentType && (
                <Tag
                  size="small"
                  color="blue"
                  className={styles.journalEntryTag}
                  weight="bold"
                >
                  {tEnum(`DocumentType.${documentType}.label`)}
                </Tag>
              )}
              <ProviderHoverInfoLink {...createdBy} key={id} />
            </div>
            <EntryDate date={createdAt} />
          </div>

          {inlineGuide && (
            <InlineGuidePanel
              content={inlineGuide}
              className={styles.inlineGuide}
            />
          )}

          <JournalEntryMenu
            id={id}
            status={status}
            onCopyEntry={handleCopyEntry}
          />
        </header>
      </Center>
      <ErrorBoundary
        fallback={
          <Center>
            <PanelWithIcon status="warning">
              {tRoutes("displayError")}
            </PanelWithIcon>
          </Center>
        }
      >
        {children}
      </ErrorBoundary>

      <Center className={styles.completeEntryWrap}>
        {!isCompletable &&
          status === JournalEntryStatus.Draft &&
          isPanelOpen && (
            <Panel status="info" className={styles.infoPanel}>
              <TextWithIcon className={styles.infoText} status="info">
                {tRoutes("validStateRequired")}
              </TextWithIcon>

              <Button
                className={styles.closeButton}
                onClick={() => setIsPanelOpen(false)}
                icon={<Icon name="close-circle-line" />}
              />
            </Panel>
          )}
        {status === JournalEntryStatus.Draft && canActorSign && (
          <div className={styles.completeEntryButtonBlock}>
            <Button
              iconEnd={<Icon name="list-check-3" />}
              variant={isCompletable ? "filled" : "outline"}
              onClick={() => {
                if (!isCompletable) {
                  if (firstItemPreventingComplete) {
                    setFocusedBlockId(firstItemPreventingComplete)
                  }

                  notification.create({
                    status: "warn",
                    message: tRoutes("validStateRequired"),
                  })

                  setIsPanelOpen(true)
                  return
                }

                // wait till blur on block not finishes and makes request to complete entry.
                // This solves issue making request to complete entry after block is saved
                setTimeout(() => {
                  completeJournalEntry({
                    variables: { journalEntryId: id },
                  })
                }, 100)
              }}
            >
              {tRoutes("completeEntry")}
            </Button>
          </div>
        )}
        {undoUntil && canUndoCompleteEntry && (
          <div className={styles.undoCompleteEntry}>
            <Text size="small" secondary>
              {tRoutes("entryCompletedLabel")}:
            </Text>
            <Tooltip
              tooltipContent={tRoutes("undoAvailableUntil", {
                date: dateFormat(new Date(undoUntil), { dateStyle: "long" }),
                time: timeFormat(new Date(undoUntil)),
              })}
            >
              <Button variant="clear" size="small" onClick={handleUndoComplete}>
                {tRoutes("undo")}
              </Button>
            </Tooltip>
          </div>
        )}
      </Center>
    </JournalEntryContext.Provider>
  )
}
