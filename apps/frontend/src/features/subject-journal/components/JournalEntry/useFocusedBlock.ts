import { useRef } from "react"

export default function useFocusedBlock(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  blocks: readonly { readonly id: any }[],
  focusedId?: string
) {
  const focusedBlockRef = useRef<HTMLDivElement>(null)
  const asideRef = useRef<HTMLDivElement>(null)

  // Calculate position of DocSessionBlock highlight
  const gapHeight = 64

  const focusedBlockIndex = blocks.findIndex((block) => block.id === focusedId)

  let focusedBlockOuterGapsCount = 2

  if (focusedBlockIndex === 0 || focusedBlockIndex === blocks.length - 1)
    focusedBlockOuterGapsCount = 1

  if (blocks.length === 1 || focusedBlockIndex === -1)
    focusedBlockOuterGapsCount = 0

  const extraMarginTop =
    blocks.length > 1 && focusedBlockIndex > 0 ? gapHeight : 0

  const focusedBlockMarginTop =
    (focusedBlockRef.current?.getBoundingClientRect().top &&
    asideRef.current?.getBoundingClientRect().top
      ? focusedBlockRef.current?.getBoundingClientRect().top -
        asideRef.current?.getBoundingClientRect().top
      : 0) - extraMarginTop

  const focusedBlockHeight =
    (focusedBlockRef.current?.getBoundingClientRect().height || 0) +
    gapHeight * focusedBlockOuterGapsCount

  return {
    asideRef,
    focusedBlockRef,
    focusedBlockMarginTop,
    focusedBlockHeight,
  }
}
