fragment JournalEntryWithBlocksAndEncounterStatusFragment on JournalEntry {
  id
  title
  status
  createdAt
  undoUntil
  blocks {
    ...JournalEntryBlockFragment
    ...DrugPrescriptionBlockFragment
    ...JournalEntryBlockNoteFragment
    ...MedicalCertificateSupplementFragment
    ...JournalEntryAttachmentFragment
    ...OutboundDoctorsLetterBlockFragment
    ...OutboundReferralBlockFragment
  }
  encounter {
    id
    status {
      status
      allowedTransitions
    }
  }
}
