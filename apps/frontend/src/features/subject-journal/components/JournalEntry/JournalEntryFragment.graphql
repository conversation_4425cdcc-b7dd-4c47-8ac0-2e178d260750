fragment JournalEntryFragment on JournalEntry {
  id
  title
  status
  createdAt
  createdBy {
    ...ProviderInfoFragment
  }
  canActorEdit
  canActorSign
  documentType
  inlineGuide
  undoUntil
  blocks {
    ...JournalEntryBlockFragment
    ...DrugPrescriptionBlockFragment
    ...JournalEntryBlockNoteFragment
    ...MedicalCertificateSupplementFragment
    ...JournalEntryAttachmentFragment
    ...OutboundDoctorsLetterBlockFragment
    ...OutboundReferralBlockFragment
  }
  sections {
    id
    title
    status
    blockType
    signedBy {
      id
      name
    }
    signedAt
    createdBy {
      ...ProviderInfoFragment
    }
    ...JournalEntryBlockNoteFragment
  }
}
