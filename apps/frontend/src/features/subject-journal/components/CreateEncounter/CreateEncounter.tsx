import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  matchPath,
  useLocation,
  useNavigate,
} from "react-router-dom"
import { v4 as uuid } from "uuid"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { PrivateRoutes, RouteStrings } from "routes/RouteStrings"
import { Button, FormGrid, Heading, Input, notification } from "ui"

import {
  AllTeamsQuery,
  EncounterCreateInput,
  namedOperations,
  ProviderInfoFragmentFragment,
  ServiceType,
  useAllTeamsQuery,
  useCreateEncounterMutation,
  useCreateInterventionPeriodAndEncounterMutation,
  useGetInterventionPeriodsQuery,
} from "generated/graphql"

import AddResponsibleProviders from "../AddResponsibleProviders/AddResponsibleProviders"
import styles from "./CreateEncounter.module.css"
import { CreateEncounterButton } from "./CreateEncounterButton/CreateEncounterButton"
import { usePreferredNewEncounterStatus } from "./CreateEncounterButton/usePreferredNewEncounterStatus"

const CreateEncounterSchema = z.object({
  reason: z.string().min(3),
  priority: z
    .string()
    .transform((value) => (value === "" ? null : Number(value))),
  note: z.string().optional(),
  primaryTeamId: z.string().uuid(),
  interventionPeriod: z
    .string()
    .uuid()
    .or(z.literal("new"))
    .or(z.literal("none"))
    .optional(),
  interventionPeriodTitle: z.string().min(3).optional(),
})

type CreateEncounterFormProps = {
  defaultInterventionPeriodId?: string
  formClassName?: string
  interventionPeriods?: Array<{ id: string; title: string }>
  members: ProviderInfoFragmentFragment[]
  refetchSubjectSummaryEncounters?: boolean
  subjectId: string

  onCancel?: () => void
}

const CreateEncounterForm = ({
  defaultInterventionPeriodId,
  formClassName,
  interventionPeriods = [],
  members,
  refetchSubjectSummaryEncounters = false,
  subjectId,

  onCancel,
}: CreateEncounterFormProps) => {
  const { getPreferredNewEncounterStatus } = usePreferredNewEncounterStatus()

  const {
    globalState: { currentTeamId: primaryTeamId },
  } = useGlobalState()

  const [responsibleProviders, setResponsibleProviders] = useState<string[]>([])

  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )
  const navigate = useNavigate()
  const { t } = useTranslation()
  const { pathname } = useLocation()
  const isInSubjectJournal = matchPath(
    { path: PrivateRoutes.subjectJournal },
    pathname
  )

  // Only fetch intervention periods if they weren't passed as props i.e. PersonSummary
  const { data } = useGetInterventionPeriodsQuery({
    variables: { subjectId },
    skip: interventionPeriods.length > 0,
  })

  const availableInterventionPeriods =
    interventionPeriods.length > 0
      ? interventionPeriods
      : data?.subjectJournal.interventionPeriods || []

  const selectTeamStore = useSelectStore({
    defaultValue: "",
  })

  const setDefaultTeamValue = (data: AllTeamsQuery) => {
    const primaryTeamInOptions = data.teams.some(
      (team) => team.id === primaryTeamId
    )

    if (primaryTeamInOptions && primaryTeamId !== null) {
      selectTeamStore.setValue(primaryTeamId)
    }
  }

  const {
    data: teamsData,
    loading: teamsLoading,
    error: teamsError,
  } = useAllTeamsQuery({
    variables: {
      serviceType: ServiceType.ClinicalAttendance,
    },
    onCompleted: (data) => {
      setDefaultTeamValue(data)
    },
  })

  const interventionPeriodOptions = [
    {
      label: t("None"),
      value: "none",
    },
    ...availableInterventionPeriods.map(({ id, title }) => ({
      label: title,
      value: id,
    })),
    {
      label: t("+ Create new intervention period"),
      value: "new",
    },
  ]

  const selectInterventionPeriodStore = useSelectStore({
    defaultValue: defaultInterventionPeriodId || "none",
  })

  const selectedInterventionPeriod =
    selectInterventionPeriodStore.useState().value

  const teamsOptions =
    teamsData?.teams.map((team) => ({
      value: team.id as string,
      label: team.name,
    })) || []

  const resetForm = () => {
    setResponsibleProviders([])
    setValidationError(null)
    selectInterventionPeriodStore.setValue(interventionPeriodOptions[0].value)
    selectTeamStore.setValue("")

    if (teamsData) setDefaultTeamValue(teamsData)
  }

  const refetchQueries = [
    // need to fetch the subject journal to show newly created encounters
    ...(isInSubjectJournal ? [namedOperations.Query.GetSubjectJournal] : []),
    ...(!isInSubjectJournal
      ? [namedOperations.Query.DashboardStatusCountHack]
      : []),
    ...(refetchSubjectSummaryEncounters
      ? [namedOperations.Query.GetSubjectSummaryEncounters]
      : []),
  ]
  const onRequestCompleted = () => {
    resetForm()
    onCancel?.()
  }

  const [createEncounter, { error, loading }] = useCreateEncounterMutation({
    onCompleted: () => {
      notification.create({
        status: "success",
        message: t("Encounter has been created"),
      })
      onRequestCompleted?.()
      if (isInSubjectJournal) {
        // if the encounter was created in the subject journal, we don't need to navigate
        return
      }
      const subjectJournalPath = generatePath(RouteStrings.subjectJournal, {
        subjectId,
      })

      navigate(subjectJournalPath)
    },
    // update was removed, because updating the cache to include the new encounter didn't work i.e. new encounters weren't shown
    refetchQueries,
  })

  const [
    createInterventionPeriodAndEncounter,
    {
      error: createInterventionPeriodAndEncounterError,
      loading: loadingCreateEncounterPeriodAndEncounter,
    },
  ] = useCreateInterventionPeriodAndEncounterMutation({
    onCompleted: () => {
      notification.create({
        status: "success",
        message: t("Encounter and Intervention Period has been created"),
      })
      onRequestCompleted?.()
    },
    refetchQueries: [
      namedOperations.Query.GetInterventionPeriods,
      ...refetchQueries,
    ],
  })

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    setValidationError(null)

    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())

    const validatedInput = CreateEncounterSchema.safeParse({
      ...data,
    })

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    const encounterInput: EncounterCreateInput = {
      fromDate: new Date(),
      reason: validatedInput.data.reason,
      priority: validatedInput.data.priority,
      note: validatedInput.data.note,
      responsibleProviders: responsibleProviders,
      subjectId,
      primaryTeamId: validatedInput.data.primaryTeamId,
      status: getPreferredNewEncounterStatus(),
    }

    if (validatedInput.data.interventionPeriod !== "new") {
      const interventionPeriod = validatedInput.data.interventionPeriod
      const encounterId = uuid()

      createEncounter({
        variables: {
          createEncounterId: encounterId,
          input: {
            ...encounterInput,
            interventionPeriodId:
              interventionPeriod === "none" ? null : interventionPeriod,
          },
        },
      })

      return
    }

    const interventionPeriodId = uuid()
    const interventionPeriodInput = {
      subjectId,
      title: validatedInput.data.interventionPeriodTitle || "",
    }

    createInterventionPeriodAndEncounter({
      variables: {
        interventionPeriodId,
        interventionPeriodInput,
        encounterInput: {
          ...encounterInput,
          interventionPeriodId,
        },
      },
    })
  }

  return (
    <div className={formClassName}>
      <FormGrid onSubmit={handleSubmit} colSpan={12}>
        <Heading>{t("Add new encounter")}</Heading>
        <Input
          label={"Reason"}
          name="reason"
          className={styles.span9}
          required
          minLength={3}
        />
        <Input
          label={"Priority"}
          name="priority"
          type="number"
          className={styles.span3}
        />
        <Input
          label={t("Team message")}
          name="note"
          className={styles.span12}
        />
        <Select
          label={t("Team")}
          name="primaryTeamId"
          selectStore={selectTeamStore}
          options={teamsOptions}
          isLoading={teamsLoading}
          message={teamsError?.message}
          data-testid="primary-team-select"
        />
        <AddResponsibleProviders
          responsibleProviders={responsibleProviders}
          members={members}
          className={styles.span12}
          onAddResponsibleProvider={(providerId) => {
            setResponsibleProviders((prev) => [...prev, providerId])
          }}
          onDeleteResponsibleProvider={(providerId) => {
            setResponsibleProviders((prev) =>
              prev.filter((id) => id !== providerId)
            )
          }}
          hideFooter
        />
        <Select
          label={t("Intervention period")}
          name="interventionPeriod"
          selectStore={selectInterventionPeriodStore}
          options={interventionPeriodOptions}
        />
        {selectedInterventionPeriod === "new" && (
          <Input
            label={t("Intervention period title")}
            name="interventionPeriodTitle"
            required={true}
          />
        )}
        {(validationError ||
          error ||
          createInterventionPeriodAndEncounterError) && (
          <Panel status="error" className={styles.span12}>
            {validationError?.message}
            {error?.message}
            {createInterventionPeriodAndEncounterError?.message}
          </Panel>
        )}
        <div className={`${styles.formFooter} ${styles.span12}`}>
          {onCancel && <Button onClick={onCancel}>{t("Cancel")}</Button>}

          <CreateEncounterButton
            loading={loading || loadingCreateEncounterPeriodAndEncounter}
          />
        </div>
      </FormGrid>
    </div>
  )
}

type CreateEncounterProps = CreateEncounterFormProps & {
  showAddEncounterForm: boolean
  onAddEncounter?: () => void
}

export const CreateEncounter = ({
  showAddEncounterForm,
  onAddEncounter,
  ...rest
}: CreateEncounterProps) => {
  const { t } = useTranslation()

  return (
    <div
      className={styles.wrap}
      data-show-encounter-form={showAddEncounterForm}
    >
      {!showAddEncounterForm && (
        <Button
          icon={<Icon name={"add-line"} />}
          className={styles.addEncounterButton}
          onClick={onAddEncounter}
          variant="filled"
        >
          {t("Add encounter")}
        </Button>
      )}

      {showAddEncounterForm && <CreateEncounterForm {...rest} />}
    </div>
  )
}
