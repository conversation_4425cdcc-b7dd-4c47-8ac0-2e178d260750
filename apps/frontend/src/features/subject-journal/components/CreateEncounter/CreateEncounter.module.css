.wrap[data-show-encounter-form="true"] {
  background: var(--color-white);
  color: var(--color-text);
}

.wrap[data-show-encounter-form="false"] {
  display: flex;
  justify-content: flex-end;
}

.addEncounterButton.addEncounterButton {
  text-align: right;
}

.span9 {
  grid-column: span 9;
}

.span3 {
  grid-column: span 3;
}

.span12 {
  grid-column: span 12;
}

.formFooter {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}
