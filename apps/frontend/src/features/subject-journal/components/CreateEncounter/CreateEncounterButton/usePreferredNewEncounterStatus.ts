import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

import { EncounterStatus } from "generated/graphql"

const actorIdSuffix = "createEncounterType"
export const usePreferredNewEncounterStatus = () => {
  const { globalData } = useGlobalState()
  const actorId = globalData.actor.id

  const setPreferredNewEncounterStatus = (encounterStatus: EncounterStatus) => {
    localStorage.setItem(`${actorId}${actorIdSuffix}`, encounterStatus)
  }

  const getPreferredNewEncounterStatus = (): EncounterStatus => {
    const storedValue = localStorage.getItem(`${actorId}${actorIdSuffix}`)

    if (!storedValue) return EncounterStatus.Planned

    const encounterStatuses = Object.values(EncounterStatus)

    if (encounterStatuses.includes(storedValue as EncounterStatus)) {
      return storedValue as EncounterStatus
    }

    return EncounterStatus.Planned
  }

  return { setPreferredNewEncounterStatus, getPreferredNewEncounterStatus }
}
