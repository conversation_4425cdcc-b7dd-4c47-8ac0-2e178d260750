import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { Menu, MenuButton } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { Button, ButtonGroup, Text } from "ui"

import { EncounterStatus } from "generated/graphql"

import styles from "./CreateEncounterButton.module.css"
import { usePreferredNewEncounterStatus } from "./usePreferredNewEncounterStatus"

type CreateEncounterButtonProps = {
  loading?: boolean
}

export const CreateEncounterButton = ({
  loading,
}: CreateEncounterButtonProps) => {
  const { setPreferredNewEncounterStatus, getPreferredNewEncounterStatus } =
    usePreferredNewEncounterStatus()

  const menuLocationRef = useRef<HTMLDivElement>(null)

  const [encounterStatus, setEncounterStatus] = useState<EncounterStatus>(
    getPreferredNewEncounterStatus()
  )

  const menu = useMenuStore()

  const { t } = useTranslation()

  useEffect(() => {
    menu.setAnchorElement(menuLocationRef.current)
  }, [menu, menuLocationRef])

  const buttonText = loading
    ? t("Creating encounter…")
    : encounterStatus === EncounterStatus.InProgress
      ? t("Create and check-in")
      : t("Create encounter")

  const isInProgress = encounterStatus === EncounterStatus.InProgress

  return (
    <>
      <Menu
        store={menu}
        composite={false}
        className={styles.menu}
        // COMEBACK: Remove when https://github.com/ariakit/ariakit/issues/3342 is resolved
        preserveTabOrderAnchor={null}
      >
        <MenuItem
          className={styles.menuItem}
          onClick={() => {
            setPreferredNewEncounterStatus(EncounterStatus.Planned)
            setEncounterStatus(EncounterStatus.Planned)
          }}
        >
          {!isInProgress && (
            <Icon className={styles.checkIcon} name="check-line" />
          )}
          <Text>{t("Create encounter")}</Text>
        </MenuItem>
        <MenuItem
          className={styles.menuItem}
          onClick={() => {
            setPreferredNewEncounterStatus(EncounterStatus.InProgress)
            setEncounterStatus(EncounterStatus.InProgress)
          }}
        >
          {isInProgress && (
            <Icon className={styles.checkIcon} name="check-line" />
          )}
          <Text>{t("Create and check-in")}</Text>
        </MenuItem>
      </Menu>
      <ButtonGroup ref={menuLocationRef}>
        <Button
          variant="filled"
          data-testid="intervention-period-submit"
          type="submit"
          className={styles.submitButton}
          icon={loading && <Icon name="loader-4-line" spin />}
          disabled={loading}
        >
          {buttonText}
        </Button>
        <MenuButton
          variant="filled"
          icon={<Icon name="arrow-down-s-line" />}
          store={{ ...menu, setAnchorElement: () => null }}
        ></MenuButton>
      </ButtonGroup>
    </>
  )
}
