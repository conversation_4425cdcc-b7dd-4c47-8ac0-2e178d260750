import { RefetchQueriesInclude } from "@apollo/client"
import c from "classnames"
import { useCallback, useState } from "react"
import { useTranslation } from "react-i18next"

import {
  DisclosureContent,
  DisclosureProvider,
} from "components/Ariakit/Disclosure/Disclosure"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { UpcomingEventCard } from "features/dashboard/components/SubjectUpcomingEvents/UpcomingEventCard"
import { Heading, JournalGrid, Text, TextWithIcon } from "ui"
import { ProviderHoverInfoLink } from "ui/components/ProviderBadgeMenu/ProviderHoverInfoLink/ProviderHoverInfoLink"
import { mergeRefs } from "ui/lib/mergeRefs"

import {
  CommunicationStatus,
  DoctorLetterResolutionStatus,
  InboundDataFragmentFragment,
  InboundDataType,
  ListItemType,
  PermissionKey,
  ReferralResolutionStatus,
} from "generated/graphql"

import { EntryDate } from "../EntryDate/EntryDate"
import { useJournalDataInView } from "../JournalDataInView/JournalDataInView.context"
import { useJournalBlockFocus } from "../JournalFocus/JournalFocus"
import styles from "./InboundData.module.css"
import { InboundDataActions } from "./InboundDataActions"
import { InboundDataHeader } from "./InboundDataHeader"
import { WaitingListEntry } from "./ReferralWaitingListEntry/ReferralWaitingListEntry"

function isActive(status: CommunicationStatus) {
  return ![
    CommunicationStatus.Cancelled,
    CommunicationStatus.Declined,
    CommunicationStatus.Failed,
  ].includes(status)
}

type InboundDataProps = InboundDataFragmentFragment
export const InboundData = ({
  communicationStatus,
  createdAt,
  entries,
  inboundType,
  listItem,
  receiver,
  sender,
  interventionPeriod,
  comment,
  headerClassName,
  open,
  setOpen,
  collapsable = false,
  refetchQueries,
}: InboundDataProps & {
  headerClassName?: string
  open?: boolean
  setOpen?: () => void
  collapsable?: boolean
  refetchQueries?: RefetchQueriesInclude
}) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.inboundData",
  })

  const { t: tEnum } = useTranslation("enums")

  const active = isActive(communicationStatus)

  //  we assume that the main message will always be in the first section of the first entry
  const message = entries?.[0]?.sections?.[0]?.content || ""
  const messageId = entries?.[0]?.sections?.[0]?.id || ""
  const { ref } = useJournalBlockFocus<HTMLDivElement>({
    id: messageId,
  })

  const hasReferralListItem =
    inboundType === InboundDataType.Referral &&
    listItem &&
    listItem.__typename === "ReferralItem"

  // Check for the types for inbound types in list item to be able to read correct properties such as rejection
  // which is not on other types of list item
  const listItemObj =
    listItem?.__typename === "ReferralItem" ||
    listItem?.__typename === "DoctorLetterItem"
      ? listItem
      : null

  const { rejectedAt, rejectedBy, rejectionReason, itemType } =
    listItemObj || {}

  const resolution =
    listItemObj?.__typename === "DoctorLetterItem"
      ? listItemObj?.doctorLetterResolution
      : listItemObj?.referralResolution

  // Moved inside the component to access translation functions directly
  const getResolutionHeader = (
    resolution: DoctorLetterResolutionStatus | ReferralResolutionStatus,
    type: ListItemType
  ) => {
    switch (resolution) {
      case DoctorLetterResolutionStatus.Rejected:
      case ReferralResolutionStatus.Rejected:
        // Provider declined
        return tRoutes("declined", {
          inboundType: tEnum(`InboundDataType.${type}`),
        })
      case ReferralResolutionStatus.Declined:
        // Subject cancelled
        return tRoutes("cancelled", {
          inboundType: tEnum(`InboundDataType.${type}`),
        })
      default:
        return null
    }
  }

  // Content to be rendered inside disclosure or standard container
  const content = (
    <>
      {comment && (
        <TextWithIcon
          size="small"
          iconName="chat-2-line"
          className={styles.commentFromSender}
        >
          {comment}
        </TextWithIcon>
      )}

      <Restricted to={PermissionKey.SubjectJournalInboundEntryView}>
        <PiiSensitive as={Text} className={styles.message} id={messageId}>
          {message}
        </PiiSensitive>

        {rejectedAt && rejectedBy && resolution && itemType && (
          <div className={styles.entry}>
            <Heading as="h4" className={styles.entryTitle}>
              <Icon
                name="corner-down-right-line"
                className={styles.entryTitleIcon}
              />
              {getResolutionHeader(resolution, itemType)}
            </Heading>
            <div className={styles.subHeading}>
              <ProviderHoverInfoLink {...rejectedBy} />
              <EntryDate date={rejectedAt} />
            </div>
            {rejectionReason && (
              <Text className={styles.rejection}>{rejectionReason}</Text>
            )}
          </div>
        )}
      </Restricted>
      {hasReferralListItem &&
        (listItem.eventInstance ? (
          <ul className={styles.upcomingEvent}>
            <UpcomingEventCard
              eventInstance={listItem.eventInstance}
              interventionPeriodId={interventionPeriod?.id}
            />
          </ul>
        ) : (
          // @ts-expect-error - This is a circular type dependency that we need to fix in another PR
          <WaitingListEntry {...listItem} isActive={active} />
        ))}

      {listItem && (
        <div className={styles.actions}>
          <InboundDataActions
            listItem={listItem}
            inboundType={inboundType}
            status={communicationStatus}
            {...(refetchQueries && { refetchQueries })}
          />
        </div>
      )}
    </>
  )

  return (
    <article className={c(styles.wrap, "inboundData")} ref={ref}>
      {collapsable ? (
        <DisclosureProvider open={open} setOpen={setOpen}>
          <InboundDataHeader
            createdAt={createdAt}
            inboundType={inboundType}
            receiver={receiver}
            sender={sender}
            status={communicationStatus}
            className={headerClassName}
            collapsable={true}
          />

          <DisclosureContent className={styles.content}>
            {content}
          </DisclosureContent>
        </DisclosureProvider>
      ) : (
        <>
          <InboundDataHeader
            createdAt={createdAt}
            inboundType={inboundType}
            receiver={receiver}
            sender={sender}
            status={communicationStatus}
            className={headerClassName}
          />
          <div className={styles.content}>{content}</div>
        </>
      )}
    </article>
  )
}

export const JournalInboundData = (props: InboundDataProps) => {
  const { collapsed, id } = props

  const [open, setOpen] = useState(!collapsed)
  const toggle = useCallback(() => setOpen((prev) => !prev), [setOpen])

  const { useJournalDataInViewRef } = useJournalDataInView()

  const journalDataRef = useJournalDataInViewRef(id)

  const { ref: refFocus } = useJournalBlockFocus<HTMLDivElement>({
    id,
  })

  return (
    <JournalGrid
      rowGap
      id={id}
      data-id={id}
      ref={mergeRefs([journalDataRef, refFocus])}
    >
      <aside className={styles.aside} />

      <InboundData {...props} open={open} setOpen={toggle} collapsable={true} />
    </JournalGrid>
  )
}
