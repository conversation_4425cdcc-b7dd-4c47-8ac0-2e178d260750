import { RefetchQueriesInclude } from "@apollo/client"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RejectListItem } from "features/waiting-list/components/RejectListItem/RejectListItem"
import { WaitingListBookAppointmentModal } from "features/waiting-list/components/WaitingListBookAppointmentModal/WaitingListBookAppointmentModal"
import { RouteStrings } from "routes/RouteStrings"
import { Button, notification, Text, Textarea } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import ExtractByTypename from "utils/ExtractByTypename"
import { isTypename } from "utils/isTypename"

import {
  CommunicationStatus,
  InboundDataFragmentFragment,
  InboundDataType,
  ListItemType,
  useAcceptListItemMutation,
  useRejectListItemMutation,
  InboundListItemFragment,
  namedOperations,
  UpcomingEventFragmentFragment,
} from "generated/graphql"

import styles from "./InboundDataActions.module.css"

export type ReferralListItem = ExtractByTypename<
  InboundListItemFragment,
  "ReferralItem"
>

const CancelReferralWithEventDialog = ({
  event,
}: {
  event: UpcomingEventFragmentFragment
}) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.inboundData",
  })
  const getCalendarPath = useGetCalendarPathObject()

  const [showDialog, setShowDialog] = useState(false)

  const eventId = event.id
  const fromDate = event.fromDate.split("T")[0]

  // Take first provider in the event
  // if there is no provider set, we can't navigate to the calendar and skip showing the button
  const providerId =
    event.participants.filter(isTypename("ParticipantProvider"))?.[0]?.provider
      ?.id || null

  return (
    <>
      <Button
        icon={<Icon name="close-line" />}
        onClick={() => setShowDialog(true)}
      >
        {tRoutes("decline")}
      </Button>
      <Dialog
        title={tRoutes("declineClinicalReferral")}
        isOpen={showDialog}
        onClose={() => setShowDialog(false)}
        closeOnClickOutside
        actions={
          providerId ? (
            <>
              <Button onClick={() => setShowDialog(false)} variant="clear">
                {tRoutes("close")}
              </Button>
              <Button
                as={Link}
                variant="filled"
                to={getCalendarPath(RouteStrings.calendarViewEventInstance, {
                  eventId,
                  search: { provider: providerId, date: fromDate },
                })}
              >
                {tRoutes("goToEvent")}
              </Button>
            </>
          ) : (
            <Button onClick={() => setShowDialog(false)} variant="filled">
              {tRoutes("close")}
            </Button>
          )
        }
      >
        {tRoutes("cantDeclineReferralHasBookedEvent")}
      </Dialog>
    </>
  )
}

const DeclineDialog = ({
  show,
  hideDialog,
  handleDecline,
  title,
  message,
  reasonRequired,
}: {
  show: boolean
  hideDialog: () => void
  handleDecline: (e: FormEvent<HTMLFormElement>) => void
  title: string
  message: string
  reasonRequired: boolean
}) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.inboundData",
  })
  const formId = "decline-form"

  return (
    <Dialog
      title={title}
      isOpen={show}
      contentClassName={styles.dialog}
      onClose={hideDialog}
      closeOnClickOutside
      actions={
        <>
          <Button onClick={hideDialog} variant="clear">
            {tRoutes("close")}
          </Button>
          <Button form={formId} variant="filled" type="submit">
            {tRoutes("decline")}
          </Button>
        </>
      }
    >
      <form id={formId} className={styles.form} onSubmit={handleDecline}>
        <Text>{message}</Text>
        <Textarea
          name="reason"
          label={tRoutes("reasonForDeclining")}
          autoGrow
          required={reasonRequired}
          onKeyDown={(e) => {
            if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
              e.preventDefault()
              e.currentTarget.form?.dispatchEvent(
                new Event("submit", { bubbles: true, cancelable: true })
              )
            }
          }}
        />
      </form>
    </Dialog>
  )
}

type InboundDataActionsProps = {
  listItem: InboundDataFragmentFragment["listItem"]
  inboundType: InboundDataType
  status: CommunicationStatus
  refetchQueries?: RefetchQueriesInclude
}
export const InboundDataActions = ({
  listItem,
  inboundType,
  status,
  refetchQueries = [namedOperations.Query.GetSubjectJournal],
}: InboundDataActionsProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.inboundData",
  })

  const [bookAppointmentItem, setBookAppointmentItem] =
    useState<ReferralListItem | null>(null)

  const [showDeclineDialog, setShowDeclineDialog] = useState(false)
  const [rejectListItem] = useRejectListItemMutation()
  const [acceptListItem] = useAcceptListItemMutation()

  // If there is no listItem we cannot do any actions
  if (!listItem) return null

  const hasEvent =
    listItem.__typename === "ReferralItem" && !!listItem.eventInstance

  const declineClinicalCorrespondence = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const reason = formData.get("reason") as string

    rejectListItem({
      variables: {
        input: {
          id: listItem.id,
          listItemType: ListItemType.DoctorLetter,
          reason: reason,
        },
      },
      refetchQueries,
      onCompleted: () => {
        setShowDeclineDialog(false)
        notification.create({
          status: "success",
          message: tRoutes("clinicalCorrespondenceDeclined"),
        })
      },
    })
  }

  const handleAcceptListItem = (type: ListItemType) => {
    acceptListItem({
      variables: {
        input: {
          id: listItem.id,
          listItemType: type,
        },
      },
      refetchQueries,
    })
  }

  // We only show actions for DoctorLetter when status is Received
  if (
    inboundType === InboundDataType.DoctorLetter &&
    status === CommunicationStatus.Received
  ) {
    return (
      <>
        <Button
          icon={<Icon name="close-line" />}
          onClick={() => setShowDeclineDialog(true)}
        >
          {tRoutes("decline")}
        </Button>
        <Button
          variant="filled"
          icon={<Icon name="checkbox-circle-line" />}
          onClick={() => handleAcceptListItem(ListItemType.DoctorLetter)}
        >
          {tRoutes("markAsRead")}
        </Button>
        <DeclineDialog
          show={showDeclineDialog}
          hideDialog={() => setShowDeclineDialog(false)}
          handleDecline={declineClinicalCorrespondence}
          title={tRoutes("declineClinicalCorrespondence")}
          message={tRoutes("declineClinicalCorrespondenceMessage")}
          reasonRequired={false}
        />
      </>
    )
  }

  // We only show actions for Referrals when status is Received or Read
  if (
    inboundType === InboundDataType.Referral &&
    (status === CommunicationStatus.Received ||
      status === CommunicationStatus.Read)
  ) {
    return (
      <>
        {hasEvent && listItem.eventInstance ? (
          <CancelReferralWithEventDialog event={listItem.eventInstance} />
        ) : (
          <RejectListItem
            buttonLabel={tRoutes("decline")}
            id={listItem.id}
            type={ListItemType.Referral}
            refetchQueries={refetchQueries}
          />
        )}
        {status === CommunicationStatus.Received && (
          <Button
            variant="filled"
            icon={<Icon name="checkbox-circle-line" />}
            onClick={() => handleAcceptListItem(ListItemType.Referral)}
          >
            {tRoutes("accept")}
          </Button>
        )}
        {status === CommunicationStatus.Read && !hasEvent && (
          <Button
            variant="filled"
            icon={<Icon name="calendar-2-line" />}
            onClick={() => setBookAppointmentItem(listItem as ReferralListItem)}
          >
            {tRoutes("bookAppointment")}
          </Button>
        )}
        <WaitingListBookAppointmentModal
          // @ts-expect-error - This is a circular dependency that we need to fix in another PR
          waitingListItem={bookAppointmentItem}
          onClose={() => {
            setBookAppointmentItem(null)
          }}
        />
      </>
    )
  }

  return null
}
