import { MockedProvider } from "@apollo/client/testing"
import { screen } from "@testing-library/react"
import { mockGlobalData } from "test/mocks/GlobalStateMock"
import { render } from "test/testUtils"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"

import { InboundDataFragmentFragment, InboundReceiver } from "generated/graphql"

import { JournalInboundData } from "./InboundData"
import {
  mockInboundExternalData,
  mockInboundLeviosaData,
} from "./mockedInboundData"

vi.mock(
  "ui/components/ProviderBadgeMenu/ProviderHoverInfoLink/ProviderHoverInfoLink",
  () => ({
    ProviderHoverInfoLink: () => <div></div>,
  })
)

vi.mock("ui/components/EditableNote/EditableNote", () => ({
  EditableNote: () => <div></div>,
}))

vi.mock("react-intersection-observer", () => ({
  useInView: () => ({
    ref: null,
    inView: true,
  }),
}))

vi.mock(
  "features/subject-journal/components/JournalDataInView/JournalDataInView.context",
  () => ({
    useJournalDataInView: () => ({
      firstVisibleJournalData: "mockId",
      markJournalDataInView: vi.fn(),
      useJournalDataInViewRef: () => () => null,
    }),
  })
)

const TestComp = (props: InboundDataFragmentFragment) => (
  <MockedProvider>
    <GlobalProvider currentTeamId={"fakeId"} globalData={mockGlobalData}>
      <JournalInboundData {...props} />
    </GlobalProvider>
  </MockedProvider>
)

const renderComponent = (props: InboundDataFragmentFragment) =>
  render(<TestComp {...props} />)

describe("InboundData", () => {
  it("should render the correct type and status", () => {
    renderComponent(mockInboundLeviosaData)

    expect(screen.getByText(/REFERRAL/i)).toBeInTheDocument()
    expect(screen.getByText(/READ/i)).toBeInTheDocument()
  })

  it("should render correct sender", () => {
    const { rerender } = renderComponent(mockInboundExternalData)

    expect(screen.getByText(/Jón Jónsson/i)).toBeInTheDocument()
    expect(screen.getByText(/Læknasetrið/i)).toBeInTheDocument()
    expect(screen.queryByText(/Svæfing/i)).toBeNull()
    expect(screen.getByTitle(/Svæfing/i)).toBeInTheDocument()

    const inboundDataProps = {
      ...mockInboundExternalData,
      sender: { ...mockInboundExternalData.sender, providerName: null },
    }

    rerender(<TestComp {...inboundDataProps} />)

    expect(screen.queryByText(/Jón Jónsson/i)).toBeNull()
    expect(screen.getByText(/Læknasetrið/i)).toBeInTheDocument()
    expect(screen.getByText(/Svæfing/i)).toBeInTheDocument()
    expect(screen.queryByTitle(/Svæfing/i)).toBeNull()
  })

  it("should render correct receiver", () => {
    const { rerender } = renderComponent(mockInboundLeviosaData)

    expect(screen.getByText(/Albus Dumbledore/i)).toBeInTheDocument()
    expect(screen.queryByText(/Leviosa/i)).toBeNull()

    const propsWithoutReceiver = {
      ...mockInboundLeviosaData,
      receiver: null,
    }

    rerender(<TestComp {...propsWithoutReceiver} />)

    expect(screen.queryByText(/Albus Dumbledore/i)).toBeNull()
    expect(screen.getByText(/Leviosa/i)).toBeInTheDocument()

    const mockReceiverWithoutProvider: InboundReceiver = {
      provider: null,
      __typename: "InboundReceiver",
      departmentName: null,
      id: "",
      providerExternalNumber: null,
      providerName: null,
      team: null,
    }

    const propsWithoutReceiverProvider = {
      ...mockInboundLeviosaData,
      receiver: mockReceiverWithoutProvider,
    }

    rerender(<TestComp {...propsWithoutReceiverProvider} />)

    expect(screen.queryByText(/Albus Dumbledore/i)).toBeNull()
    expect(screen.getByText(/Leviosa/i)).toBeInTheDocument()

    rerender(<TestComp {...mockInboundExternalData} />)

    expect(screen.getByText(/Nonni/i)).toBeInTheDocument()
    expect(screen.getByText(/Blabla/i)).toBeInTheDocument()
    expect(screen.queryByText(/Leviosa/i)).toBeNull()
  })
})
