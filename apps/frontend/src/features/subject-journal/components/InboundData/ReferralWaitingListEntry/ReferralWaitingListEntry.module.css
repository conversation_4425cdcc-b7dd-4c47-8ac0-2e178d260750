.wrap {
  padding: 16px 0;
  border-radius: 16px;
}

.titleWrap {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 4px 8px 24px;
}

.tableWrap {
  padding-left: 8px;
  padding-right: 24px;
}

.table {
  --color-table: var(--color-black);
  --color-table-bg-odd: var(--color-white);
  --color-table-header-bg: transparent;
  --color-table-header-text: var(--color-text);
}

.table.table * {
  font-size: 14px;
}
.table.table th {
  font-size: 12px;
}

.table :where(tbody:last-child tr:last-of-type td:first-of-type) {
  border-top-left-radius: 32px;
  border-bottom-left-radius: 32px;
}
.table :where(tbody:last-child tr:last-of-type td:last-of-type) {
  border-top-right-radius: 32px;
  border-bottom-right-radius: 32px;
}

.note {
  padding: 16px 8px 0 24px;
  color: var(--color-text);
}

.noteLabel {
  padding-bottom: 4px;
}

.noteText > div {
  font-size: 14px;
}
