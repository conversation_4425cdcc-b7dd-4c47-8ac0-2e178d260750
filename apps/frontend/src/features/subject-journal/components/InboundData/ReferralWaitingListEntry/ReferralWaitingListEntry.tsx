import c from "classnames"
import { useTranslation } from "react-i18next"
import { generateP<PERSON>, <PERSON> } from "react-router-dom"

import { ListItemAssigneeSelect } from "components/ListItemAssigneeSelect/ListItemAssigneeSelect"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { WaitingListNote } from "features/waiting-list/components/WaitingListTable/WaitingListNote/WaitingListNote"
import { WaitingListPrioritySelect } from "features/waiting-list/components/WaitingListTable/WaitingListPrioritySelect/WaitingListPrioritySelect"
import { WaitingListServiceTypeSelect } from "features/waiting-list/components/WaitingListTable/WaitingListServiceTypeSelect/WaitingListServiceTypeSelect"
import { WaitingListTeamSelect } from "features/waiting-list/components/WaitingListTable/WaitingListTeamSelect/WaitingListTeamSelect"
import { RouteStrings } from "routes/RouteStrings"
import { color } from "styles/colors"
import { <PERSON><PERSON>, Heading, Label, Table } from "ui"

import { namedOperations } from "generated/graphql"

import styles from "./ReferralWaitingListEntry.module.css"

type WaitingListEntryProps = WaitingListItem & {
  isActive: boolean
  waitingListPath?: string | null
}
export const WaitingListEntry = (props: WaitingListEntryProps) => {
  const {
    assignee,
    comment,
    isActive,
    priority,
    serviceType,
    team,
    waitingListPath,
  } = props

  const { t: tRoutesWL } = useTranslation("routes", {
    keyPrefix: "waitingList",
  })
  const { t: tRoutesID } = useTranslation("routes", {
    keyPrefix: "subjectJournal.inboundData",
  })

  const showNote = isActive || comment

  return (
    <div
      className={c(styles.wrap, {
        [color.neutral.light]: !isActive,
        [color.levBlue.light]: isActive,
      })}
    >
      <div className={styles.titleWrap}>
        <Heading size="small" as="h5">
          {tRoutesID("waitingListEntryTitle")}
        </Heading>
        {isActive && (
          <Button
            as={Link}
            variant="clear"
            size="small"
            to={
              waitingListPath ||
              generatePath(RouteStrings.waitingList, {
                pageNumber: 1,
              })
            }
          >
            {tRoutesID("openWaitingListButton")}
          </Button>
        )}
      </div>

      <div className={styles.tableWrap}>
        <Table spacing="narrow" hoverable={false} className={styles.table}>
          <thead>
            <tr>
              <th />
              <th>{tRoutesWL("tableColumns.assignee")}</th>
              <th>{tRoutesWL("tableColumns.serviceType")}</th>
              <th>{tRoutesWL("tableColumns.team")}</th>
            </tr>
          </thead>

          <tbody>
            <tr>
              <td>
                <WaitingListPrioritySelect
                  isEditable={isActive}
                  value={priority}
                  waitingListItem={props}
                />
              </td>
              <td>
                <ListItemAssigneeSelect
                  isEditable={isActive}
                  id={props.id}
                  value={assignee?.name}
                  waitingListItem={props}
                  refetchQueries={[
                    namedOperations.Query.GetWaitingListItemsCount,
                    namedOperations.Query.ProviderBox,
                  ]}
                />
              </td>
              <td>
                <WaitingListServiceTypeSelect
                  isEditable={isActive}
                  value={serviceType?.name}
                  waitingListItem={props}
                />
              </td>
              <td>
                <WaitingListTeamSelect
                  isEditable={isActive}
                  value={team?.id}
                  waitingListItem={props}
                />
              </td>
            </tr>
          </tbody>
        </Table>
      </div>

      {showNote && (
        <div className={styles.note}>
          <Label className={styles.noteLabel}>{tRoutesWL("note.label")}</Label>
          <WaitingListNote
            className={styles.noteText}
            isEditable={isActive}
            value={comment}
            waitingListItem={props}
          />
        </div>
      )}
    </div>
  )
}
