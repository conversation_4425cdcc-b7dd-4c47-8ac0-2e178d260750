import "@ariakit/react"
import { Meta } from "@storybook/react-vite"

import { GlobalProvider } from "components/GlobalDataContext/GlobalData.context"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"

import { InboundData } from "./InboundData"
import {
  mockInboundExternalData,
  mockInboundLeviosaData,
} from "./inboundDataMockData"

export default {
  title: "Components/InboundData",
  component: InboundData,
} as Meta

const mockActor = {
  organisation: {
    id: "e81b19ab-de6d-4893-8df1-60b78f96467d",
    name: "<PERSON><PERSON>",
    config: {
      __typename: "OrganisationConfig",
      enableNationalRegistry: false,
    },
    __typename: "Organisation",
  },
}
export const ExternalSenderExample = () => {
  return (
    <GlobalProvider
      currentTeamId={"fakeId"}
      globalData={{ actor: mockActor } as GlobalDataWithNonNullableActor}
    >
      <div style={{ marginLeft: "auto", marginRight: "auto" }}>
        <InboundData {...mockInboundExternalData} />
      </div>
    </GlobalProvider>
  )
}

export const LeviosaSenderExample = () => {
  return (
    <GlobalProvider
      currentTeamId={"fakeId"}
      globalData={{ actor: mockActor } as GlobalDataWithNonNullableActor}
    >
      <div style={{ marginLeft: "auto", marginRight: "auto" }}>
        <InboundData {...mockInboundLeviosaData} />
      </div>
    </GlobalProvider>
  )
}
