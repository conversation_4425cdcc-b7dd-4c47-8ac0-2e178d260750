fragment InboundDataFragment on InboundData {
  collapsed
  createdAt
  id
  date
  sender {
    id
    providerName
    providerPersonaId
    organisationName
    departmentName
  }
  receiver {
    ...InboundReceiverInfoFragment
  }
  inboundType
  comment
  entries {
    createdAt
    createdBy {
      ...ProviderInfoFragment
    }
    id
    sections {
      content
      id
    }
    title
  }
  communicationStatus: status
  listItem {
    ...InboundListItem
  }
  interventionPeriod {
    id
  }
}
