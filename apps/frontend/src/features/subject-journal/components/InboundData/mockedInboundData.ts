import {
  CommunicationStatus,
  InboundDataFragmentFragment,
  InboundDataType,
  InboundEntry,
  InboundExternalSender,
  InboundReceiver,
  ListItemPriority,
  ListItemType,
  Note,
  Provider,
  ProviderSpecialty,
  InboundListItemFragment,
  GenderId,
} from "generated/graphql"

export const mockListItem: InboundListItemFragment = {
  __typename: "AppointmentRequestItem",
  id: "123",
  team: null,
  comment: "Some comment",
  assignee: {
    __typename: "Provider",
    id: "123",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    specialty: ProviderSpecialty.Registrar,
  },
  priority: ListItemPriority.Critical,
  itemType: ListItemType.AppointmentRequest,
  serviceType: {
    __typename: "ExternalServiceType",
    id: "321",
    name: "<PERSON>i<PERSON><PERSON> læk<PERSON>",
  },
  subject: {
    __typename: "Subject",
    id: "123",
    name: "<PERSON>",
    personaId: "**********",
    phoneNumber: "8998605",
    gender: GenderId.Male,
    age: "50",
  },
  handledAt: null,
  createdAt: "2025-01-27T10:48:18.353366+00:00",
}

const mockInboundExternalSender: InboundExternalSender = {
  id: "sender-id",
  departmentName: "Svæfing",
  providerName: "Jón Jónsson",
  providerPersonaId: "111",
  organisationName: "Læknasetrið",
  __typename: "InboundExternalSender",
}

const mockInboundReceiver: InboundReceiver = {
  id: "someid",
  providerName: "Nonni",
  providerExternalNumber: "",
  departmentName: "Blabla",
  provider: null,
  team: null,
  __typename: "InboundReceiver",
}

export const mockInboundLeviosaReceiver: InboundReceiver = {
  provider: {
    id: "albus",
    name: "Albus Dumbledore",
    phoneNumber: "**********",
    __typename: "Provider",
    nameInitials: "PT",
    specialty: ProviderSpecialty.AttendingPhysician,
  } as Provider,
  id: "otherid",
  providerExternalNumber: null,
  providerName: null,
  team: null,
  departmentName: "",
  __typename: "InboundReceiver",
}

const mockProviderInfo = {
  __typename: "Provider",
  id: "provider1",
  name: "Provider One",
  nameInitials: "PO",
  phoneNumber: "**********",
  specialty: ProviderSpecialty.AttendingPhysician,
} as Provider

const NoteData = {
  id: "123",
  content:
    "Affected area is the  right shoulder  joint. The subject reported to have a pain score of   8 out of 10   and   dizziness  . There was   an inability to move   the affected arm, and   tingling with pain   was experienced. The subject has a history of    #    previous shoulder dislocations.  provided some relief, while    worsened the symptoms. Further evaluation and imaging are recommended to assess the extent of the injury, and appropriate treatment measures should be taken to manage the pain and prevent further complications.",
} as Note

export const mockInboundDataTextEntry = {
  createdAt: "2025-01-15T00:00:00Z",
  createdBy: mockProviderInfo,
  id: "mockInboundDataTextEntry",
  sections: [NoteData],
  title: "Mock Entry",
  updatedAt: "2025-01-15T00:00:00Z",
  updatedBy: mockProviderInfo,
} as InboundEntry

export const mockInboundDataRejectedEntry = {
  createdAt: "2025-01-15T00:00:00Z",
  createdBy: mockProviderInfo,
  id: "mockInboundDataRejectedEntry",
  sections: [NoteData],
  title: "Referral Declined",
  updatedAt: "2025-01-15T00:00:00Z",
  updatedBy: mockProviderInfo,
} as InboundEntry

export const mockInboundExternalData: InboundDataFragmentFragment = {
  __typename: "InboundData",
  collapsed: false,
  communicationStatus: CommunicationStatus.Sent,
  createdAt: "2025-01-16T08:00:00Z",
  date: "2025-01-16",
  entries: [mockInboundDataTextEntry],
  id: "mockInboundExternalData",
  inboundType: InboundDataType.DoctorLetter,
  listItem: mockListItem,
  receiver: mockInboundReceiver,
  sender: mockInboundExternalSender,
  interventionPeriod: {
    __typename: "InterventionPeriod",
    id: "intervention-period-id",
  },
  comment: "hæhæ",
}

export const mockLeviosaReferralTextEntry = {
  createdAt: "2025-01-15T00:00:00Z",
  createdBy: mockProviderInfo,
  id: "mockLeviosaReferralTextEntry",
  sections: [NoteData],
  title: "Mock Entry",
  updatedAt: "2025-01-15T00:00:00Z",
  updatedBy: mockProviderInfo,
} as InboundEntry

export const mockInboundLeviosaData: InboundDataFragmentFragment = {
  __typename: "InboundData",
  collapsed: false,
  communicationStatus: CommunicationStatus.Read,
  createdAt: "2024-12-24T00:00:00Z",
  date: "2025-01-16",
  entries: [mockLeviosaReferralTextEntry],
  id: "mockInboundLeviosaData",
  inboundType: InboundDataType.Referral,
  listItem: mockListItem,
  receiver: mockInboundLeviosaReceiver,
  sender: mockInboundExternalSender,
  interventionPeriod: {
    __typename: "InterventionPeriod",
    id: "intervention-period-id",
  },
  comment: "hoho",
}
