import c from "classnames"
import { useTranslation } from "react-i18next"

import { Disclosure } from "components/Ariakit/Disclosure/Disclosure"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { Heading, Label } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  CommunicationStatus,
  InboundDataType,
  InboundExternalSender,
  InboundReceiverInfoFragmentFragment,
} from "generated/graphql"

import { CommunicationStatusTag } from "../CommunicationStatusTag/CommunicationStatusTag"
import styles from "./InboundData.module.css"

type SenderInfoProps = {
  sender: InboundExternalSender
}
const SenderInfo = ({
  sender: { departmentName, organisationName, providerName },
}: SenderInfoProps) => {
  const { t } = useTranslation()

  const primaryText = providerName || departmentName

  return (
    <>
      <Label secondary>{t("from")}:</Label>
      <div>
        <Label
          className={styles.nameAndSpecialty}
          // only show the department name in the title if it's not already visible
          {...(providerName && departmentName && { title: departmentName })}
        >
          {primaryText && (
            <Label className={styles.name} as="span" secondary>
              {primaryText} •
            </Label>
          )}

          <Label className={styles.specialty} as="span" secondary>
            {organisationName}
          </Label>
        </Label>
      </div>
    </>
  )
}

type ReceiverInfoProps = {
  receiver: InboundReceiverInfoFragmentFragment | null
}
const ReceiverInfo = ({ receiver }: ReceiverInfoProps) => {
  const { t } = useTranslation()
  const { globalData } = useGlobalState()

  const departmentName =
    receiver?.__typename === "InboundReceiver" && receiver?.departmentName
      ? ` • ${receiver.departmentName}`
      : ""

  const organisationName = globalData.actor.organisation.name

  // We want to show both provider.name and providerName as this is not necessarily the same information
  // and this is based on what the sender writes
  const providerNames = [receiver?.provider?.name, receiver?.providerName]
  const providerName = Array.from(new Set(providerNames.filter(Boolean))).join(
    " • "
  )

  return (
    <>
      <Label secondary>{t("to")}:</Label>
      <Label secondary className={styles.receiver}>
        {providerName || organisationName}
        {departmentName}
      </Label>
    </>
  )
}

type DateLabelProps = {
  date: string
}
const DateLabel = ({ date }: DateLabelProps) => {
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()

  const formattedDate = dateFormat(new Date(date))
  const formattedDateTime = timeFormat(new Date(date))

  const dateString = `${formattedDate}, ${formattedDateTime}`

  return (
    <Label className={styles.creationDate} secondary>
      {dateString}
    </Label>
  )
}

type InboundDataHeaderProps = {
  createdAt: string
  inboundType: InboundDataType
  receiver: InboundReceiverInfoFragmentFragment | null
  sender: InboundExternalSender
  status: CommunicationStatus
  className?: string
  collapsable?: boolean
}

export const InboundDataHeader = ({
  createdAt,
  inboundType,
  receiver,
  sender,
  status,
  className = "",
  collapsable = false,
}: InboundDataHeaderProps) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "InboundDataType",
  })

  const content = (
    <div className={styles.typeAndStatusWrap}>
      <Heading as="h3" size={collapsable ? "large" : "default"}>
        {tEnum(inboundType)}
      </Heading>
      <CommunicationStatusTag
        className={styles.communicationStatus}
        status={status}
      />
    </div>
  )

  return (
    <div
      className={c(styles.headerWrap, className, {
        [styles.collapsableHeader]: collapsable,
      })}
    >
      <header className={styles.header}>
        {collapsable ? (
          <Disclosure className={styles.typeStatusAndIconWrap}>
            {content}
          </Disclosure>
        ) : (
          <> {content}</>
        )}

        <div className={styles.senderReceiverDateContainer}>
          <SenderInfo sender={sender} />

          <DateLabel date={createdAt} />

          <ReceiverInfo receiver={receiver} />
        </div>
      </header>
    </div>
  )
}
