.wrap {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.collapsableHeader {
  composes: wrapper from "features/subject-journal/components/Encounter/EncounterHeader.module.css";
}

.collapsableHeader > header {
  border-top: 2px solid var(--color-brand-primary-blue);
  margin-top: -20px;
  padding-top: 4px;
}

.collapsableHeader.collapsableHeader {
  margin: 0;
}

.collapsableHeader.collapsableHeader::before {
  bottom: -8px;
}

.collapsableHeader.collapsableHeader::after {
  content: none;
}

.header {
  display: flex;
  flex-direction: column;
  position: relative;
  gap: 12px;
}

.typeStatusAndIconWrap {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.typeAndStatusWrap {
  display: flex;
  align-items: baseline;
  gap: 8px;
  padding-left: 4px;
}

.communicationStatus {
  position: relative;
  bottom: 4px;
}

.senderReceiverDateContainer {
  display: grid;
  grid-template-columns: min-content 3fr 1fr;
  column-gap: 8px;
  row-gap: 4px;
  align-items: baseline;
  padding-left: 4px;
  padding-right: 8px;
}

.creationDate {
  justify-self: end;
}

.subHeading {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.message {
  padding: 0 24px;
  white-space: pre-wrap;
}

.content {
  display: flex;
  gap: 24px;
  flex-direction: column;
  margin: 0 -24px;
}

.entry {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 0 24px;
}

.entryTitle {
  display: flex;
}

.entryTitleIcon {
  height: 24px;
  width: 24px;
}

.tags {
  display: flex;
  gap: 4px;
}

.entryTitle {
  display: grid;
  gap: 8px;
  grid-template-columns: auto 1fr auto;
  margin-right: 40px;
  align-items: center;
}

.rejection {
  white-space: pre-wrap;
}

.actionsWrap {
  display: flex;
  justify-content: end;
}

.nameAndSpecialty {
  display: inline-flex;
  gap: 4px;
}

.name {
  max-width: 200px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: var(--color-text);
}

.specialty {
  color: var(--color-text);
}

.receiver {
  color: var(--color-text);
}

.aside {
  composes: aside from "features/subject-journal/components/JournalEntry/JournalEntry.module.css";
}

.upcomingEvent {
  padding: 0 8px;
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
  margin-right: 24px;
}

.commentFromSender {
  color: var(--color-text);
  padding: 0 24px;
}
