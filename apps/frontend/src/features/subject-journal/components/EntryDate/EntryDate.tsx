/**
 * EntryDate component displays a formatted date and time based on the provided date string.
 * If the date is more than 24 hours ago, it shows the full date and time.
 * Otherwise, it shows the relative time difference.
 *
 * @param {Object} props - The component props.
 * @param {string} props.date - The date string to be formatted and displayed.
 *
 * @returns {JSX.Element} The rendered EntryDate component.
 */
import Icon from "components/Icon/Icon"
import { Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeDifferenceFormatter from "utils/useTimeDifferenceFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import styles from "./EntryDate.module.css"

export const EntryDate = ({ date }: { date: string }) => {
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const relativeTimeFormat = useTimeDifferenceFormatter()

  const { createdMoreThan24HoursAgo, diffInRelativeTime } =
    relativeTimeFormat(date)

  const formattedDate = dateFormat(new Date(date))
  const formattedDateTime = timeFormat(new Date(date))

  const dateString = `${formattedDate}, ${formattedDateTime}`

  return (
    <Text
      className={styles.wrap}
      size="small"
      {...(createdMoreThan24HoursAgo && { title: dateString })}
    >
      <Icon name="calendar-2-line" />
      {createdMoreThan24HoursAgo ? dateString : diffInRelativeTime}
    </Text>
  )
}
