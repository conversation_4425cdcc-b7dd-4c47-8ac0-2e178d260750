mutation CreateOutboundDoctorsLetter(
  $id: UUID!
  $input: OutboundDoctorsLetterCreateInput!
) {
  createOutboundDoctorsLetter(id: $id, input: $input) {
    ...OutboundDoctorsLetterBlockFragment
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
      }
    }
  }
  setSubjectJournalFocusedItem(
    input: { encounterId: null, journalEntryBlockId: $id }
  ) {
    subjectJournal {
      id
      focusedItemId
    }
  }
}
