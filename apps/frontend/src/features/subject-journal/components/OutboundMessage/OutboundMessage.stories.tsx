import "@ariakit/react"
import { Meta } from "@storybook/react-vite"

import { JournalEntryBlockStatus } from "generated/graphql"

import { OutboundMessage } from "./OutboundMessage"

export default {
  title: "Components/OutboundMessage",
  component: OutboundMessage,
} as Meta

export const ViewDraft = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        provider={""}
        receiverOrganisation={""}
        status={JournalEntryBlockStatus.Draft}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewDraftNotEditable = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={false}
        provider={""}
        receiverOrganisation={""}
        status={JournalEntryBlockStatus.Draft}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewSent = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="SENT"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewSentNotEditable = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={false}
        communicationStatus="SENT"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewDeclined = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="DECLINED"
        provider={"Jón Jónsson"}
        receiverOrganisation={"Læknasetrið"}
        status={""}
        text={"Tilvísun fyrir Siggu að fara í rannsókn"}
        type="REFERRAL"
        communicationStatusReason="Referral declined because of reasons"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewRead = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="READ"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewReceived = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="RECEIVED"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewCancelled = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="CANCELLED"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}

export const ViewFailed = () => {
  return (
    <div style={{ width: "635px" }}>
      <OutboundMessage
        id={"id"}
        canEdit={true}
        communicationStatus="FAILED"
        provider={""}
        receiverOrganisation={""}
        status={""}
        text={""}
        type="REFERRAL"
        onDelete={function (): void {
          throw new Error("Function not implemented.")
        }}
      />
    </div>
  )
}
