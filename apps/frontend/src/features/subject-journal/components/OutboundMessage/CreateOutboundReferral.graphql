mutation CreateOutboundReferral(
  $id: UUID!
  $input: OutboundReferralCreateInput!
) {
  createOutboundReferral(id: $id, input: $input) {
    ...OutboundReferralBlockFragment
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
      }
    }
  }
  setSubjectJournalFocusedItem(
    input: { encounterId: null, journalEntryBlockId: $id }
  ) {
    subjectJournal {
      id
      focusedItemId
    }
  }
}
