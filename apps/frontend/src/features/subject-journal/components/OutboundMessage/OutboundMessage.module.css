.outboundMessageView {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.formGrid {
  margin: 0 24px;
}

.heading {
  display: flex;
  gap: 12px;
}

.description {
  margin-left: 24px;
}

.cancelButton {
  align-self: flex-end;
}

.addEntryContentButton {
  justify-self: start;
  margin-top: -8px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 24px;
}

.orange {
  color: var(--color-orange-800);
}

.levGreen {
  color: var(--color-lev-green-dark);
}

.error {
  color: var(--color-critical-500);
}

.levBlue {
  color: var(--color-lev-blue);
}
