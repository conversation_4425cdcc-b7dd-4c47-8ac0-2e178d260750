import { useId } from "react"
import { useMemo, useState, useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"

import { Icon } from "@leviosa/components"

import { useComboboxStore, useSelectStore } from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { useJournalEntryContext } from "features/subject-journal/components/JournalEntry/JournalEntry"
import { Button, FormGrid, Input, Textarea } from "ui"

import {
  ExternalMessage,
  OutboundReceiver,
  useGetExternalOrganisationsQuery,
  useSendOutboundDoctorsLetterMutation,
  useSendOutboundReferralMutation,
  useUpdateOutboundDoctorsLetterMutation,
  useUpdateOutboundReferralMutation,
} from "generated/graphql"

import styles from "./OutboundMessage.module.css"

type OutboundMessageFormProps = {
  id: string
  messageType: ExternalMessage
  receiver?: OutboundReceiver
  content?: string | null
  onDelete?: () => void
}

export const OutboundMessageForm = ({
  id,
  messageType,
  receiver,
  content: initialContent,
  onDelete,
}: OutboundMessageFormProps) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.outboundMessages",
  })

  const [updateOutboundReferral] = useUpdateOutboundReferralMutation()
  const [updateOutboundDoctorsLetter] = useUpdateOutboundDoctorsLetterMutation()

  const [sendOutboundReferral] = useSendOutboundReferralMutation()
  const [sendOutboundDoctorsLetter] = useSendOutboundDoctorsLetterMutation()

  const formId = useId()

  const comboboxStore = useComboboxStore()

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: receiver?.externalOrganisation?.id || "",
  })

  const { value: comboboxValue } = comboboxStore.useState()

  const {
    data: externalOrganisationsData,
    loading: loadingExternalOrganisations,
    previousData: previousExternalOrganisationsData,
  } = useGetExternalOrganisationsQuery({
    variables: {
      filter: {
        supportsMessage: messageType,
        search: comboboxValue,
      },
    },
  })

  const initialReceiverOption = receiver?.externalOrganisation
    ? [
        {
          label: receiver.externalOrganisation.name,
          value: receiver.externalOrganisation.id,
          subContent: receiver.externalOrganisation.department,
        },
      ]
    : []

  const receiverOptions = useMemo(() => {
    // use previous data while loading so that we don't show an empty state meanwhile
    const options = loadingExternalOrganisations
      ? previousExternalOrganisationsData?.externalOrganisations.map((org) => ({
          label: org.name,
          value: org.id,
          subContent: org.department,
        })) || []
      : externalOrganisationsData?.externalOrganisations.map((org) => ({
          label: org.name,
          value: org.id,
          subContent: org.department,
        })) || []

    return options.length > 0 ? options : initialReceiverOption
  }, [
    loadingExternalOrganisations,
    previousExternalOrganisationsData,
    externalOrganisationsData,
    initialReceiverOption,
  ])

  const [content, setContent] = useState(initialContent || "")
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setContent(initialContent || "")
  }, [initialContent])

  const handleUpdate = (
    receiver?: {
      externalOrganisationId?: string
      externalProviderName?: string
    },
    updatedContent?: string
  ) => {
    const variables = {
      input: {
        receiver: {
          externalOrganisationId: receiver?.externalOrganisationId,
          externalProviderName: receiver?.externalProviderName
            ? {
                set: receiver.externalProviderName,
              }
            : undefined,
        },
        content: updatedContent,
      },
    }

    if (messageType === ExternalMessage.Referral) {
      updateOutboundReferral({
        variables: {
          updateOutboundReferralId: id,
          ...variables,
        },
      })
    }
    if (messageType === ExternalMessage.DoctorLetter) {
      updateOutboundDoctorsLetter({
        variables: {
          updateOutboundDoctorsLetterId: id,
          ...variables,
        },
      })
    }
  }

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault()

    if (messageType === ExternalMessage.Referral) {
      sendOutboundReferral({
        variables: {
          sendOutboundReferralId: id,
        },
      })
    }

    if (messageType === ExternalMessage.DoctorLetter) {
      sendOutboundDoctorsLetter({
        variables: {
          sendOutboundDoctorsLetterId: id,
        },
      })
    }
  }

  // Try to access the journal entry context safely
  let journalEntryContext: ReturnType<typeof useJournalEntryContext> | null =
    null
  try {
    journalEntryContext = useJournalEntryContext()
  } catch (error) {
    // We're outside of a JournalEntry context - that's okay
  }

  return (
    <>
      <FormGrid colSpan={12} id={formId} onSubmit={handleSubmit}>
        <FiltrableSelect
          options={receiverOptions}
          filteredOptions={receiverOptions}
          sameWidth
          selectStore={selectStore}
          comboboxStore={comboboxStore}
          label={tRoutes("recipientOrganisation")}
          name="externalOrganisationId"
          required
          noOptionsPlaceholder={tRoutes("noOrganisationsFound")}
          isLoading={loadingExternalOrganisations}
          onSelectChange={(value) => {
            if (typeof value === "string") {
              handleUpdate({
                externalOrganisationId: value,
              })
            }
          }}
          placeholder={tRoutes("selectOrganisation")}
          inputPlaceholder={tRoutes("searchOrganisation")}
          inlineGuide={tRoutes("recipientOrganisationGuide")}
        />
        <Input
          name="externalProviderName"
          label={tRoutes("organisationProvider")}
          defaultValue={receiver?.externalProviderName || ""}
          onBlur={(e) =>
            handleUpdate({
              externalProviderName: e.target.value,
            })
          }
          inlineGuide={tRoutes("organisationProviderGuide")}
        />
        <Textarea
          name="content"
          label={tRoutes("documentText")}
          required
          hideMessage={true}
          autoGrow
          value={content}
          ref={textareaRef}
          onChange={(e) => setContent(e.target.value)}
          onBlur={() => handleUpdate({}, content)}
          inlineGuide={tRoutes("documentTextGuide")}
        />
        {journalEntryContext && (
          <Button
            icon={<Icon name="file-copy-line" />}
            className={styles.addEntryContentButton}
            variant="clear"
            onClick={() => {
              // Get markdown content from the journal entry context
              const markdownContent =
                journalEntryContext?.getJournalEntryAsMarkdown()

              if (!markdownContent) return

              // Create the new content with the markdown added
              const newContent = content
                ? `${content}\n\n---------------------\n\n${markdownContent}`
                : markdownContent

              // Update the local state so the textarea reflects the change
              setContent(newContent)

              // Update only backend - rely on Apollo cache updates
              handleUpdate({}, newContent)

              // Focus the textarea after updating the content
              textareaRef.current?.focus()
            }}
          >
            {tRoutes("addEntryContent")}
          </Button>
        )}
      </FormGrid>

      <div className={styles.footer}>
        <Button
          variant="clear"
          className={styles.cancelButton}
          onClick={onDelete}
        >
          {t("doDelete")}
        </Button>
        <Button
          variant="filled"
          className={styles.cancelButton}
          form={formId}
          type="submit"
        >
          {t("send")}
        </Button>
      </div>
    </>
  )
}
