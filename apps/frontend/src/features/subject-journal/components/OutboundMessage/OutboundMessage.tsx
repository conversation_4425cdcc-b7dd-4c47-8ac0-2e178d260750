import { useTranslation } from "react-i18next"

import Supplement from "ui/components/Supplement/Supplement"

import {
  JournalEntryBlockStatus,
  OutboundDoctorsLetterBlockFragmentFragment,
  OutboundReferralBlockFragmentFragment,
  CommunicationStatus,
  ExternalMessage,
} from "generated/graphql"

import {
  CommunicationStatusTag,
  statusMap,
} from "../CommunicationStatusTag/CommunicationStatusTag"
import styles from "./OutboundMessage.module.css"
import { OutboundMessageForm } from "./OutboundMessageForm"
import { OutboundMessageView } from "./OutboundMessageView"

type OutboundMessageProps = (
  | OutboundDoctorsLetterBlockFragmentFragment
  | OutboundReferralBlockFragmentFragment
) & {
  messageType: ExternalMessage
  canEdit: boolean
  onDelete?: () => void
}

type HeadingWithStatusTagProps = {
  title: string
  communicationStatus: CommunicationStatus
}
const HeadingWithStatusTag = ({
  title,
  communicationStatus,
}: HeadingWithStatusTagProps) => {
  return (
    <div className={styles.heading}>
      {title}
      <CommunicationStatusTag status={communicationStatus} />
    </div>
  )
}

export const OutboundMessage = ({
  id,
  canEdit,
  onDelete,
  receiver,
  status: blockStatus,
  blockContent,
  messageType,
}: OutboundMessageProps) => {
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.outboundMessages",
  })

  const outboundMessageType = tEnum(`OutboundDataType.${messageType}`)

  const isDraft = blockStatus === JournalEntryBlockStatus.Draft

  const communicationStatus = receiver?.communicationStatus

  const heading =
    isDraft || !communicationStatus ? (
      outboundMessageType
    ) : (
      <HeadingWithStatusTag
        title={outboundMessageType}
        communicationStatus={communicationStatus}
      />
    )

  const minimizedCommunicationStatus = communicationStatus ? (
    <span
      className={
        styles[statusMap[communicationStatus].color as keyof typeof styles]
      }
    >
      {tEnum(`CommunicationStatus.${communicationStatus}`)}
    </span>
  ) : (
    ""
  )
  const minimizedSubheading = isDraft
    ? tRoutes("draft")
    : minimizedCommunicationStatus

  return (
    <Supplement
      id={id}
      icon="mail-send-line"
      heading={heading}
      minimizedHeading={outboundMessageType}
      confirmed={!isDraft}
      minimizedSubheading={minimizedSubheading}
      isMinimized={!isDraft || !canEdit}
      // {...(isDraft && {
      //   subHeading: tRoutes(`description.${messageType}`),
      // })}
    >
      <div className={styles.outboundMessageView}>
        {isDraft && canEdit ? (
          <OutboundMessageForm
            id={id}
            messageType={messageType}
            receiver={receiver}
            content={blockContent}
            onDelete={onDelete}
          />
        ) : (
          <OutboundMessageView
            receiver={receiver}
            content={blockContent}
            communicationStatus={communicationStatus}
            canEdit={canEdit}
          />
        )}
      </div>
    </Supplement>
  )
}
