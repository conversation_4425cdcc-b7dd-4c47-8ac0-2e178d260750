// import Panel from "components/Panel/Panel"
import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { FormGrid, LabeledValue, TextWithIcon } from "ui"

import { CommunicationStatus, OutboundReceiver } from "generated/graphql"

import styles from "./OutboundMessage.module.css"

type OutboundMessageViewProps = {
  canEdit: boolean
  communicationStatus: CommunicationStatus
  receiver: OutboundReceiver
  content?: string | null
}

export const OutboundMessageView = ({
  canEdit,
  communicationStatus,
  receiver,
  content,
}: OutboundMessageViewProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "subjectJournal.outboundMessages",
  })

  const { externalOrganisation, externalProviderName, communicationResponse } =
    receiver

  const externalOrganisationValue = [
    externalOrganisation?.name,
    externalOrganisation?.department,
  ]
    .filter(Boolean)
    .join(" - ")

  return (
    <>
      <FormGrid as="dl" colSpan={12} rowGap={3} className={styles.formGrid}>
        <LabeledValue
          label={tRoutes("recipientOrganisation")}
          value={externalOrganisationValue}
        />
        <LabeledValue
          label={tRoutes("organisationProvider")}
          value={externalProviderName}
        />
        <LabeledValue
          label={tRoutes("documentText")}
          value={content}
          contentClassName="whitespace-pre-wrap"
        />
      </FormGrid>
      {communicationResponse && (
        <Panel>
          <TextWithIcon size="small">{communicationResponse}</TextWithIcon>
        </Panel>
      )}
      {/* Hide cancel button for now until BE is ready */}
      {/* {canCancel && (
        <Button
          variant="clear"
          className={styles.cancelButton}
          onClick={cancelRequest}
        >
          {tRoutes("cancelRequest")}
        </Button>
      )} */}
    </>
  )
}
