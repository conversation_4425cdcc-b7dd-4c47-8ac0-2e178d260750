.formGrid {
  position: relative;
  color: var(--color-text);
}

.disabled {
  pointer-events: none;
}

.drugCombobox {
  margin: 0;
}

.drugComboboxItem {
  display: grid;
  grid-template-columns: 12% 1fr auto;
}

.package {
  color: var(--color-text-secondary);
}

.drugLabel {
  display: grid;
  grid-template-columns: 12% 2fr 1fr auto;
  align-items: center;
}

.panel {
  text-align: justify;
}

/* If Panel has img then guarantee is rendered on right side with fixed size. */
.panel > p:has(img) {
  display: grid;
  grid-template-columns: 3fr auto;
  gap: 8px;
}

.panel > p > img {
  height: 80px;
}

/* Intention first input of the proper DrugPrescription form so justifies space  */

.itemExempted {
  /* TODO we need a warning (not critical) text color from palette */
  color: var(--color-orange-600);
}

.informativeNotes > p {
  padding: 8px 0;
}
.informativeNotes > p:not(:last-child) {
  border-bottom: 1px solid var(--color-gray-30);
}

.checkboxWrap {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.span1 {
  grid-column: span 1;
}
.span2 {
  grid-column: span 2;
}
.span3 {
  grid-column: span 3;
}
.span4 {
  grid-column: span 4;
}
.saveStatus {
  margin-right: auto;
}

.errorMessage {
  white-space: pre-line;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  margin: -16px;
  margin-top: -60px;
  backdrop-filter: blur(2px);
  z-index: var(--z-index-sticky-header);

  display: flex;
  justify-content: center;
  align-items: center;
}

.reasoning {
  resize: block;
}
