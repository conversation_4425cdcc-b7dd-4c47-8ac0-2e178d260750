import c from "classnames"
import { addYears } from "date-fns"
import { FormEventHandler } from "react"
import { useTranslation } from "react-i18next"

import { InlineGuideId, inlineGuides } from "assets/inlineGuides"
import DatePicker from "components/DatePicker/DatePicker"
import { InlineGuidePopover } from "components/InlineGuide/InlineGuidePopover"
import Panel from "components/Panel/Panel"
import useDrugPrescriptionState, {
  SubmitDrugPrescriptionInputSchema,
} from "features/subject-journal/components/DrugPrescription/useDrugPrescriptionState"
import SaveStatus from "features/subject-journal/components/SaveStatus/SaveStatus"
import {
  Button,
  Input,
  Text,
  FormFooter,
  FormGrid,
  Checkbox,
  Loading,
  Textarea,
} from "ui/index"
import { SupportedLanguages } from "utils/SupportedLanguages"

import {
  DrugAdministrationFlag,
  DrugPrescriptionBlockFragmentFragment,
  DrugPrescriptionTemplateFragmentFragment,
  useSendDrugPrescriptionMutation,
} from "generated/graphql"

import styles from "./DrugPrescriptionForm.module.css"
import DrugSelect from "./DrugSelect"

const addictiveDrugFlags = [
  DrugAdministrationFlag.AddictiveMonitored,
  DrugAdministrationFlag.AddictiveNotMonitored,
  DrugAdministrationFlag.AddictiveQuantityControlled,
]

type DrugPrescriptionFormProps = {
  drugPrescription:
    | DrugPrescriptionBlockFragmentFragment
    | DrugPrescriptionTemplateFragmentFragment
  journalTemplateId?: string
  languageId: SupportedLanguages
  onDelete?: () => void
}

/* 
  Strategy: BE has state, FE updates field by field, motivated in Product Documentation here https://leviosa.atlassian.net/wiki/spaces/DOC/pages/77299803/JournalEntry+JE.
  To prevent lagging on UI we update FE state immediately but only update BE state when user leaves control (onBlur).
 */

export default function DrugPrescriptionForm({
  drugPrescription,
  journalTemplateId,
  languageId,
  onDelete,
}: // drugIntention,
DrugPrescriptionFormProps) {
  const details = drugPrescription.drugCode?.details || null
  const isJournalTemplate = !!journalTemplateId

  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "drugPrescription",
  })

  const [{ formState, register, handleChange, handleBlur, saveStatus }] =
    useDrugPrescriptionState(
      {
        ...drugPrescription,
        drugCodeId: drugPrescription.drugCode?.id || undefined,
        validFrom:
          drugPrescription.__typename === "DrugPrescription"
            ? drugPrescription.validFrom || new Date().toISOString()
            : null,
        validTo:
          drugPrescription.__typename === "DrugPrescription"
            ? drugPrescription.validTo || addYears(new Date(), 1).toISOString()
            : null,
      },
      journalTemplateId
    )

  const [sendDrugPrescription, { error, loading }] =
    useSendDrugPrescriptionMutation()

  // const diCalculations = drugIntention.getDpCalculations()

  const isAddictiveDrug =
    details && details.flags.some((flag) => addictiveDrugFlags.includes(flag))

  const drugIsDisabled = false
  const isDrugSelected = drugPrescription.drugCode !== null

  const handleSubmit: FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault()
    // Update drug prescription with current form state
    // Send drug prescription

    const result = SubmitDrugPrescriptionInputSchema.safeParse(formState)
    if (!result.success) {
      console.error(result.error)
      return
    }

    sendDrugPrescription({
      variables: {
        id: drugPrescription.id,
        input: result.data,
      },
    })
  }

  const onExaminationList = drugPrescription.drugCode?.details?.onExemptionList

  return (
    <FormGrid
      colSpan={6}
      onSubmit={handleSubmit}
      className={c(styles.formGrid, {
        [styles.disabled]: loading,
      })}
    >
      {loading && (
        <div className={styles.loadingOverlay}>
          <Loading showText={false} large />
        </div>
      )}
      <DrugSelect
        prescriptionId={drugPrescription.id}
        selectedDrug={drugPrescription.drugCode}
        languageId={languageId}
        handleChange={handleChange}
        handleBlur={handleBlur}
      />

      {onExaminationList && (
        <Textarea
          label={tRoutes("reasoning")}
          {...register("reasoning")}
          rows={1}
          autoGrow
          textareaProps={{
            className: styles.reasoning,
          }}
        />
      )}

      {isDrugSelected && (
        <>
          {/* If disabling warnings then Intention control and rest of form is not shown. Panels above should display appropriate warning. */}
          {!drugIsDisabled && (
            // <Input
            //   label={tRoutes("drugIntention")}
            //   type="text"
            //   autoFocus={!doSelectDrug}
            //   className={styles.dosingIntention}
            //   placeholder={tRoutes("drugIntention_placeholder")}
            //   value={formState.shorthand}
            //   onChange={({ currentTarget }) =>
            //     handleChange({ shorthand: currentTarget.value })
            //   }
            //   onBlur={({ currentTarget }) =>
            //     handleChange({ shorthand: currentTarget.value }, true)
            //   }
            //   inlineGuide={<InlineGuidePopover content="DRUG_INTENTION_SHORTHAND" />}
            //   /* COMEBACK before 'return' calculate value from unit/daily max. show message only if current value exceeds. */
            //   /* COMEBACK REF v2: set explicitly on db using our units */
            //   // message={
            //   //   details.flags.includes(DrugAdministrationFlag.AddictiveMonitored)
            //   //     ? `${tRoutes(
            //   //         "warnings.addictiveDrug.monitored_quantityRestrictions"
            //   //       )}`
            //   //     : `${tRoutes("warnings.addictiveDrug.quantityRestricted")}"${
            //   //         details.addictivePrescriptionQuantityLimit
            //   //       } ${details.addictivePrescriptionQuantityUnit}"`
            //   // }
            //   clearable
            //   autoComplete="off"
            // />

            <Input
              label={tRoutes("drugIntention")}
              type="text"
              {...register("dosingInstructions")}
              required
              clearable
              autoComplete="off"
            />
          )}
          {/* {drugIntention.isShorthand() && (
        <ShorthandPanel drugIntention={drugIntention} />
      )} */}
          <Input
            className={styles.span2}
            type="number"
            min={1}
            // disabled={!!diCalculations?.daysDuration}
            label={tRoutes("packageCount")}
            {...register("packageCount")}
            // title={`${details.packageSize} : ${
            //   diCalculations?.daysDuration &&
            //   (diCalculations.daysDuration * diCalculations.dailyMaxDoses) /
            //     details.packageSize
            // }`}
            // title={`${details.packageSize}`}
            required
            // message={
            //   details && `${details.packageSize} ${details.packageSizeUnit}`
            // }
            autoComplete="off"
          />
          <Input
            label={tRoutes("maxDailyUnits")}
            type="number"
            className={styles.span2}
            min={isAddictiveDrug ? 1 : undefined}
            {...register("maxDailyUnits")}
            required={isAddictiveDrug || false}
            autoComplete="off"
          />
          <Input
            label={tRoutes("daysBetweenDispension")}
            type="number"
            className={styles.span2}
            min={isAddictiveDrug ? 1 : undefined}
            required={isAddictiveDrug || false}
            {...register("daysBetweenDispension")}
            autoComplete="off"
          />
          <Input
            label={tRoutes("commentToDispenser")}
            {...register("commentToDispenser")}
            inlineGuide={inlineGuides["DRUGORDER_COMMENTTODISPENSER"].content}
            autoComplete="off"
          />
          <Input
            label={tRoutes("clinicalIndication")}
            {...register("clinicalIndication")}
            required
            autoComplete="off"
          />
          <span className={styles.checkboxWrap}>
            <Checkbox
              label={tRoutes("rMarked")}
              checked={!formState.allowGenericSubstitution}
              {...register("allowGenericSubstitution")}
              onChange={() => {
                handleChange(
                  "allowGenericSubstitution",
                  !formState.allowGenericSubstitution
                )
                handleBlur(
                  "allowGenericSubstitution",
                  !formState.allowGenericSubstitution
                )
              }}
            />
            <InlineGuidePopover id={InlineGuideId.DRUG_R_MARKED} />
          </span>
          {!isJournalTemplate && (
            <>
              <DatePicker
                label={tRoutes("validFrom")}
                className={styles.span3}
                {...register("validFrom")}
                min={new Date().toISOString().split("T")[0]}
                max={formState.validTo || undefined}
                portal
              />
              <DatePicker
                label={tRoutes("validTo")}
                className={styles.span3}
                {...register("validTo")}
                min={formState.validFrom || undefined}
                max={addYears(new Date(), 1).toISOString().split("T")[0]}
                portal
              />
            </>
          )}

          {error && (
            <Panel status="error">
              <Text className={styles.errorMessage}>{error.message}</Text>
            </Panel>
          )}
        </>
      )}
      <FormFooter>
        {!loading && isDrugSelected && (
          <SaveStatus
            status={saveStatus}
            className={styles.saveStatus}
            updatedAt={drugPrescription.updatedAt}
          />
        )}
        {onDelete && (
          <Button variant="clear" onClick={onDelete}>
            {tRoutes("delete")}
          </Button>
        )}
        {isDrugSelected && !isJournalTemplate && (
          <Button variant="filled" type="submit">
            {tRoutes("send")}
          </Button>
        )}
      </FormFooter>
    </FormGrid>
  )
}
