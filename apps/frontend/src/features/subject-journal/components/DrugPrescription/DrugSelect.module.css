.wrap {
  margin-bottom: 16px;
}

.select {
  width: 100%;
  padding: 8px;
  position: relative;
  z-index: 1;
  margin-top: 8px;
}

.selectPopover {
  z-index: var(--z-index-select-popover);
}

.combobox {
  width: 100%;
  margin: 0px;
  border: none;
  border-radius: 0;
  border-bottom: 1px solid var(--color-text);
}

.comboboxItem {
  width: 100%;
  display: flex;
  padding: 0 8px;
  gap: 16px;
  flex-wrap: nowrap;
}

.comboboxItem:hover {
  background-color: transparent;
}

.drugItem {
  display: grid;
  grid-template-columns: 18% 1fr auto;
  width: 100%;
}

.itemExempted {
  color: var(--color-orange-600);
}

.comboboxList > div {
  padding: 8px 16px;
}

.arrowIcon {
  flex-shrink: 0;
  margin: auto;
}

.emptyState {
  display: flex;
  width: 100%;
  justify-content: flex-end;
  padding-right: 8px;
}

.moreInfoLink {
  display: flex;
  gap: 4px;
  align-items: center;
  width: fit-content;

  padding-left: 16px;
  padding-top: 8px;
}
