import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import usePermissions from "features/authentication/hooks/usePermissions"
import { Button } from "ui"
import Supplement from "ui/components/Supplement/Supplement"
import { SupportedLanguages } from "utils/SupportedLanguages"

import {
  DrugPrescriptionBlockFragmentFragment,
  DrugPrescriptionTemplateFragmentFragment,
  JournalEntryBlockStatus,
  PermissionKey,
  useRetryDrugPrescriptionMutation,
} from "generated/graphql"

import styles from "./DrugPrescriptionBlock.module.css"
import DrugPrescriptionForm from "./DrugPrescriptionForm"
import DrugPrescriptionView from "./DrugPrescriptionView"

type DrugPrescriptionBlockProps = {
  drugPrescription:
    | DrugPrescriptionBlockFragmentFragment
    | DrugPrescriptionTemplateFragmentFragment
  languageId: SupportedLanguages
  canEdit: boolean
  showDoctorNumberWarning?: boolean
  journalTemplateId?: string
  onDelete?: () => void
}

const getDrugPrescriptionBlock = (
  drugPrescription:
    | DrugPrescriptionBlockFragmentFragment
    | DrugPrescriptionTemplateFragmentFragment
): DrugPrescriptionBlockFragmentFragment | null => {
  return drugPrescription.__typename === "DrugPrescription"
    ? drugPrescription
    : null
}

export default function DrugPrescriptionBlock({
  drugPrescription,
  languageId,
  canEdit,
  showDoctorNumberWarning,
  journalTemplateId,
  onDelete,
}: DrugPrescriptionBlockProps) {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "drugPrescription",
  })

  const { hasPermission } = usePermissions()

  const [retryDrugPrescription] = useRetryDrugPrescriptionMutation()

  const drugPrescriptionBlock = getDrugPrescriptionBlock(drugPrescription)

  const drugPrescriptionStatus = drugPrescriptionBlock?.status
  const drugPrescriptionError = drugPrescriptionBlock?.sendError

  const canEditDrugPrescription = drugPrescriptionBlock
    ? hasPermission(PermissionKey.SubjectJournalDrugPrescriptionEdit) &&
      (drugPrescriptionBlock.status === JournalEntryBlockStatus.Draft ||
        drugPrescriptionBlock.status === JournalEntryBlockStatus.Error) &&
      canEdit
    : canEdit

  const getSubLabel = () => {
    switch (drugPrescriptionStatus) {
      case JournalEntryBlockStatus.Draft:
        return tRoutes("draft")
      case JournalEntryBlockStatus.Sending:
        return tRoutes("failedToSend")
      case JournalEntryBlockStatus.Error:
        return tRoutes("errorStatus")
      default:
        return ""
    }
  }
  const selectedDrug = drugPrescription.drugCode?.label
  const subHeading = drugPrescriptionBlock ? getSubLabel() : undefined
  const confirmed =
    drugPrescriptionStatus &&
    drugPrescriptionStatus === JournalEntryBlockStatus.Completed

  return (
    <Supplement
      id={drugPrescription.id}
      heading={tRoutes("title")}
      minimizedHeading={selectedDrug || tRoutes("title")}
      subHeading={subHeading}
      color="pink"
      icon="capsule-line"
      confirmed={confirmed}
      isMinimized={!canEditDrugPrescription || confirmed}
    >
      {canEdit && drugPrescriptionError && (
        <Panel status="warning" className={styles.sendError}>
          {drugPrescriptionError}
          {drugPrescriptionStatus === JournalEntryBlockStatus.Sending &&
            drugPrescriptionBlock && (
              <Button
                className={styles.retryButton}
                onClick={() =>
                  retryDrugPrescription({
                    variables: {
                      id: drugPrescriptionBlock?.id || "",
                    },
                  })
                }
                variant="filled"
                icon={<Icon name="restart-line" />}
              >
                {t("retry")}
              </Button>
            )}
        </Panel>
      )}
      {canEditDrugPrescription ? (
        <>
          {showDoctorNumberWarning && (
            <Panel status="warning" className={styles.doctorNumberWarning}>
              {tRoutes("warnings.doctorNumberMissing")}
            </Panel>
          )}
          <DrugPrescriptionForm
            drugPrescription={drugPrescription}
            languageId={languageId}
            journalTemplateId={journalTemplateId}
            onDelete={onDelete}
          />
        </>
      ) : (
        <DrugPrescriptionView drugPrescription={drugPrescription} />
      )}
    </Supplement>
  )
}
