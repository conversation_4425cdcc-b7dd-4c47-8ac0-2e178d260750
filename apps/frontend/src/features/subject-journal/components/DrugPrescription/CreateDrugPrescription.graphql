mutation CreateDrugPrescription(
  $input: CreateDrugPrescriptionInput!
  $id: UUID!
) {
  createDrugPrescription(input: $input, id: $id) {
    ...DrugPrescriptionBlockFragment
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
      }
    }
  }
  setSubjectJournalFocusedItem(
    input: { encounterId: null, journalEntryBlockId: $id }
  ) {
    subjectJournal {
      id
      focusedItemId
    }
  }
}
