.wrap {
  display: flex;
  flex-direction: column;
  position: relative;

  padding: 16px;
  padding-top: 38px;
  margin-top: -22px;

  border-radius: 0 0 var(--radius-button-half) var(--radius-button-half);
  z-index: var(--z-index-base);
}

.warningPanel {
  display: flex;
  gap: 8px;
}

.icon {
  flex-shrink: 0;
}

.noIconWarning {
  margin-left: 24px;
}

strong {
  font-weight: 600;
}

.showDetailsButton {
  width: fit-content;
  position: absolute;
  right: 16px;
  top: 34px;
  border: none;
}

.showDetailsButton.showDetailsButton:hover,
.showDetailsButton.showDetailsButton:active {
  background-color: white;
}

.heading {
  margin-bottom: 8px;
}

.details {
  display: flex;
  gap: 24px;
  flex-direction: column;
  overflow: hidden;
  transition: height 200ms cubic-bezier(0.76, 0, 0.24, 1);
  height: 0;
}

.details > div:first-child {
  margin-top: 8px;
}

.moreWarnings {
  font-weight: normal;
}
