import c from "classnames"
import { useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { Markdown } from "components/Markdown/Markdown"
import { color } from "styles/colors"
import { Button, Text, TextWithIcon } from "ui"

import {
  DrugAdministrationFlag,
  DrugClinicalCodeFragmentFragment,
} from "generated/graphql"

import styles from "./DrugWarningPanel.module.css"

const addictiveDrugFlags = [
  DrugAdministrationFlag.AddictiveMonitored,
  DrugAdministrationFlag.AddictiveNotMonitored,
  DrugAdministrationFlag.AddictiveQuantityControlled,
]

type DrugWarningPanelProps = {
  prescriptionId: string
  drug: DrugClinicalCodeFragmentFragment
}

const getWarnings = (
  drug: DrugClinicalCodeFragmentFragment
): { heading: string; details: string; icon: IconName }[] => {
  const warnings: { heading: string; details: string; icon: IconName }[] = []
  const { details } = drug
  const isAddictiveDrug =
    details && details.flags.some((flag) => addictiveDrugFlags.includes(flag))

  if (isAddictiveDrug) {
    warnings.push({
      heading: "warnings.addictiveDrug.generic.heading",
      details: "warnings.addictiveDrug.generic.details",
      icon: "error-warning-line",
    })
  }

  if (
    isAddictiveDrug &&
    details?.flags.includes(DrugAdministrationFlag.AddictiveMonitored)
  ) {
    warnings.push({
      heading: "warnings.addictiveDrug.monitored.heading",
      details: "warnings.addictiveDrug.monitored.details",
      icon: "error-warning-line",
    })
  }

  const isExemptedDrug =
    details && details.flags.includes(DrugAdministrationFlag.OnExemptionList)

  if (isExemptedDrug) {
    warnings.push({
      heading: "warnings.isExempted.heading",
      details: "warnings.isExempted.details",
      icon: "alert-line",
    })
  }

  if (
    details &&
    details.flags.includes(
      DrugAdministrationFlag.AdditionalRiskMinimisationMeasures
    )
  ) {
    warnings.push({
      heading: "warnings.hasSecurityNote.heading",
      details: "warnings.hasSecurityNote.details",
      icon: "alert-line",
    })
  }
  if (
    details &&
    details.flags.includes(DrugAdministrationFlag.OverTheCounter)
  ) {
    warnings.push({
      heading: "warnings.isOtc.heading",
      details: "warnings.isOtc.details",
      icon: "alert-line",
    })
  }
  return warnings
}

const WarningInformation = ({
  icon,
  heading,
  details,
}: {
  icon?: IconName
  heading?: string
  details: string
}) => {
  return (
    <div className={styles.warningPanel}>
      {icon && <Icon name={icon} className={styles.icon} />}
      <span>
        {heading && (
          <Text weight="bold" className={styles.heading}>
            {heading}
          </Text>
        )}
        <Markdown
          children={details}
          className={!icon ? styles.noIconWarning : ""}
        />
      </span>
    </div>
  )
}
const DrugWarningPanel = ({ prescriptionId, drug }: DrugWarningPanelProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "drugPrescription",
  })

  const { globalData } = useGlobalState()

  const [showDetails, setShowDetails] = useState(true)
  const detailsRef = useRef<HTMLDivElement>(null)

  // We want to show the warning details by default for the first time
  // but after that we want it to be default hidden
  useEffect(() => {
    const localStorageId = `hideDrugWarningDetails-${drug.id}-${globalData.actor.id}`

    if (localStorage.getItem(localStorageId) === null) {
      localStorage.setItem(localStorageId, "true")
    } else {
      setShowDetails(false)
    }
  }, [drug.id, prescriptionId])

  useEffect(() => {
    if (detailsRef.current) {
      const contentHeight = detailsRef.current.scrollHeight
      const newHeight = showDetails ? `${contentHeight}px` : "0"
      detailsRef.current.style.height = newHeight
    }
  }, [showDetails])

  const warnings: { heading: string; details: string; icon: IconName }[] =
    getWarnings(drug)

  if (warnings.length === 0) {
    return null
  }

  const firstWarning = warnings[0]
  const numberOfWarnings = warnings.length
  const moreWarningsText =
    numberOfWarnings > 1
      ? ` + ${numberOfWarnings - 1} ${tRoutes("warnings.moreWarnings")}`
      : ""

  return (
    <div className={c(styles.wrap, color.warning.light)}>
      <TextWithIcon weight="bold" iconName={firstWarning.icon}>
        {tRoutes(firstWarning.heading)}
        {!showDetails && moreWarningsText && (
          <span className={styles.moreWarnings}>{moreWarningsText}</span>
        )}
      </TextWithIcon>
      <Button
        variant="clear"
        className={styles.showDetailsButton}
        onClick={() => setShowDetails(!showDetails)}
      >
        {showDetails
          ? tRoutes("warnings.hideDetails")
          : tRoutes("warnings.seeDetails")}
      </Button>
      <div ref={detailsRef} className={styles.details}>
        <WarningInformation details={tRoutes(firstWarning.details)} />
        {warnings.slice(1).map((warning, i) => (
          <WarningInformation
            key={i}
            icon={warning.icon}
            heading={tRoutes(warning.heading)}
            details={tRoutes(warning.details)}
          />
        ))}
      </div>
    </div>
  )
}

export default DrugWarningPanel
