import { ApolloError } from "@apollo/client"
import { addYears, isBefore, startOfDay } from "date-fns"
import dayjs from "dayjs"
import { Draft } from "immer"
import { isEqual } from "lodash"
import { useRef } from "react"
import { useTranslation } from "react-i18next"
import { useImmerReducer } from "use-immer"
import { z } from "zod"

import { getSaveStatus } from "features/subject-journal/components/SaveStatus/SaveStatus"
import isDefined from "utils/isDefined"
import isDev from "utils/isDev"

import {
  DrugPrescriptionTemplateFragmentFragment,
  useEditDrugPrescriptionTemplateMutation,
  useUpdateDrugPrescriptionMutation,
} from "generated/graphql"
import { DrugPrescriptionBlockFragmentFragment } from "generated/graphql/index"

type WritableDraft<T> = {
  -readonly [K in keyof T]: Draft<T[K]>
}

const isBeforeOrEqual = (date: number | Date, dateToCompare: number | Date) =>
  isBefore(date, dateToCompare) || isEqual(date, dateToCompare)

// Zod schema for UpdateDrugPrescriptionInput

export const SubmitDrugPrescriptionInputSchema = z.object({
  drugCodeId: z.string().uuid(),
  dosingInstructions: z.string(),
  shorthand: z.string().optional(),
  clinicalIndication: z.string(),
  reasoning: z.string().or(z.null()),
  commentToDispenser: z.string().optional(),
  packageCount: z.coerce.number(),
  maxDailyUnits: z.coerce.number().or(z.null()).optional(),
  validFrom: z
    .string()
    .or(z.date())
    .or(z.null())
    .transform((v) => (v ? new Date(v) : undefined))
    .refine(
      (v) => {
        const today = startOfDay(new Date())
        return v ? isBeforeOrEqual(today, v) : true
      },
      {
        message: "Date must not be before today",
      }
    )
    .optional(),
  validTo: z
    .string()
    .or(z.date())
    .or(z.null())
    .transform((v) => (v ? new Date(v) : undefined))
    .refine((v) => (v ? isBeforeOrEqual(v, addYears(new Date(), 1)) : true), {
      message:
        "Prescriptions can only be valid for a maximum of one year from today",
    })
    .optional(),
  daysBetweenDispension: z.coerce.number().or(z.null()).optional(),
  allowGenericSubstitution: z.boolean().optional(),
})

const PartialUpdateDrugPrescriptionSchema =
  SubmitDrugPrescriptionInputSchema.partial()

// Used when updating a single field
const UpdateDrugPrescriptionSchema: z.ZodType<
  z.output<typeof PartialUpdateDrugPrescriptionSchema>,
  z.ZodTypeDef,
  z.input<typeof PartialUpdateDrugPrescriptionSchema>
> = PartialUpdateDrugPrescriptionSchema.refine(
  ({ validFrom, validTo }) => {
    return validFrom && validTo ? isBefore(validFrom, validTo) : true
  },
  {
    message: `Valid from date must be before valid to date`,
    path: ["validFrom", "validTo"], // path of error
  }
)

const getInitialState = (
  drugPrescription: (
    | DrugPrescriptionBlockFragmentFragment
    | DrugPrescriptionTemplateFragmentFragment
  ) & {
    drugCodeId?: string
    validFrom?: string | null
    validTo?: string | null
  }
) => ({
  formState: {
    drugCodeId: drugPrescription.drugCode?.id || null,
    dosingInstructions: drugPrescription.dosingInstructions || "",
    shorthand: drugPrescription.shorthand || "",
    clinicalIndication: drugPrescription.clinicalIndication || "",
    commentToDispenser: drugPrescription.commentToDispenser || "",
    daysBetweenDispension: drugPrescription.daysBetweenDispension || null,
    packageCount: drugPrescription.packageCount || null,
    maxDailyUnits: drugPrescription.maxDailyUnits || null,
    reasoning: drugPrescription.reasoning || null,
    allowGenericSubstitution: isDefined(
      drugPrescription.allowGenericSubstitution
    )
      ? drugPrescription.allowGenericSubstitution
      : true,
    validFrom: drugPrescription.validFrom
      ? dayjs(drugPrescription.validFrom).format("YYYY-MM-DD")
      : null,
    validTo: drugPrescription.validTo
      ? dayjs(drugPrescription.validTo).format("YYYY-MM-DD")
      : null,
  },
  editedKeys: [] as (keyof Zod.infer<typeof UpdateDrugPrescriptionSchema>)[],
  errors: {} as Record<string, ApolloError | string | null>,
})

type State = ReturnType<typeof getInitialState>

export type FormState = State["formState"]

export default function useDrugPrescriptionState(
  initialState: (
    | DrugPrescriptionBlockFragmentFragment
    | DrugPrescriptionTemplateFragmentFragment
  ) & {
    drugCodeId?: string
    validFrom?: string | null
    validTo?: string | null
  },
  journalTemplateId?: string
) {
  const [updateDrugPrescription, updateMutationResult] =
    useUpdateDrugPrescriptionMutation()
  const [editDrugPrescriptionTemplate, updateTemplateMutationResult] =
    useEditDrugPrescriptionTemplateMutation()
  const { t } = useTranslation("routes", { keyPrefix: "drugPrescription" })
  const timeoutRef = useRef<ReturnType<typeof setTimeout>>()

  type Action =
    | {
        type: "focus"
        payload: keyof FormState
      }
    | {
        type: "update"
        payload: Partial<FormState>
      }
    | {
        type: "blur"
        payload: keyof FormState
      }
    | {
        type: "mutationError"
        payload: {
          key: keyof FormState
          error: ApolloError
        }
      }
    | {
        type: "validationError"
        payload: {
          key: keyof FormState
          error: string
        }
      }
    | {
        type: "clearErrors"
      }

  const [state, dispatch] = useImmerReducer<State, Action>((draft, action) => {
    switch (action.type) {
      case "focus": {
        draft.errors[action.payload] = null
        return
      }
      case "update": {
        Object.assign(draft.formState, action.payload)

        const keys = Object.keys(
          action.payload
        ) as (keyof typeof action.payload)[]

        draft.editedKeys = [...new Set([...draft.editedKeys, ...keys])]

        return
      }
      case "blur": {
        draft.editedKeys = draft.editedKeys.filter(
          (key) => key !== action.payload
        )
        return
      }
      case "mutationError": {
        draft.errors[action.payload.key] = action.payload
          .error as WritableDraft<ApolloError>
        return
      }
      case "validationError": {
        draft.errors[action.payload.key] = action.payload.error
        return
      }
      case "clearErrors": {
        draft.errors = {}
        return
      }
    }
  }, getInitialState(initialState))

  const handleFocus = (key: keyof FormState) => {
    // Clear error
    dispatch({ type: "focus", payload: key })
  }

  function handleChange<T extends keyof FormState>(
    key: T,
    value: FormState[T]
  ) {
    dispatch({ type: "update", payload: { [key]: value } })

    if (timeoutRef.current) clearTimeout(timeoutRef.current)
    timeoutRef.current = setTimeout(() => handleBlur(key), 3000)
  }

  function handleBlur(
    key: keyof FormState,
    value: FormState[keyof FormState] = state.formState[key]
  ) {
    if (value === initialState[key]) return
    if (timeoutRef.current) clearTimeout(timeoutRef.current)

    const unparsedPayload = { ...state.formState, [key]: value }
    const result = UpdateDrugPrescriptionSchema.safeParse(unparsedPayload)

    if (!result.success) {
      // Set error in state
      const errors = result.error.issues
      if (isDev) console.error(errors)

      if (!errors) return
      dispatch({ type: "clearErrors" })

      errors.forEach((e) =>
        e.path.forEach((path) =>
          dispatch({
            type: "validationError",
            payload: { key: path as keyof FormState, error: e?.message },
          })
        )
      )

      return
    } else {
      dispatch({ type: "clearErrors" })
    }

    if (result.data[key] !== value && !(result.data[key] instanceof Date)) {
      dispatch({ type: "update", payload: { [key]: result.data[key] } })
    }

    // This is operating under the assumption that only
    // one key is updated at a time. It that changes
    // we will need to mutate all keys that are updated
    if (journalTemplateId) {
      editDrugPrescriptionTemplate({
        variables: {
          id: initialState.id,
          input: {
            [key]: result.data[key],
          },
        },
        onCompleted: (data) => {
          if (
            data.editDrugPrescriptionTemplate?.__typename ===
            "DrugPrescriptionTemplate"
          ) {
            dispatch({ type: "blur", payload: key })
          }
        },
        onError: (error) => {
          console.error({ error })
          // save error message in state
          dispatch({ type: "mutationError", payload: { key, error } })
        },
      })
      return
    }

    updateDrugPrescription({
      variables: {
        id: initialState.id,
        input: {
          [key]: result.data[key],
        },
      },
      onCompleted: (data) => {
        if (data.updateDrugPrescription?.__typename === "DrugPrescription") {
          dispatch({ type: "blur", payload: key })
        }
      },
      onError: (error) => {
        console.error({ error })
        // save error message in state
        dispatch({ type: "mutationError", payload: { key, error } })
      },
    })
  }

  const register = <T extends HTMLInputElement | HTMLTextAreaElement>(
    key: keyof FormState
  ) => {
    const value = state.formState[key] ?? ""

    const error = state.errors[key]

    const errorMessage =
      typeof error === "string"
        ? error
        : error?.networkError
          ? t("error.networkError")
          : error?.message

    return {
      value: value.toString(),
      name: key,
      onFocus: () => handleFocus(key),
      onBlur: () => handleBlur(key),
      status: state.errors[key] ? ("error" as const) : undefined,
      message: errorMessage,
      onChange: (e: React.ChangeEvent<T>) => {
        handleChange(key, e.target.value)
      },
    }
  }

  const mutationResult = journalTemplateId
    ? updateTemplateMutationResult
    : updateMutationResult

  const saveStatus = getSaveStatus({
    isLoading: mutationResult.loading,
    isError: !!mutationResult.error,
    isCalled: mutationResult.called,
    isContentSync: !(state.editedKeys.length > 0),
  })

  return [
    {
      formState: state.formState,
      errors: state.errors,
      register,
      handleChange,
      handleBlur,
      saveStatus,
    },
    mutationResult,
  ] as const
}
