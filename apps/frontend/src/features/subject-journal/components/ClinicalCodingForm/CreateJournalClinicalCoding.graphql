mutation CreateJournalClinicalCoding(
  $id: UUID
  $input: ClinicalCodingCreateInput!
) {
  createClinicalCoding(input: $input, id: $id) {
    subject {
      id
      clinicalCodings {
        id
      }
    }
    # to render new block
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
      }
    }
    # refetch whole block to visualize the new code as tag
    ...JournalEntryBlockClinicalCodingFragment
  }
}
