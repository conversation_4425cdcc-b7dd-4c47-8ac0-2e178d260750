import { matchSorter } from "match-sorter"
import { FormEvent, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDebounce } from "use-debounce"
import { v4 as uuid } from "uuid"
import z from "zod"

import { useComboboxStore, useSelectStore } from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import Icon from "components/Icon/Icon"
import { useJournalEntryContext } from "features/subject-journal/components/JournalEntry/JournalEntry"
import { Checkbox, FormGrid, Heading, Label, Textarea } from "ui"
import Button from "ui/components/Button/Button"

import {
  ClinicalCodingCriticalType,
  ClinicalCodingType,
  LanguageId,
  useCreateJournalClinicalCodingMutation,
  useFilteredCodesetEntriesQuery,
  useGetMostUsedClinicalCodesQuery,
} from "generated/graphql"

import styles from "../ClinicalCodingForm/ClinicalCodingForm.module.css"

const schema = z.object({
  clinicalCoding: z.string(),
  description: z.string().optional(),
})

export type ClinicalCodingFormProps = {
  onCancel?: () => void
  referenceType: ClinicalCodingType
  onSelectOption?: (
    id: string,
    value: string,
    critical?: boolean,
    description?: string
  ) => void
  autoFocus?: boolean
}

export default function ClinicalCodingForm({
  onCancel,
  referenceType,
  onSelectOption,
  autoFocus = true,
}: ClinicalCodingFormProps) {
  let journalEntryId: string | null = null
  try {
    const journalEntry = useJournalEntryContext()
    journalEntryId = journalEntry.id
  } catch (e) {
    // No journal entry context
  }

  const clinicalCodingRef = useRef<HTMLButtonElement>(null)
  const submitButtonRef = useRef<HTMLButtonElement>(null)

  const { t } = useTranslation()

  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ClinicalCoding_CodingType",
  })

  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "clinicalCoding",
  })

  const [createJournalClinicalCoding] = useCreateJournalClinicalCodingMutation()

  useEffect(() => {
    if (autoFocus) clinicalCodingRef.current?.focus()
  }, [])

  const [critical, setCritical] = useState(false)
  const comboboxStore = useComboboxStore({ defaultItems: [] })

  const { value: comboboxValue } = comboboxStore.useState()

  const [debouncedSearchValue] = useDebounce(comboboxValue, 500)

  const { data, loading, previousData } = useFilteredCodesetEntriesQuery({
    variables: {
      // if referenceType is null, we will not query
      codingType: referenceType || ClinicalCodingType.Diagnosis,
      languageId: LanguageId.Is,
      filter: debouncedSearchValue,
      limit: null,
    },
    // We should not skipping the query if the clinical coding type = Allergy since we only have 1 code for that for now
    skip: !debouncedSearchValue && !ClinicalCodingType.Allergy,
  })

  const {
    data: mostUsedCodesData,
    loading: loadingMostUsedCodes,
    previousData: mostUsedCodesPreviousData,
  } = useGetMostUsedClinicalCodesQuery({
    variables: {
      codingType: referenceType || ClinicalCodingType.Diagnosis,
      languageId: LanguageId.Is,
    },
  })

  const optionsData = loading ? previousData : data
  const mostUsedCodesOptionsData = loadingMostUsedCodes
    ? mostUsedCodesPreviousData
    : mostUsedCodesData

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: "",
    focusLoop: "vertical",
  })

  const { value: selectedValue } = selectStore.useState()

  const mostUsedCodesOptions =
    mostUsedCodesOptionsData?.mostUsedClinicalCodes
      ?.slice(0, 25)
      .map((code) => ({
        label: code.displayLabel,
        value: code.id.toString(),
      })) || []

  // Filter out duplicates from the most used codes
  // Show the first 25 results
  const options =
    optionsData?.clinicalCodes
      ?.filter((c) => {
        return !mostUsedCodesOptions?.some((mc) => {
          return mc.value === c.id.toString()
        })
      })
      .slice(0, 25)
      .map((code) => ({
        label: code.displayLabel,
        value: code.id.toString(),
      })) || []

  const filteredMostUsedCodes = debouncedSearchValue
    ? matchSorter(
        mostUsedCodesOptions.map((code) => code),
        debouncedSearchValue,
        {
          keys: ["label", "value"],
        }
      )
    : mostUsedCodesOptions

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()
    event.stopPropagation()

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = schema.safeParse(data)

    if (!validatedInput.success) {
      console.error(validatedInput.error)
      return
    }

    const clinicalCodingId = validatedInput.data.clinicalCoding
    const allCodes = [...mostUsedCodesOptions, ...options]

    const value =
      allCodes?.find((option) => option.value === clinicalCodingId)?.label || ""

    const id = uuid()
    const description = validatedInput.data.description

    onSelectOption?.(clinicalCodingId, value, critical, description)

    if (journalEntryId) {
      createJournalClinicalCoding({
        variables: {
          id,
          input: {
            journalEntryId,
            code: {
              codeId: clinicalCodingId,
              codingType: referenceType,
            },
            criticalType: critical
              ? ClinicalCodingCriticalType.Normal
              : ClinicalCodingCriticalType.NotCritical,
            description: validatedInput.data.description,
          },
        },
      })
    }
  }

  const placeholder = `${t("typeToSearch")}${
    referenceType === ClinicalCodingType.Diagnosis ? " ICD10" : ""
  }...`

  return (
    <div
      className={styles.container}
      onKeyDown={(e) => {
        e.stopPropagation()
      }}
    >
      <Heading className={styles.formHeading}>
        {tRoutes("newCoding", {
          type: tEnum(referenceType),
        })}
      </Heading>
      <FormGrid
        as="form"
        onSubmit={handleSubmit}
        colSpan={4}
        className={styles.form}
      >
        <FiltrableSelect
          ref={clinicalCodingRef}
          label={tRoutes("selectCodingType", {
            type: tEnum(referenceType),
          })}
          hideLabel
          options={[...mostUsedCodesOptions, ...options]}
          filteredOptionsGroups={[
            {
              label: tRoutes("recentlyUsedCodes"),
              options: filteredMostUsedCodes,
            },
            {
              label: t("Search Results"),
              options,
            },
          ]}
          selectStore={selectStore}
          comboboxStore={comboboxStore}
          placeholder={placeholder}
          portal
          name="clinicalCoding"
          sameWidth
          noOptionsPlaceholder={
            comboboxValue && !loading ? `No ${tEnum(referenceType)} found` : ""
          }
          finalFocus={submitButtonRef}
        />

        <Textarea
          label={tRoutes("description")}
          name="description"
          rows={3}
          autoGrow
        />

        <div className={styles.submitRow}>
          <Checkbox
            label={tRoutes("markAsCritical")}
            checked={critical}
            onChange={() => setCritical(!critical)}
          />

          <div>
            {onCancel && (
              <Button
                size="large"
                disabled={false}
                variant="clear"
                onClick={onCancel}
                data-testid="intervention-period-cancel"
              >
                {t("cancel")}
              </Button>
            )}
            <Button
              ref={submitButtonRef}
              type="submit"
              variant="filled"
              size="large"
              disabled={!selectedValue}
              data-testid="new-clinical-coding-submit"
            >
              {t("confirm")}
            </Button>
          </div>
        </div>

        {journalEntryId && (
          <div className={styles.description}>
            <Icon name="information-line" />
            <Label size="small">
              {tRoutes("entryWillBeMarkedAsOriginOfDiagnosis")}
            </Label>
          </div>
        )}
      </FormGrid>
    </div>
  )
}
