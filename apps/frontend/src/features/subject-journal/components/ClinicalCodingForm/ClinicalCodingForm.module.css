.container {
  padding: 8px 16px;
  gap: 16px;
  display: flex;
  flex-direction: column;
}

.formHeading.formHeading {
  grid-column: 1 / center-start;
  justify-self: self-end;
  padding-bottom: 16px;
  margin: 0;
  color: var(--color-text);
  text-transform: initial;
}

.form {
  position: relative;
}

.span1 {
  grid-column: span 1;
}
.span2 {
  grid-column: span 2;
}
.span3 {
  grid-column: span 3;
}

.submitRow {
  display: flex;
  justify-content: space-between;
  gap: var(--grid-gap);
}

.submitRow div {
  display: flex;
  gap: 8px;
}

.description {
  display: grid;
  gap: 8px;
  grid-auto-flow: column;
  grid-template-columns: 14px auto;
}
