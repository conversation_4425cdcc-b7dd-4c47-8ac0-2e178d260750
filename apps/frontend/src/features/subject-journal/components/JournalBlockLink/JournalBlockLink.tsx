import { forwardRef } from "react"
import { Link, LinkProps } from "react-router-dom"

import Restricted from "features/authentication/components/Restricted/Restricted"
import { useJournalBlockClick } from "hooks/useJournalBlockClick"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"

import { PermissionKey } from "generated/graphql"

type JournalBlockLinkProps = {
  encounterId: string | null
  fallback?: React.ReactNode
  sectionId: string | null
  subjectId: string
  className?: string
  onClick?: () => void
} & Omit<LinkProps, "to">

export default forwardRef<HTMLAnchorElement, JournalBlockLinkProps>(
  function JournalBlockLink(
    { encounterId, fallback, sectionId, subjectId, onClick, ...rest },
    ref
  ) {
    const { onClick: handleClick } = useJournalBlockClick({
      encounterId,
      sectionId,
    })

    const subjectJournalPath = getRecordPagePath(
      RouteStrings.subjectJournal,
      subjectId
    )

    return (
      <Restricted
        to={PermissionKey.SubjectJournalJournalEntryView}
        fallback={fallback}
      >
        <Link
          ref={ref}
          to={subjectJournalPath}
          onClick={(e) => {
            handleClick(e)
            onClick?.()
          }}
          {...rest}
        />
      </Restricted>
    )
  }
)
