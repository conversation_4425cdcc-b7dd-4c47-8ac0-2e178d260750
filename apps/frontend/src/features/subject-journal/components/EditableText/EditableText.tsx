import c from "classnames"
import React, { ElementType, useEffect, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Input, InputProps, Text, TextOwnProps } from "ui"
import Button from "ui/components/Button/Button"
import ButtonGroup from "ui/components/ButtonGroup/ButtonGroup"

import styles from "./EditableText.module.css"

type EditableTextProps = {
  title?: string | null | undefined
  isEditable: boolean
  onSave: (newTitle: string) => void
  inputSize?: InputProps["size"]
  textSize?: TextOwnProps["size"]
  as?: string
  fallbackText?: string
  wrapperElement?: ElementType
  viewModeClassName?: string
  editModeClassName?: string
  testid?: string
}

enum Mode {
  VIEW = "VIEW",
  EDIT = "EDIT",
}

/* COMEBACK this component does not use same polymorphism strategy as others, should be updated. E.g. misses className on wrapper.  */
const EditableText = ({
  title,
  onSave,
  isEditable,
  inputSize,
  textSize,
  as,
  fallbackText,
  wrapperElement: WrapperElement = Text,
  viewModeClassName,
  editModeClassName,
  testid,
}: EditableTextProps) => {
  const { t } = useTranslation()
  const [mode, setMode] = useState(Mode.VIEW)

  const inputRef = useRef<HTMLInputElement>(null)

  const handleEditClick = () => {
    if (isEditable) {
      setMode(Mode.EDIT)
    }
  }

  useEffect(() => {
    if (mode === Mode.EDIT && inputRef.current) {
      inputRef.current.focus()
      inputRef.current.select()
    }
  }, [mode])

  if (!title && isEditable && mode === Mode.VIEW) {
    return (
      <div className={styles.editModeCenter}>
        <WrapperElement
          as={as}
          size={textSize}
          className={styles.clickableText}
        >
          <Button onClick={handleEditClick} className={styles.textButton}>
            {fallbackText || t("clickToAddTitle")}
          </Button>
        </WrapperElement>
      </div>
    )
  }

  if (mode === Mode.VIEW) {
    return (
      <div className={c(styles.editModeCenter, viewModeClassName)}>
        <WrapperElement
          as={as}
          size={textSize}
          onClick={isEditable ? handleEditClick : undefined}
          className={isEditable ? styles.wrapperElement : undefined}
          data-testid={testid || undefined}
        >
          {title}
        </WrapperElement>
        {isEditable && (
          <Button
            icon={<Icon name="edit-line" />}
            onClick={handleEditClick}
            className={styles.editModeButton}
          />
        )}
      </div>
    )
  }

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const title = data.title as string

    onSave(title)
    setMode(Mode.VIEW)
  }

  return (
    <form
      onSubmit={onSubmit}
      className={c(styles.editModeCenter, styles.editing, editModeClassName)}
    >
      <Input
        ref={inputRef}
        name="title"
        defaultValue={title || ""}
        label="Edit title"
        hideLabel
        hideMessage
        className={styles.input}
        size={inputSize}
        minLength={3}
      />
      <ButtonGroup className={styles.buttonGroup}>
        <Button
          aria-label="Save changes"
          type="submit"
          icon={<Icon name="check-line" />}
        ></Button>
        <Button
          aria-label="Discard changes"
          icon={<Icon name="close-line" />}
          onClick={() => setMode(Mode.VIEW)}
        ></Button>
      </ButtonGroup>
    </form>
  )
}

export default EditableText
