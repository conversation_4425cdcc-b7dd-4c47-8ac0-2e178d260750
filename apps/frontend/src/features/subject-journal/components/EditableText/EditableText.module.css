.editModeCenter {
  display: flex;
  align-items: baseline;
  margin: 0;
  max-width: fit-content;
  min-height: 32px;
  gap: 12px;
}
.editing {
  align-items: center;
  max-width: unset;
}

.buttonGroup {
  height: 32px;
}

.clickableText {
  color: var(--color-neutral-700);
  cursor: pointer;
}

.wrapperElement {
  cursor: pointer;
}

.editModeButton {
  border: none;
  opacity: 0;
  transition: opacity 150ms;
}

.editModeCenter:hover .editModeButton {
  opacity: 1;
}

.textButton {
  all: unset;
  color: var(--color-neutral-700);
}

.textButton:hover {
  all: unset;
  color: var(--color-gray-70);
}
.input {
  flex-grow: 1;
}
