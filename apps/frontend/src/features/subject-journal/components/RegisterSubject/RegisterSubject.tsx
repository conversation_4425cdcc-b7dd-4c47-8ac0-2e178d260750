import React, { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"
import { Link, generatePath, useNavigate, useLocation } from "react-router-dom"
import { z } from "zod"

import {
  getFormattedBirthDate,
  isPseudoIdNumber,
  isPersonaIdValid,
  sanitizePersonaId,
  getBirthDate,
} from "@leviosa/utils"

import { useSelectStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import PersonaIdInput from "components/PersonaIdInput/PersonaIdInput"
import Select from "components/Select/Select"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import {
  Button,
  FormFooter,
  FormGrid,
  Heading,
  Input,
  notification,
  Status,
  TextWithIcon,
} from "ui"
import { Center } from "ui/components/Layout/Center"

import {
  GenderId,
  PersonaIdType,
  useCreateSubjectMutation,
  useLookUpPersonaIdLazyQuery,
} from "generated/graphql"

import styles from "./RegisterSubject.module.css"

// Zod schema for validation
const registerSubjectSchema = z.object({
  personaId: z.string().min(10, "Persona ID must be 10 characters long."),
  name: z.string().min(3, "Name must be at least 3 characters long."),
  gender: z.nativeEnum(GenderId),
})

export default function RegisterSubject() {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "manageSubject",
  })
  const location = useLocation()
  const { personaId } = location.state || {}

  const [personaIdValue, setPersonaIdValue] = useState<string | undefined>(
    personaId
  )
  const [nameValue, setNameValue] = useState("")
  const [isPersonaIdBlurred, setIsPersonaIdBlurred] = useState(false)
  const [validationErrors, setValidationErrors] = useState({
    personaId: "",
    name: "",
  })
  const [matchingSubjects, setMatchingSubjects] = useState<
    Array<{ id: string; name: string }>
  >([])

  const navigate = useNavigate()
  const selectGenderStore = useSelectStore({})
  const [lookUpPersonaId] = useLookUpPersonaIdLazyQuery()
  const [createSubject, { loading: submitLoad }] = useCreateSubjectMutation()

  const handleBlur = (field: keyof typeof validationErrors) => {
    validateField(field)
    if (field === "personaId") {
      setIsPersonaIdBlurred(true)
      if (isPersonaIdValid(personaIdValue, true)) {
        lookUpPersonaId({
          variables: { personaId: sanitizePersonaId(personaIdValue) },
          onCompleted: ({ lookUpPersonaId: { matchedSubjects } }) => {
            setMatchingSubjects(matchedSubjects)
          },
        })
      } else {
        setMatchingSubjects([])
      }
    }
  }

  const validateField = (field: keyof typeof validationErrors) => {
    const inputData = {
      personaId: personaIdValue,
      name: nameValue,
    }

    const validationResult = registerSubjectSchema.safeParse(inputData)

    if (!validationResult.success) {
      const fieldError = validationResult.error.issues.find(
        (issue) => issue.path[0] === field
      )
      setValidationErrors((prev) => ({
        ...prev,
        [field]: fieldError?.message || "",
      }))
    } else {
      setValidationErrors((prev) => ({ ...prev, [field]: "" }))
    }
  }

  const handleSubmit = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const validatedInput = registerSubjectSchema.safeParse({
      ...data,
      personaId: personaIdValue,
    })

    if (!validatedInput.success) {
      validatedInput.error.issues.forEach((issue) => {
        const field = issue.path[0] as keyof typeof validationErrors
        setValidationErrors((prev) => ({ ...prev, [field]: issue.message }))
      })
      return
    }

    const { personaId, name, gender } = validatedInput.data

    if (!isPersonaIdValid(personaId, true)) {
      notification.create({
        status: "error",
        message: tRoutes("invalidPersonaId"),
      })
      return
    }

    if (matchingSubjects.length > 0) {
      notification.create({
        status: "error",
        message: tRoutes("personaIdAlreadyExists"),
      })
      return
    }

    createSubject({
      variables: {
        input: {
          personaId: sanitizePersonaId(personaId),
          birthDate: getBirthDate(personaId) as Date,
          gender,
          name,
          personaIdType: isPseudoIdNumber(personaId)
            ? PersonaIdType.PseudoIdNumber
            : PersonaIdType.IsIdNumber,
          nationality: "IS",
        },
      },
      onCompleted: ({ createSubject }) =>
        navigate(
          generatePath(RouteStrings.subjectEdit, {
            subjectId: createSubject.id,
          }) + "#phoneNumber"
        ),
      onError: (error) =>
        notification.create({ status: "error", message: error.message }),
    })
  }

  const birthDate = getFormattedBirthDate(personaIdValue)

  const getHelperMessageForPersonaId = (): {
    message: React.ReactNode
    status: Status
  } => {
    if (isPersonaIdBlurred) {
      if (matchingSubjects.length > 0) {
        return {
          message: (
            <>
              <span>{tRoutes("personaIdAlreadyExists")}</span>
              <Link
                to={getRecordPagePath(
                  RouteStrings.subjectEdit,
                  matchingSubjects[0].id
                )}
              >
                {` ${matchingSubjects[0].name}`}
              </Link>
            </>
          ),
          status: "error",
        }
      }

      if (birthDate) {
        return { message: birthDate, status: "success" }
      }

      if (!isPersonaIdValid(personaIdValue, true)) {
        return { message: tRoutes("invalidPersonaId"), status: "error" }
      }
    }

    return { message: "", status: "default" }
  }

  const helperMessage = getHelperMessageForPersonaId()

  return (
    <Center className={styles.wrapper}>
      <FormGrid colSpan={6} id="register-subject-form" onSubmit={handleSubmit}>
        <Heading size="display" as="h1" className={styles.heading}>
          {tRoutes("registerNewSubject")}
        </Heading>

        <PersonaIdInput
          type="text"
          allowAlternatePersonaId={true}
          label={t("PersonaID")}
          placeholder={tRoutes("personaIdTypes.IS")}
          autoFocus
          required
          onBlur={() => handleBlur("personaId")}
          onFocus={() => setIsPersonaIdBlurred(false)}
          value={personaIdValue}
          onChange={({ currentTarget }) => {
            setPersonaIdValue(currentTarget.value)
            setValidationErrors((prev) => ({ ...prev, personaId: "" }))
          }}
          status={helperMessage.status}
          message={helperMessage.message}
        />
        <div />

        <Input
          label={t("Name")}
          name="name"
          required
          onBlur={() => handleBlur("name")}
          onChange={({
            currentTarget,
          }: React.ChangeEvent<HTMLInputElement>) => {
            setNameValue(currentTarget.value)
            setValidationErrors((prev) => ({ ...prev, name: "" }))
          }}
          status={validationErrors.name ? "error" : "default"}
          message={validationErrors.name}
          inputProps={{ "data-1p-ignore": true }}
        />
        <div />

        <Select
          label="Gender"
          name="gender"
          placeholder=""
          required
          selectStore={selectGenderStore}
          options={Object.entries(GenderId).map(([label, value]) => ({
            label,
            value,
          }))}
        />
        <div />

        {isPseudoIdNumber(personaIdValue) && (
          <Panel status="info" className={styles.panel}>
            <TextWithIcon>
              {tRoutes("pseudoIdNumberInfoPanelText")}
            </TextWithIcon>
          </Panel>
        )}

        <FormFooter>
          <Button
            type="reset"
            onClick={() => {
              setPersonaIdValue("")
              setNameValue("")

              setIsPersonaIdBlurred(false)
              setValidationErrors({ personaId: "", name: "" })
              setMatchingSubjects([])
            }}
          >
            {t("Clear")}
          </Button>

          <Button
            type="submit"
            variant="filled"
            disabled={submitLoad}
            icon={submitLoad && <Icon name="loader-4-line" spin />}
          >
            {tRoutes("registerNewSubject")}
          </Button>
        </FormFooter>
      </FormGrid>
    </Center>
  )
}
