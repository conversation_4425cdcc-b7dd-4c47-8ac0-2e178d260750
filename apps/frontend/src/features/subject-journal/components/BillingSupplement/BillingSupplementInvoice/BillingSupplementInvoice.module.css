.wrap {
  display: grid;
  grid-row-gap: 8px;
  color: var(--color-text-secondary);
}

.table {
  --color-table-bg-odd: var(--color-neutral-200);
  --color-table-bg-even: var(--color-white);
  --color-table-bg-odd-completed: rgba(255, 255, 255, 0.4);
  --color-table-bg-even-completed: inherit;
}

.table tr th {
  padding: 8px 10px;
  font-size: 12px;
  background: var(--color-neutral-500);
}

.table tr td {
  padding: 6px 10px;
  font-size: 14px;
  color: var(--color-text);
}

.table:not(.draft) tr td {
  color: var(--color-blue-gray-violet-dark);
}

.table :where(tbody tr:nth-child(odd) :is(td, th)) {
  background: var(--color-table-bg-odd);
}

.table :where(tbody tr:nth-child(even) :is(td, th)) {
  background: var(--color-table-bg-even);
}

.quantity {
  text-align: right;
  width: 100px;
}

.options {
  width: 60px;
}

.deleteRow {
  text-align: center;
}

.code {
  min-width: 100px;
}

.inputWrap.inputWrap > input {
  background-color: inherit;
  border: 1px solid transparent;
  padding: 5px 8px;
}

.menuButton.menuButton {
  font-size: 20px;
  opacity: 0;
  border-radius: 50%;
}

.menuButton[aria-expanded="true"],
.menuButton:active {
  opacity: 1;
}

.menuItem {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
  align-items: center;
}

.completed :where(tbody tr:nth-child(odd) :is(td, th)) {
  background: var(--color-table-bg-odd-completed);
}

.completed :where(tbody tr:nth-child(even) :is(td, th)) {
  background: var(--color-table-bg-even-completed);
}

.clipText {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.hideBillingItem {
  visibility: hidden;
}

.invoiceText {
  color: var(--color-text-secondary);
}

.completed .itemRow:hover .menuButton.menuButton {
  opacity: 0;
}

tr:hover .menuButton.menuButton,
.menuButton.menuButton:focus {
  opacity: 1;
  transition: opacity 0.15s ease-in-out;
}

.footer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
}

.attachmentButton.attachmentButton {
  justify-self: end;
}

.openInvoiceButton {
  justify-self: end;
}

.draft tr th {
  padding: 8px 10px;
  font-size: 12px;
  background: var(--color-main);
}

.table tr:hover {
  background: initial;
}

.deleteButton {
  align-self: flex-end;
}

.header {
  display: grid;
  grid-template-columns: auto auto 1fr auto;
  grid-gap: 8px;
  align-items: center;
}

.headerDraft {
  margin-bottom: -8px;
}
