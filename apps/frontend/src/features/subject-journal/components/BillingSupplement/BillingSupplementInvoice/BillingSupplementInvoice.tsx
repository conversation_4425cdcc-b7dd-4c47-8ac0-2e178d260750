import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link } from "react-router-dom"

import { Menu, MenuButton, MenuItem, MenuProvider } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import { AttachmentButton } from "components/AttachmentButton/AttachmentButton"
import { AttachmentsPreview } from "components/AttachmentsPreview/AttachmentsPreview"
import Icon from "components/Icon/Icon"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import { InvoiceStatusTag } from "features/billing/components/InvoiceOverview/InvoiceOverviewTable/InvoiceStatusTag/InvoiceStatusTag"
import { invoicePdfUrl } from "features/billing/components/InvoiceOverview/invoicePdfUrl"
import { QuantityInput } from "features/billing/components/QuantityInput/QuantityInput"
import { AddBillingItem } from "features/subject-journal/components/BillingSupplement/AddBillingItem/AddBillingItem"
import { BillingTitleTooltip } from "features/subject-journal/components/BillingSupplement/ShowTextInPopover/BillingTitleTooltip"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, IconButton, notification, Table, Text } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import useDateFormatter from "utils/useDateFormatter"

import {
  EncounterFragmentFragment,
  EncounterInvoiceLineFragmentFragment,
  PermissionKey,
  useCreateInvoiceLineMutation,
  useCreateInvoiceMutation,
  useDeleteInvoiceLineMutation,
  useDeleteInvoiceMutation,
  useEditInvoiceLineQuantityMutation,
  useMoveInvoiceLineMutation,
} from "generated/graphql"

import styles from "./BillingSupplementInvoice.module.css"

type BillingSupplementInvoiceProps = {
  encounterId: string
  invoice: EncounterFragmentFragment["invoices"][number]
  draftInvoices?: EncounterFragmentFragment["invoices"]
  canCreateInvoice: boolean
}

export default function BillingSupplementInvoice({
  encounterId,
  invoice,
  draftInvoices,
  canCreateInvoice,
}: BillingSupplementInvoiceProps) {
  const { t } = useTranslation()
  const dateFormatter = useDateFormatter()
  const [deleteInvoice] = useDeleteInvoiceMutation()
  const [invoiceToDelete, setInvoiceToDelete] = useState<string | null>(null)
  const [createInvoiceLine, { error, loading }] = useCreateInvoiceLineMutation()

  const {
    id,
    invoiceNumber,
    paymentDate: paymentDateString,
    issued,
    invoiceLines,
    subject,
    paymentStatus,
  } = invoice

  const paymentDate = paymentDateString
    ? dateFormatter(new Date(paymentDateString))
    : null

  const fileUrl = invoicePdfUrl(invoice.id)
  // Filtering out credit invoices since we don't want to show them in the subject journal
  // and this is currently the only way to know if an invoice is a credit invoice
  if (invoiceNumber.charAt(0) === "C") {
    return null
  }

  return (
    <div className={styles.wrap}>
      <div
        className={c(styles.header, {
          [styles.headerDraft]: !issued,
        })}
      >
        <Heading size="xsmall">
          {t("Invoice")} {invoiceNumber}
        </Heading>
        <InvoiceStatusTag
          id={id}
          paymentStatus={paymentStatus}
          size="small"
          issued={issued}
        />
        <Heading size="xsmall">
          {paymentDate && <span> {paymentDate}</span>}
        </Heading>

        {!issued && (
          <IconButton
            iconName="delete-bin-line"
            variant="clear"
            size="large"
            className={styles.deleteButton}
            onClick={() => {
              setInvoiceToDelete(id)
            }}
          />
        )}
      </div>

      <BillingSupplementInvoiceTable
        invoiceLines={invoiceLines}
        draft={!issued}
        draftInvoices={draftInvoices}
        encounterId={encounterId}
        canCreateInvoice={canCreateInvoice}
      />

      {issued ? (
        <AttachmentsPreview
          attachments={[
            {
              url: fileUrl,
              name: `${t("Invoice")} ${invoiceNumber}.pdf`,
            },
          ]}
        >
          <AttachmentButton
            fileUrl={fileUrl}
            color="green"
            className={c(styles.attachmentButton)}
            extension="pdf"
            isSensitiveData={false}
          >
            {t("View invoice")}
          </AttachmentButton>
        </AttachmentsPreview>
      ) : (
        <div className={styles.footer}>
          <Restricted to={PermissionKey.BillingIssuedItemCreate}>
            <AddBillingItem
              error={error?.message}
              className={styles.addItem}
              isLoading={loading}
              onSelectItem={(billingCodeId, billingCodeType) => {
                createInvoiceLine({
                  variables: {
                    input: {
                      invoiceId: id,
                      encounterId,
                      billingCodeId,
                      quantity: 1,
                      billingCodeType,
                    },
                  },
                })
              }}
            />
          </Restricted>

          <Button
            as={Link}
            variant="clear"
            className={styles.openInvoiceButton}
            to={{
              pathname: generatePath(RouteStrings.patientInvoice, {
                invoiceId: id,
              }),
            }}
          >
            {t("Open invoice")}
          </Button>
        </div>
      )}

      <Dialog
        isOpen={!!invoiceToDelete}
        onClose={() => setInvoiceToDelete(null)}
        title={t("Delete draft invoice")}
        actions={
          <>
            <Button
              onClick={() => {
                setInvoiceToDelete(null)
              }}
              variant="clear"
            >
              {t("Keep invoice")}
            </Button>
            <Button
              status="error"
              onClick={() =>
                invoiceToDelete &&
                deleteInvoice({
                  variables: {
                    input: { id: invoiceToDelete },
                  },
                })
              }
            >
              {t("doDelete")}
            </Button>
          </>
        }
      >
        <Text>
          This action will delete this draft invoice for {subject.name}. This
          cannot be undone.
        </Text>
      </Dialog>
    </div>
  )
}

export function BillingSupplementInvoiceTable({
  invoiceLines,
  draft,
  draftInvoices,
  encounterId,
  canCreateInvoice,
}: {
  invoiceLines: EncounterInvoiceLineFragmentFragment[]
  draft?: boolean
  draftInvoices?: EncounterFragmentFragment["invoices"]
  encounterId: string
  canCreateInvoice: boolean
}) {
  const { t } = useTranslation()
  const editInvoiceLineResult = useEditInvoiceLineQuantityMutation()
  const deleteInvoiceLineResult = useDeleteInvoiceLineMutation()

  const isEditLoading = editInvoiceLineResult[1].loading
  const isDeleteLoading = deleteInvoiceLineResult[1].loading
  const isLoading = isEditLoading || isDeleteLoading

  return (
    <Table
      className={c(styles.table, {
        [styles.draft]: draft,
      })}
      hoverable={false}
    >
      <thead>
        <tr>
          <th>{t("Item")}</th>
          <th>{t("Item name")}</th>
          <th>{t("Description")}</th>
          <th className={styles.quantity}>{t("Quantity")}</th>
          <th className={styles.options}></th>
        </tr>
      </thead>
      <tbody>
        {invoiceLines.map((line) => (
          <InvoiceLine
            key={line.id}
            {...line}
            draftInvoices={draftInvoices}
            encounterId={encounterId}
            canCreateInvoice={canCreateInvoice}
            editInvoiceLineResult={editInvoiceLineResult}
            deleteInvoiceLineResult={deleteInvoiceLineResult}
            disabled={isLoading}
          />
        ))}
      </tbody>
    </Table>
  )
}

function InvoiceLine({
  id,
  billingCode,
  quantity,
  billableQuantity,
  invoice,
  encounterId,
  draftInvoices,
  canCreateInvoice,
  disabled,
  editInvoiceLineResult,
  deleteInvoiceLineResult,
}: EncounterInvoiceLineFragmentFragment & {
  encounterId: string
  draftInvoices?: EncounterFragmentFragment["invoices"]
  canCreateInvoice: boolean
  disabled: boolean
  editInvoiceLineResult: ReturnType<typeof useEditInvoiceLineQuantityMutation>
  deleteInvoiceLineResult: ReturnType<typeof useDeleteInvoiceLineMutation>
}) {
  const { t } = useTranslation()
  const { hasPermission } = usePermissions()
  const [editLine, { error: editLineError }] = editInvoiceLineResult

  const [deleteLine, { loading }] = deleteInvoiceLineResult

  const [createInvoice] = useCreateInvoiceMutation({})
  const [moveInvoiceLine] = useMoveInvoiceLineMutation({})

  const menu = useMenuStore({
    placement: "bottom-end",
    animated: true,
  })

  const handleChangeQuantity = (quantity: number) => {
    return editLine({
      variables: {
        input: {
          invoiceLineId: id,
          quantity,
        },
      },
      optimisticResponse: {
        __typename: "Mutation",
        editInvoiceLine: {
          __typename: "InvoiceLine",
          id,
          quantity,
          billableQuantity,
        },
      },
    })
  }

  const issued = invoice?.issued || false
  const canEdit = !issued && hasPermission(PermissionKey.BillingIssuedItemEdit)
  const code =
    billingCode?.__typename === "BillingCodeClinicSpecific"
      ? billingCode.clinicCode
      : billingCode.code

  if (loading) return null

  return (
    <tr key={id}>
      <td className={styles.code}>{code}</td>
      <td>
        <BillingTitleTooltip text={billingCode.title} />
      </td>
      <td>{billingCode.description}</td>
      <td className={styles.quantity}>
        {issued ? (
          quantity
        ) : (
          <QuantityInput
            name="quantity"
            quantity={quantity}
            disabled={disabled}
            error={editLineError?.message}
            onBlur={handleChangeQuantity}
            className={styles.inputWrap}
          />
        )}
      </td>
      <td className={styles.options}>
        {canEdit && (
          <MenuProvider store={menu}>
            <MenuButton
              variant="clear"
              store={menu}
              className={styles.menuButton}
              iconEnd={<Icon name="more-line" />}
              onClick={(e: { stopPropagation: () => void }) => {
                e.stopPropagation()
              }}
            />
            <Menu portal>
              {draftInvoices?.map((invoice) => {
                return (
                  <MenuItem
                    key={invoice.id}
                    className={styles.menuItem}
                    onClick={() => {
                      moveInvoiceLine({
                        variables: {
                          input: {
                            invoiceId: invoice.id,
                            id,
                          },
                        },
                        onError: () => {
                          notification.create({
                            message: `${t("Could not move item to invoice")} ${
                              invoice.invoiceNumber
                            }}`,
                            status: "error",
                          })
                        },
                      })
                    }}
                  >
                    <Icon name="corner-down-right-line" />
                    {`${t("Move to invoice")} ${invoice.invoiceNumber}`}
                  </MenuItem>
                )
              })}

              {canCreateInvoice && (
                <MenuItem
                  className={styles.menuItem}
                  onClick={() => {
                    createInvoice({
                      variables: {
                        input: {
                          encounterId,
                        },
                      },
                      onCompleted: (data) => {
                        moveInvoiceLine({
                          variables: {
                            input: {
                              invoiceId: data.createInvoice.id,
                              id,
                            },
                          },
                          onError: () => {
                            notification.create({
                              message: t("Could not move item to new invoice"),
                              status: "error",
                            })
                          },
                        })
                      },
                      onError: () => {
                        notification.create({
                          message: t("Could not create new invoice"),
                          status: "error",
                        })
                      },
                    })
                  }}
                >
                  <Icon name="corner-down-right-line" />
                  {t("Move to new invoice")}
                </MenuItem>
              )}
              <MenuItem
                className={styles.menuItem}
                onClick={() => {
                  deleteLine({
                    variables: {
                      input: { id },
                    },
                    onError: () => {
                      notification.create({
                        message: t("Could not remove item from invoice"),
                        status: "error",
                      })
                    },
                  })
                }}
              >
                <Icon name="delete-bin-fill" />
                {t("Remove from invoice")}
              </MenuItem>
            </Menu>
          </MenuProvider>
        )}
      </td>
    </tr>
  )
}
