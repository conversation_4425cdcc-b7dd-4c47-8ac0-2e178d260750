import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { Text } from "ui"

import styles from "./ShowTextInPopover.module.css"

type BillingTitleTooltipProps = {
  text: string
}

export const BillingTitleTooltip = ({ text }: BillingTitleTooltipProps) => {
  if (text.length <= 50) return <Text>{text}</Text>

  return (
    <Tooltip tooltipClassName={styles.tooltipClassName} tooltipContent={text}>
      <Text className={styles.clipText}>{text}</Text>
    </Tooltip>
  )
}
