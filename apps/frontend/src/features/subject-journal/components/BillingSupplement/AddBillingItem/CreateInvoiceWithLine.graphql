mutation CreateInvoiceWithLine(
  $invoiceId: UUID!
  $encounterId: UUID!
  $billingCodeId: UUID!
  $billingCodeType: BillingCodeType!
  $quantity: Float!
) {
  createInvoice(input: { encounterId: $encounterId, id: $invoiceId }) {
    ...EncounterInvoiceFragment
    encounter {
      id
      invoices {
        id
      }
    }
  }
  createInvoiceLine(
    input: {
      billingCodeId: $billingCodeId
      billingCodeType: $billingCodeType
      encounterId: $encounterId
      invoiceId: $invoiceId
      quantity: $quantity
    }
  ) {
    id
    ...EncounterInvoiceLineFragment
    invoice {
      id
      invoiceLines {
        id
      }
    }
    encounter {
      id
      invoiceLines {
        id
      }
    }
  }
}
