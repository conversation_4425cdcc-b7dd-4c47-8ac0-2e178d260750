import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { BillingCodeSelect } from "features/billing/components/PatientInvoice/BillingCodeSelect/BillingCodeSelect"
import { TextWithIcon, Text } from "ui"

import { BillingCodeType } from "generated/graphql"

import styles from "./AddBillingItem.module.css"

type AddBillingItemProps = {
  className?: string
  error?: string
  newInvoice?: boolean
  isLoading?: boolean
  onSelectItem: (
    billingCodeId: string,
    billingCodeType: BillingCodeType
  ) => void
}

export const AddBillingItem = ({
  className,
  error,
  newInvoice,
  isLoading,
  onSelectItem,
}: AddBillingItemProps) => {
  const { t } = useTranslation()

  return (
    <>
      {error && (
        <Panel status="warning" className={styles.errorPanel}>
          <TextWithIcon iconName="alert-line">
            {t(
              "Could not create invoice line. Please try again. If the problem persists, please contact support."
            )}
            <Text size="small" className={styles.errorMessage}>
              {error}
            </Text>
          </TextWithIcon>
        </Panel>
      )}
      <BillingCodeSelect
        className={`${styles.addBillingItemMenu} ${className}`}
        isLoading={isLoading}
        label={newInvoice ? t("Add item to new invoice") : t("Add item")}
        onSelect={(billingCodeId, billingCodeType) => {
          onSelectItem(billingCodeId, billingCodeType)
        }}
      />
    </>
  )
}
