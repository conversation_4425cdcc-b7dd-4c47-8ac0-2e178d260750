import {
  Fragment,
  useCallback,
  useDeferredValue,
  useEffect,
  useState,
  useMemo,
} from "react"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import Panel from "components/Panel/Panel"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { Button, JournalGrid, Modal } from "ui"
import ClinicalCodingCard from "ui/components/ClinicalCodingCard/ClinicalCodingCard"
import { Dialog } from "ui/components/Dialog/Dialog"
import { Center } from "ui/components/Layout/Center"
import { SubjectOverviewBar } from "ui/components/SubjectOverviewBar/SubjectOverviewBar"
import { SupplementsWrapper } from "ui/components/Supplement/Supplement"
import { isTypename } from "utils/isTypename"
import { removeWhiteSpaceFromContent } from "utils/removeWhiteSpaceFromContent"
import { stripHtml } from "utils/stripHtml"

import {
  EncounterFragmentFragment,
  ExternalMessage,
  JournalEntryBlockStatus,
  JournalEntryStatus,
  JournalTemplateFragmentFragment,
  JournalTemplateType,
  LanguageId,
  PermissionKey,
  useCreateSubjectInteractionMutation,
  useDeleteJournalEntryBlockMutation,
  useGetJournalTemplatesQuery,
  useGetSubjectJournalQuery,
} from "generated/graphql"

import { AddSupplements } from "../AddSupplements/AddSupplements"
import { CreateEncounter } from "../CreateEncounter/CreateEncounter"
import Encounter from "../Encounter/Encounter"
import { JournalInboundData as InboundDataComp } from "../InboundData/InboundData"
import { InterventionPeriod } from "../InterventionPeriod/InterventionPeriod"
import { InterventionPeriodHeader } from "../InterventionPeriod/InterventionPeriodHeader"
import JournalAttachments from "../JournalAttachments/JournalAttachments"
import { JournalDataInViewProvider } from "../JournalDataInView/JournalDataInView.context"
import JournalEntry from "../JournalEntry/JournalEntry"
import JournalEntryBlock from "../JournalEntryBlock/JournalEntryBlock"
import JournalEntryBlockDrugPrescription from "../JournalEntryBlockDrugPrescription/JournalEntryBlockDrugPrescription"
import JournalEntryBlockNote from "../JournalEntryBlockNote/JournalEntryBlockNote"
import { JournalEntryFreeText } from "../JournalEntryFreeText/JournalEntryFreeText"
import JournalFocus, {
  JournalFocusIndicator,
} from "../JournalFocus/JournalFocus"
import MedicalCertificateSupplement from "../MedicalCertificateSupplement/MedicalCertificateSupplement"
import { OutboundMessage } from "../OutboundMessage/OutboundMessage"
import InterventionPeriodNavigation from "../SubjectJournalSideNavigation/SubjectJournalSideNavigation"
import { UploadAttachment } from "../UploadAttachment/UploadAttachment"
import { EmptySubjectJournal } from "./EmptySubjectJournal/EmptySubjectJournal"
import NoInterventionPeriodFound from "./NoInterventionPeriodFound"
import styles from "./SubjectJournal.module.css"
import {
  searchInterventionPeriods,
  searchEncounters,
} from "./subjectJournalSearch/subjectJournalSearch"

const RenderEncounter = ({
  id: encounterId,
  journalEntries,
  hiddenJournalEntriesCount,
  documentTemplates,
  contentTemplates,
  ...rest
}: EncounterFragmentFragment & {
  hiddenJournalEntriesCount?: number
  lastInterventionPeriodEncounter?: boolean
  documentTemplates: JournalTemplateFragmentFragment[]
  contentTemplates: JournalTemplateFragmentFragment[]
}) => {
  const canEditBlock = (canActorEdit: boolean, status: JournalEntryStatus) =>
    canActorEdit && status === JournalEntryStatus.Draft

  const [journalEntryId, setJournalEntryId] = useState<string | null>(null)

  const { t } = useTranslation()

  const [blockToDelete, setBlockToDelete] = useState<string | null>(null)

  const [deleteJournalEntryBlock] = useDeleteJournalEntryBlockMutation()

  const handleDeleteBlock = (id: string) => {
    deleteJournalEntryBlock({
      variables: {
        blockId: id,
      },
      onCompleted: () => {
        setBlockToDelete(null)
      },
    })
  }

  const onDelete = (
    canEdit: boolean,
    status: JournalEntryStatus,
    blockId: string
  ): (() => void) | undefined => {
    const canDelete = canEditBlock(canEdit, status)
    return canDelete
      ? () => {
          setBlockToDelete(blockId)
        }
      : undefined
  }

  const handleAddBillingItem = useCallback(() => {
    const element = document.getElementById(`${encounterId}-billing`)
    if (element) {
      // For some reason the scrollIntoView does not work
      // from the AddSupplements menu in Chrome without the timeout
      setTimeout(() => {
        element.scrollIntoView({ behavior: "smooth", block: "start" })
      }, 0)
    }
  }, [encounterId])

  return (
    <Encounter
      key={encounterId}
      id={encounterId}
      journalEntries={journalEntries}
      journalTemplates={documentTemplates}
      {...rest}
    >
      <Restricted to={PermissionKey.SubjectJournalJournalEntryView}>
        {journalEntries.map(
          ({
            id: journalEntryId,
            blocks,
            createdAt,
            createdBy,
            title,
            sections,
            status,
            canActorEdit,
            canActorSign,
            documentType,
            inlineGuide,
            undoUntil,
          }) => {
            const journalEntrySupplements = blocks.filter(
              (b) => !isTypename("Note")(b)
            )

            const attachments = blocks.filter(
              isTypename("JournalEntryAttachment")
            )

            const nonCompletableBlocks = blocks
              .filter((b) => !b.completable)
              .filter((b) => !isTypename("JournalEntryAttachment")(b))
              .map(({ id }) => id)

            const nonCompletableSections =
              sections?.filter((s) => !s.completable).map(({ id }) => id) || []

            // Check if at least one block is completed - if present
            const hasCompletedBlocks =
              blocks
                .filter((b) => b.status === JournalEntryBlockStatus.Completed)
                .filter((b) => !isTypename("JournalEntryAttachment")(b))
                .map(({ id }) => id).length > 0

            // Check if at least one section has content - if present
            const sectionHasContent =
              (sections || []).filter((section) => {
                return (
                  stripHtml(removeWhiteSpaceFromContent(section.content))
                    .length > 0
                )
              }).length > 0

            const canCompleteJournalEntry =
              nonCompletableSections.length === 0 &&
              nonCompletableBlocks.length === 0 &&
              (sectionHasContent || hasCompletedBlocks)

            const firstItemPreventingComplete =
              nonCompletableSections.length > 0
                ? nonCompletableSections[0]
                : nonCompletableBlocks.length > 0
                  ? nonCompletableBlocks[0]
                  : null

            const constantRows = 3 // Header, supplements, footer
            const hasAttachments = attachments.length > 0
            const entryRulerSpan =
              constantRows + (sections ?? []).length + (hasAttachments ? 1 : 0)

            return (
              <JournalEntry
                key={journalEntryId}
                id={journalEntryId}
                journalEntryId={journalEntryId}
                status={status}
                entryRulerSpan={entryRulerSpan}
                createdAt={createdAt}
                createdBy={createdBy}
                title={title ? title : undefined}
                canActorEdit={canActorEdit}
                canActorSign={canActorSign}
                firstItemPreventingComplete={firstItemPreventingComplete}
                isCompletable={canCompleteJournalEntry}
                documentType={documentType}
                inlineGuide={inlineGuide}
                undoUntil={undoUntil}
                sections={sections}
              >
                {/* COMEBACK: We should show something if there is an error that caused
                 * sections to become null */}
                {sections?.map((section) => (
                  <JournalEntryBlock
                    key={section.id}
                    encounterId={encounterId}
                    canEdit={canActorEdit}
                    {...section}
                  >
                    <JournalEntryBlockNote
                      journalEntryId={journalEntryId}
                      ownedId={createdBy.id}
                      isEditable={canEditBlock(canActorEdit, status)}
                      journalTemplates={contentTemplates}
                      isCompleted={status === JournalEntryStatus.Completed}
                      onAddAttachment={() => setJournalEntryId(journalEntryId)}
                      onAddBillingItem={handleAddBillingItem}
                      {...section}
                    />
                  </JournalEntryBlock>
                ))}

                {(journalEntrySupplements.length > 0 ||
                  status !== JournalEntryStatus.Completed) && (
                  <SupplementsWrapper>
                    {journalEntrySupplements.map((block) => {
                      switch (block.__typename) {
                        case "ClinicalCoding":
                          return (
                            <ClinicalCodingCard
                              key={block.id}
                              {...block}
                              entryStatus={status}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                              closedAt={block.closedAt}
                            />
                          )
                        case "DrugPrescription":
                          return (
                            <JournalEntryBlockDrugPrescription
                              key={block.id}
                              {...block}
                              languageId={LanguageId.Is}
                              canEdit={canActorEdit}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                            />
                          )
                        case "MedicalCertificate":
                          return block.certType === "Absence" ? (
                            <MedicalCertificateSupplement
                              key={block.id}
                              {...block}
                              languageId={LanguageId.Is}
                              canEdit={canActorEdit}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                            />
                          ) : (
                            <JournalEntryFreeText
                              key={block.id}
                              {...block}
                              languageId={LanguageId.Is}
                              canEdit={canActorEdit}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                            />
                          )
                        case "OutboundDoctorsLetter":
                          return (
                            <OutboundMessage
                              key={block.id}
                              {...block}
                              messageType={ExternalMessage.DoctorLetter}
                              canEdit={canActorEdit}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                            />
                          )
                        case "OutboundReferral":
                          return (
                            <OutboundMessage
                              key={block.id}
                              {...block}
                              messageType={ExternalMessage.Referral}
                              canEdit={canActorEdit}
                              onDelete={onDelete(
                                canActorEdit,
                                status,
                                block.id
                              )}
                            />
                          )
                        default:
                          return null
                      }
                    })}

                    {canActorEdit && status === JournalEntryStatus.Draft && (
                      <AddSupplements
                        inlineButton={
                          journalEntrySupplements.filter(
                            (journalEntrySupplement) =>
                              !isTypename("JournalEntryAttachment")(
                                journalEntrySupplement
                              )
                          ).length > 0
                        }
                        onSelectAttachment={() =>
                          setJournalEntryId(journalEntryId)
                        }
                        onSelectBilling={handleAddBillingItem}
                      />
                    )}
                  </SupplementsWrapper>
                )}

                {hasAttachments && (
                  <Center>
                    <JournalAttachments
                      attachments={attachments}
                      journalEntryId={journalEntryId}
                      canUpload={
                        status === JournalEntryStatus.Completed || !canActorEdit
                      }
                    />
                  </Center>
                )}
              </JournalEntry>
            )
          }
        )}

        <Dialog
          isOpen={blockToDelete !== null}
          onClose={() => setBlockToDelete(null)}
          title={t("doDeleteConfirmation")}
          actions={
            <>
              <Button
                onClick={() => {
                  setBlockToDelete(null)
                }}
              >
                {t("cancel")}
              </Button>
              <Button
                status="error"
                onClick={() =>
                  blockToDelete && handleDeleteBlock(blockToDelete)
                }
              >
                {t("doDelete")}
              </Button>
            </>
          }
        ></Dialog>

        {!!hiddenJournalEntriesCount && (
          <Center>
            <Panel status="info">
              {t(
                hiddenJournalEntriesCount > 1
                  ? "routes:interventionPeriod.hiddenEntry_plural"
                  : "routes:interventionPeriod.hiddenEntry",
                {
                  count: hiddenJournalEntriesCount,
                }
              )}
            </Panel>
          </Center>
        )}

        <UploadAttachment
          onClose={async () => {
            setJournalEntryId(null)
          }}
          journalEntryId={journalEntryId}
        />
      </Restricted>
    </Encounter>
  )
}

const SubjectJournalWithoutPermission = () => {
  const { subjectId } = useParams<{ subjectId: string }>()
  if (!subjectId) throw new Error("No subjectId")

  const { t } = useTranslation()
  const { hasPermission } = usePermissions()

  const [showAddEncounterForm, setShowAddEncounterForm] = useState(false)

  const { data, error } = useGetSubjectJournalQuery({
    variables: { subjectId },
  })
  const [setSelectedSubjectId] = useCreateSubjectInteractionMutation({
    variables: { subjectId },
  })

  const [isCreatingInterventionPeriod, setIsCreatingInterventionPeriod] =
    useState(false)

  const [interventionPeriodId, setInterventionPeriodId] = useState<
    string | null
  >(null)

  const { data: journalTemplatesData } = useGetJournalTemplatesQuery({
    variables: {
      filter: {},
    },
  })

  const journalTemplates = journalTemplatesData?.journalTemplates || []

  const documentTemplates = useMemo(
    () =>
      journalTemplates.filter(
        (t) =>
          t.templateType === JournalTemplateType.DocumentTemplate ||
          t.templateType === JournalTemplateType.ExtendedDocumentTemplate
      ),
    [journalTemplates]
  )

  const contentTemplates = useMemo(
    () =>
      journalTemplates.filter(
        (t) => t.templateType === JournalTemplateType.InlineTemplate
      ),
    [journalTemplates]
  )

  const [searchValue, setSearchValue] = useState("")

  const deferredSearchValue = useDeferredValue(searchValue)

  useEffect(() => {
    setSelectedSubjectId()
  }, [subjectId])

  const handleInterventionPeriodCreated = useCallback(
    () => setIsCreatingInterventionPeriod(false),
    []
  )

  const interventionPeriodsData = data?.subjectJournal.interventionPeriods || []
  const unlinkedJournalData = data?.subjectJournal.unlinkedJournalData || []
  const unlinkedEncountersData = useMemo(
    () => unlinkedJournalData.filter(isTypename("Encounter")),
    [unlinkedJournalData]
  )

  const hasNoContent =
    interventionPeriodsData.length === 0 && unlinkedJournalData.length === 0

  const interventionPeriods = useMemo(
    () =>
      searchInterventionPeriods(interventionPeriodsData, deferredSearchValue),
    [interventionPeriodsData, deferredSearchValue]
  )

  const hiddenInterventionPeriodsCount =
    interventionPeriodsData.length - interventionPeriods.length

  const unlinkedSearchedJournalData = useMemo(
    () => searchEncounters(unlinkedJournalData, deferredSearchValue),
    [unlinkedJournalData, deferredSearchValue]
  )

  const hasNoMatches =
    interventionPeriods.length === 0 && unlinkedSearchedJournalData.length === 0

  if (error && error.message?.includes("Unauthorized")) {
    return <UnauthorizedPage />
  }

  const subject = data?.subjectJournal?.subject

  if (!subject) return null

  const focusedItemId = data?.subjectJournal?.focusedItemId

  const members = subject.organisation?.members || []

  const journalDataIds = [
    ...unlinkedJournalData.map((journalData) => journalData.id),
    ...interventionPeriods.flatMap((interventionPeriod) =>
      interventionPeriod.journalData.map((journalData) => journalData.id)
    ),
  ]

  const hideContent = hasNoContent || hasNoMatches

  const handleClose = () => {
    setShowAddEncounterForm(false)
    if (interventionPeriodId) setInterventionPeriodId(null)
  }

  const encounters = interventionPeriods.flatMap((ip) =>
    ip.journalData.filter(isTypename("Encounter"))
  )

  return (
    <>
      <SubjectOverviewBar
        subjectId={subjectId}
        encounterId={
          subject.encounters.length ? subject.encounters[0].id : undefined
        }
        age={subject.age}
        birthDate={subject.birthDate}
        genderId={subject.gender}
        locationId={subject.location?.id}
        dietaryAllowanceId={subject.subjectHealthProfile?.dietaryAllowanceId}
        healthProfileId={subject.subjectHealthProfile?.id}
        members={members}
        onAddEncounter={() => setShowAddEncounterForm(true)}
      />

      <JournalDataInViewProvider journalDataIds={journalDataIds}>
        <JournalFocus
          encounters={[...unlinkedEncountersData, ...encounters]}
          focusedItemId={focusedItemId}
          subjectId={subjectId}
        >
          <div className={styles.wrap}>
            <JournalGrid rowGap>
              <InterventionPeriodNavigation
                unlinkedEncounters={unlinkedSearchedJournalData}
                interventionPeriods={interventionPeriods}
                onCreateInteractionPeriod={() =>
                  setIsCreatingInterventionPeriod(true)
                }
                onCreateNewEncounter={setInterventionPeriodId}
                onSearchChange={setSearchValue}
              />

              <EmptySubjectJournal
                hasNoContent={hasNoContent}
                hasNoMatches={hasNoMatches}
                onNewEncounterClick={() => setShowAddEncounterForm(true)}
              />

              {!hideContent && (
                <>
                  <div className={styles.interventionPeriod}>
                    {unlinkedSearchedJournalData.length > 0 && (
                      <JournalGrid
                        rowGap
                        className={styles.encountersWOInterventionPeriod}
                      >
                        {unlinkedSearchedJournalData.map((journalData) => (
                          <Fragment key={journalData.id}>
                            <InterventionPeriodHeader
                              isInboundData={true}
                              subjectId={subjectId}
                              {...(journalData.__typename === "Encounter" && {
                                encounterId: journalData.id,
                              })}
                              {...(journalData.__typename === "InboundData" && {
                                inboundDataId: journalData.id,
                              })}
                              interventionPeriods={interventionPeriods}
                            />

                            {journalData.__typename === "Encounter" ? (
                              <RenderEncounter
                                {...journalData}
                                documentTemplates={documentTemplates}
                                contentTemplates={contentTemplates}
                              />
                            ) : (
                              <InboundDataComp {...journalData} />
                            )}
                          </Fragment>
                        ))}
                      </JournalGrid>
                    )}
                    {interventionPeriods.map(
                      ({ id: interventionPeriodId, journalData, title }) => {
                        return (
                          <InterventionPeriod
                            title={title}
                            key={interventionPeriodId}
                            id={interventionPeriodId}
                            journalData={journalData.map((jd) => jd.id)}
                            subjectId={subjectId}
                          >
                            <Restricted
                              to={PermissionKey.SubjectJournalEncounterView}
                            >
                              {journalData.map((journalData) => {
                                if (journalData.__typename === "Encounter") {
                                  return (
                                    <RenderEncounter
                                      key={journalData.id}
                                      {...journalData}
                                      documentTemplates={documentTemplates}
                                      contentTemplates={contentTemplates}
                                    />
                                  )
                                } else {
                                  return (
                                    <InboundDataComp
                                      key={journalData.id}
                                      {...journalData}
                                    />
                                  )
                                }
                              })}
                            </Restricted>
                          </InterventionPeriod>
                        )
                      }
                    )}
                  </div>

                  {!!hiddenInterventionPeriodsCount && (
                    <Center>
                      <Panel status="info">
                        {t(
                          hiddenInterventionPeriodsCount > 1
                            ? "routes:interventionPeriod.hiddenInterventionPeriod_plural"
                            : "routes:interventionPeriod.hiddenInterventionPeriod",
                          {
                            count: hiddenInterventionPeriodsCount,
                          }
                        )}
                      </Panel>
                    </Center>
                  )}
                </>
              )}
            </JournalGrid>
          </div>
          <JournalFocusIndicator />
        </JournalFocus>
      </JournalDataInViewProvider>

      {isCreatingInterventionPeriod &&
        hasPermission(PermissionKey.SubjectJournalInterventionPeriodEdit) && (
          <NoInterventionPeriodFound
            subjectId={subjectId}
            onInterventionPeriodCreated={handleInterventionPeriodCreated}
            onCancel={() => setIsCreatingInterventionPeriod(false)}
            className={styles.noInterventionPeriodFound}
          />
        )}

      <Modal
        isOpen={interventionPeriodId !== null || showAddEncounterForm}
        contentClassName={styles.modal}
        onClose={handleClose}
        allowOverflow
      >
        <CreateEncounter
          subjectId={subjectId}
          showAddEncounterForm={true}
          onAddEncounter={handleClose}
          onCancel={handleClose}
          members={members}
          interventionPeriods={interventionPeriodsData}
          defaultInterventionPeriodId={interventionPeriodId || undefined}
        />
      </Modal>
    </>
  )
}

const SubjectJournal = () => {
  return (
    <Restricted
      to={PermissionKey.SubjectJournalView}
      fallback={<UnauthorizedPage />}
    >
      <SubjectJournalWithoutPermission />
    </Restricted>
  )
}

export default SubjectJournal
