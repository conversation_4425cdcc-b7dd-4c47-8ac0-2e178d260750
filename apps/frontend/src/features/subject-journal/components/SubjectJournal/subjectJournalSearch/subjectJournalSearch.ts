import { matchSorter } from "match-sorter"

import { InterventionPeriodFragmentFragment } from "generated/graphql"

export const searchInterventionPeriods = (
  interventionPeriods: InterventionPeriodFragmentFragment[],
  searchValue: string
) => {
  if (!searchValue) return interventionPeriods

  const ipMatches = matchSorter(interventionPeriods, searchValue, {
    keys: ["title"],
  })
  const ipMatchIds = ipMatches.map((ip) => ip.id)

  // Flat list of encounters that haven't been matched yet
  const encounters = interventionPeriods
    .filter((ip) => !ipMatchIds.includes(ip.id))
    .flatMap((ip) =>
      ip.journalData.map((e) => ({ ...e, interventionPeriod: ip }))
    )

  const encounterMatches = searchEncounters(encounters, searchValue)

  const ips = [
    ...new Set(encounterMatches.map((e) => e.interventionPeriod)),
  ].map((ip) => ({
    ...ip,
    journalData: encounterMatches.filter(
      (e) => e.interventionPeriod.id === ip.id
    ),
  }))

  return ipMatches.concat(ips)
}

export function searchEncounters<
  T extends InterventionPeriodFragmentFragment["journalData"],
>(encounters: T, searchValue: string) {
  if (!searchValue) return encounters

  return matchSorter(encounters, searchValue, {
    keys: [
      // Encounters search keys
      "reason",
      "journalEntries.*.title",
      "journalEntries.*.sections.*.title",
      {
        key: "journalEntries.*.sections.*.content",
        threshold: matchSorter.rankings.CONTAINS,
      },
      // Inbound search keys
      "entries.*.inboundType",
      "sender.providerName",
      "sender.departmentName",
      "sender.provider.name",
      {
        key: "entries.*.sections.*.content",
        threshold: matchSorter.rankings.CONTAINS,
      },
    ],
  }) as T
}
