import { GetSubjectJournalQuery } from "generated/graphql"

export type InterventionPeriods =
  GetSubjectJournalQuery["subjectJournal"]["interventionPeriods"]
export type InterventionPeriod = InterventionPeriods[0]

export type JournalData = InterventionPeriod["journalData"]

export type Encounters = Extract<
  InterventionPeriod["journalData"][number],
  { __typename: "Encounter" }
>[]
export type Encounter = Encounters[0]

export type JournalEntries = Encounter["journalEntries"]
export type JournalEntry = JournalEntries[0]

export type InboundDataList = Extract<
  InterventionPeriod["journalData"][number],
  { __typename: "InboundData" }
>[]
export type InboundData = InboundDataList[0]

export type InboundEntries = InboundData["entries"]
export type InboundEntry = InboundEntries[0]
