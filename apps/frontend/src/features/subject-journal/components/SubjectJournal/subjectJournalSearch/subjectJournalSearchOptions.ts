import Fuse from "fuse.js"

import {
  Encounter,
  InboundData,
  InboundEntry,
  InterventionPeriod,
  JournalEntry,
} from "./types"

const threshold = 0.3

export const interventionPeriodOptions: Fuse.IFuseOptions<InterventionPeriod> =
  {
    keys: ["title"],
    threshold,
  }

export const encounterOptions: Fuse.IFuseOptions<Encounter> = {
  keys: ["reason"],
  threshold,
}

export const journalEntriesOptions: Fuse.IFuseOptions<JournalEntry> = {
  keys: [
    { name: "title", weight: 0.7 },
    { name: "sections.title", weight: 0.2 },
    { name: "sections.strippedContent", weight: 0.1 },
  ],
  threshold,
  distance: 100000,
}

export const inboundDataOptions: Fuse.IFuseOptions<InboundData> = {
  keys: ["inboundType"],
  threshold,
}

export const inboundEntryOptions: Fuse.IFuseOptions<InboundEntry> = {
  keys: [
    { name: "title", weight: 0.7 },
    { name: "sections.strippedContent", weight: 0.3 },
  ],
  threshold,
  distance: 100000,
}
