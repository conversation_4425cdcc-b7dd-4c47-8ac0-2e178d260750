import { stripHtml } from "utils/stripHtml"

import { InboundEntries, JournalEntries } from "./types"

export const prepareForSearchJournalEntry = (
  journalEntries: JournalEntries
) => {
  return journalEntries.map((journalEntry) => {
    return {
      ...journalEntry,
      sections: (journalEntry.sections || []).map((section) => {
        return {
          ...section,
          strippedContent: stripHtml(section.content),
          content: section.content,
        }
      }),
    }
  })
}
export const prepareForSearchInboundEntry = (
  inboundEntries: InboundEntries
) => {
  return inboundEntries.map((inboundEntry) => {
    return {
      ...inboundEntry,
      sections: inboundEntry.sections.map((section) => {
        return {
          ...section,
          strippedContent: stripHtml(section.content),
          content: section.content,
        }
      }),
    }
  })
}
