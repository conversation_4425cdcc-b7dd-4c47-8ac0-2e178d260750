import { useTranslation } from "react-i18next"
import { <PERSON> } from "react-router-dom"

import ComputerIllustration from "@leviosa/assets/illustrations/computer2.svg?react"

import Icon from "components/Icon/Icon"
import { useGetCalendarPathObject } from "features/calendar/utils/navigateCalendar"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, Text } from "ui"

import styles from "./EmptySubjectJournal.module.css"

type EmptySubjectJournalProps = {
  hasNoContent: boolean
  hasNoMatches: boolean
  onNewEncounterClick: () => void
}

export const EmptySubjectJournal = ({
  hasNoMatches,
  hasNoContent,
  onNewEncounterClick,
}: EmptySubjectJournalProps) => {
  const { t } = useTranslation()

  const getCalendarPath = useGetCalendarPathObject()

  if (hasNoContent) {
    return (
      <div className={styles.emptySubjectJournal}>
        <div className={styles.emptySubjectJournalContent}>
          <ComputerIllustration />
          <div className={styles.contentInfo}>
            <Heading size="large">{t("Looking a little empty")}</Heading>
            <Text>{t("You do not have records for this subject yet")}</Text>
          </div>
          <div className={styles.buttons}>
            <Button as={Link} to={getCalendarPath(RouteStrings.calendar)}>
              {t("Book appointment")}
            </Button>
            <Button
              variant="filled"
              onClick={onNewEncounterClick}
              icon={<Icon name={"add-line"} />}
            >
              {t("New encounter")}
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (hasNoMatches) {
    return (
      <div className={styles.emptySubjectJournal}>
        <div className={styles.emptySubjectJournalContent}>
          <ComputerIllustration />
          <div className={styles.contentInfo}>
            <Heading size="large">{t("No results found")}</Heading>
            <Text>{t("We couldn't find any results for your search")}</Text>
          </div>
        </div>
      </div>
    )
  }

  return null
}
