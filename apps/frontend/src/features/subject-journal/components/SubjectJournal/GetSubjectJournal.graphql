query GetSubjectJournal($subjectId: UUID!) {
  subjectJournal(subjectId: $subjectId) {
    id
    focusedItemId
    subject {
      id
      age
      birthDate
      name
      gender

      location {
        id
      }
      subjectHealthProfile {
        id
        dietaryAllowanceId
      }
      organisation {
        id
        teams {
          id
        }

        members {
          ...ProviderInfoFragment
        }
      }

      encounters(statuses: IN_PROGRESS) {
        id
      }
    }
    unlinkedJournalData: journalData(interventionPeriodLinkStatus: UNLINKED) {
      ... on Encounter {
        ...EncounterFragment
      }
      ... on InboundData {
        ...InboundDataFragment
      }
    }
    interventionPeriods {
      ...InterventionPeriodFragment
    }
  }
}

# In reality, sets single interaction i.e. equals "set Subject selection"
mutation CreateSubjectInteraction($subjectId: UUID!) {
  createSubjectInteraction(
    input: { subjectId: $subjectId, interaction: SUBJECT_JOURNAL }
  ) {
    id

    provider {
      id
      selectedSubjects: subjectInteractions(limit: 1, groupSubjects: true) {
        ...SubjectInteractionSelectionFragment
      }
      recentSubjectInteractions: subjectInteractions(
        limit: 15
        groupSubjects: true
      ) {
        ...SubjectInteractionSelectionFragment
      }

      lastSubjectInteraction {
        id
        subject {
          id
          name
          clinicalCodings {
            id
            criticalType
            closedAt
          }
        }
      }
    }
  }
}
