import { useState } from "react"

import { View } from "features/user-management/UserManagement"
import { Loading } from "ui"

import { ActivationStatus, useUserManagementQuery } from "generated/graphql"

import { ProviderManage } from "../ProviderManage/ProviderManage"
import { UserManagementTable } from "../UserManagementTable/UserManagementTable"

type Props = {
  view: View
}

export const ActiveInactiveProviders = ({ view }: Props) => {
  const [selectedProviderId, setSelectedProviderId] = useState<string | null>(
    null
  )

  const { data, loading } = useUserManagementQuery()
  if (loading) return <Loading large />

  if (!data) return null

  const providersForTable = data.providers.filter(({ activationStatus }) =>
    view === View.Activated
      ? activationStatus === ActivationStatus.Active
      : activationStatus === ActivationStatus.Deactivated
  )

  return (
    <>
      <UserManagementTable
        setSelectedProviderId={setSelectedProviderId}
        providers={providersForTable}
        view={view}
      />

      <ProviderManage
        /* Using key to reset state between selections */
        key={selectedProviderId}
        selectedProviderId={selectedProviderId}
        data={data}
        onClose={() => setSelectedProviderId(null)}
      />
    </>
  )
}
