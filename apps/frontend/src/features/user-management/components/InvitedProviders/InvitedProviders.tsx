import { useSuspenseQuery } from "@apollo/client"

import { View } from "features/user-management/UserManagement"

import {
  InvitationStatus,
  InvitedProvidersDocument,
  InvitedProvidersQuery,
  InvitedProvidersQueryVariables,
} from "generated/graphql"

import { UserManagementTable } from "../UserManagementTable/UserManagementTable"

type Props = {
  view: View
}

export default function InvitedProviders({ view }: Props) {
  const { data } = useSuspenseQuery<
    InvitedProvidersQuery,
    InvitedProvidersQueryVariables
  >(InvitedProvidersDocument, {
    variables: { filter: { status: InvitationStatus.Pending } },
  })

  const invitedProviders = (data?.invitedProviders || []).map((p) => ({
    ...p,
    activationStatus: undefined,
    teams: [],
  }))

  return <UserManagementTable providers={invitedProviders} view={view} />
}
