import Icon from "components/Icon/Icon"
import { Button } from "ui"

import { useReinviteProviderMutation } from "generated/graphql"

export type ResendInviteButtonProps = {
  id: string
}

export default function ResendInviteButton({ id }: ResendInviteButtonProps) {
  const [resendInvite, { loading, called, error }] =
    useReinviteProviderMutation({
      variables: { reInviteProviderId: id },
    })

  const isSent = called && !error && !loading
  return (
    <Button
      onClick={() => resendInvite()}
      icon={
        isSent ? (
          <Icon name="check-line" />
        ) : (
          <Icon name="restart-line" spin={loading} />
        )
      }
      aria-label="Resend Invite"
      status={isSent ? "success" : "default"}
    >
      {isSent ? "Sent" : ""}
    </Button>
  )
}
