import dayjs from "dayjs"
import { useTranslation } from "react-i18next"
import { Link } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { View } from "features/user-management/UserManagement"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { Button, Table, Text } from "ui"

import { InvitedProvidersQuery, UserManagementQuery } from "generated/graphql"

import ResendInviteButton from "../InvitedProviders/ResendInviteButton"
import styles from "./UserManagementTable.module.css"

type Props = {
  setSelectedProviderId?: (id: string) => void
  providers:
    | UserManagementQuery["providers"]
    | Array<
        InvitedProvidersQuery["invitedProviders"][number] & { teams: never[] }
      >
  view: View
}

export const UserManagementTable = ({
  setSelectedProviderId,
  providers,
  view,
}: Props) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")

  const sortedProviders = providers.toSorted((a, b) =>
    a.name.localeCompare(b.name)
  )

  return (
    <Table className={styles.table} spacing="narrow">
      <thead>
        <tr>
          <th>{t("provider")}</th>
          <th>{t("specialty")}</th>
          <th>{t("phone")}</th>
          <th>{t("teams")}</th>
          <th>{t("roles")}</th>
          <th>{view === View.Invited ? t("expirationDate") : ""}</th>
        </tr>
      </thead>
      <tbody>
        {sortedProviders.map((provider) => {
          const { id, name, email, phoneNumber, specialty, roles, teams } =
            provider

          const invitedProvider = provider.__typename === "InvitedProvider"

          return (
            <tr key={id}>
              <td>
                <td>
                  {invitedProvider ? (
                    <Text>
                      <span>{name}</span>
                    </Text>
                  ) : (
                    <Text
                      as={Link}
                      to={getRecordPagePath(RouteStrings.providerView, id)}
                    >
                      <span>{name}</span>
                    </Text>
                  )}

                  <Text size="small">{email}</Text>
                </td>
              </td>
              <td>{tEnum(`ProviderSpecialty.${specialty}`)}</td>
              <td>{phoneNumber}</td>
              <td className={styles.tdList}>
                ({teams.length}){" "}
                {teams.map((team, i) => (
                  <>
                    <Link
                      key={team.id}
                      to={getRecordPagePath(RouteStrings.team, team.id)}
                    >
                      {team.name}
                    </Link>
                    {i === teams.length - 1 ? "" : ", "}
                  </>
                ))}
              </td>
              <td className={styles.tdList}>
                {roles.map((role) => role.id).join(", ")}
              </td>
              <td>
                {provider.__typename === "InvitedProvider" ? (
                  <div className={styles.tdInvitation}>
                    {dayjs(provider.expiredAt).format("D MMM @HH:mm")}
                    <ResendInviteButton id={provider.id} />
                  </div>
                ) : setSelectedProviderId ? (
                  <Button
                    icon={<Icon name="settings-line" />}
                    variant="outline"
                    onClick={() => setSelectedProviderId(id)}
                  />
                ) : null}
              </td>
            </tr>
          )
        })}
      </tbody>
    </Table>
  )
}
