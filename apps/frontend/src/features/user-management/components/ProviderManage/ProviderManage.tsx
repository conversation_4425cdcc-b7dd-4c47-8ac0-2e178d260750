import { FormEvent, useReducer } from "react"
import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel, { PanelWithIcon } from "components/Panel/Panel"
import { Select } from "components/Select/Select"
import { Button, FormGrid, Heading, Input, Modal } from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"
import {
  rxGenericEmail,
  rxHumanName,
  rxIcelandicPhoneNumber,
} from "utils/inputValidators"

import {
  ActivationStatus,
  ProviderSpecialty,
  useActivateUserMutation,
  useDeactivateUserMutation,
  useGrantRolesToProviderMutation,
  useProviderUpdateMutation,
  useRevokeRolesFromProviderMutation,
  UserManagementQuery,
} from "generated/graphql"

import styles from "./ProviderManage.module.css"

type Props = {
  onClose: () => void
  data: UserManagementQuery
  selectedProviderId: string | null
}

type FormState = {
  name: string
  localValidationErrors: {
    NAME: string
    EMAIL: string
    PHONE: string
  }
  email: string
  phoneNumber: string
  doctorNumber: string | null
  // externalEhrId: string
  isInitial: boolean
}

const isPhysician = (specialty: ProviderSpecialty): boolean => {
  return [
    ProviderSpecialty.Physician,
    ProviderSpecialty.AttendingPhysician,
    ProviderSpecialty.ResidentPhysician,
    ProviderSpecialty.PrimaryCarePhysician,
  ].includes(specialty)
}
export const ProviderManage = ({
  onClose,
  data: { providers, roles: allRoles },
  selectedProviderId,
}: Props) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "userManagement",
  })
  const { t: tValidators } = useTranslation("enums", {
    keyPrefix: "inputValidators",
  })
  const {
    globalData: { actor },
  } = useGlobalState()

  const selectedProvider = providers.find(({ id }) => id === selectedProviderId)

  const mutationErrors: string[] = []

  const [addRoles, { loading: addLoading }] = useGrantRolesToProviderMutation({
    onError: (error) => mutationErrors.concat(error.message),
  })

  const [removeRoles, { loading: removeLoading }] =
    useRevokeRolesFromProviderMutation({
      onError: (error) => mutationErrors.concat(error.message),
    })

  const [setStatusActive] = useActivateUserMutation({
    onError: (error) => mutationErrors.concat(error.message),
  })

  const [setStatusInactive] = useDeactivateUserMutation({
    onError: (error) => mutationErrors.concat(error.message),
  })

  // If we always have a permission, we can just use that as the default value rather than this check but for now this is fine
  const rolesStore = useSelectStore<string[]>({
    defaultValue: (selectedProvider?.roles || []).map(({ id }) => id),
    focusLoop: "vertical",
  })

  if (selectedProvider === undefined) return null

  // Select is multi-select so can bring either added or removed roles, compared with currentRoles.
  // We need to calculate the difference and then add/remove the roles from the provider.
  const handleRolesChange = (selectedRoles: string[] | null) => {
    // const selectedRoles = rolesStore.getState().value

    const rolesToAdd = (selectedRoles || []).filter(
      (role) => !selectedProvider.roles.some(({ id }) => id === role)
    )
    if (rolesToAdd.length > 0)
      addRoles({
        variables: {
          input: {
            providerId: selectedProvider.id,
            roles: rolesToAdd,
          },
        },
      })
    else {
      const rolesToRemove = selectedProvider.roles
        .filter(({ id }) => !(selectedRoles || []).some((role) => role === id))
        .map(({ id }) => id)
      if (rolesToRemove.length > 0)
        removeRoles({
          variables: {
            input: {
              providerId: selectedProvider.id,
              roles: rolesToRemove,
            },
          },
        })
    }
  }

  const isActive = selectedProvider.activationStatus === ActivationStatus.Active
  const isRoleLoading = addLoading || removeLoading

  const specialtyStore = useSelectStore({
    defaultValue: selectedProvider.specialty,
  })

  const [
    updateProvider,
    { loading: loadingUpdateProvider, error: errorUpdateProvider },
  ] = useProviderUpdateMutation({
    onCompleted: () => setFormState({ isInitial: true }),
  })

  const [formState, setFormState] = useReducer<
    (p: FormState, a: Partial<FormState>) => FormState
  >((prev, next) => ({ ...prev, ...next, isInitial: false }), {
    name: selectedProvider.name,
    localValidationErrors: {
      NAME: "",
      EMAIL: "",
      PHONE: "",
    },
    email: selectedProvider.email,
    phoneNumber: selectedProvider.phoneNumber,
    doctorNumber: selectedProvider.doctorNumber,
    // COMEBACK externalEhrId should not be nullable
    // externalEhrId: selectedProvider.externalEhrId ?? "",
    isInitial: true,
  })

  const hasValidationErrors = Object.values(
    formState.localValidationErrors
  ).some((msg) => msg.length > 0)

  const handleSubmitProfileConfig = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (hasValidationErrors) return

    updateProvider({
      variables: {
        id: selectedProvider.id,
        input: {
          name: formState.name,
          email: formState.email,
          phoneNumber: formState.phoneNumber,
          doctorNumber: formState.doctorNumber || null,
          specialty: selectedSpecialty,
          // externalEhrId: formState.externalEhrId,
        },
      },
    })
  }

  const checkValidations = () => {
    const validations = {
      NAME: !rxHumanName.test(formState.name) ? tValidators("rxHumanName") : "",
      PHONE: !rxIcelandicPhoneNumber.test(formState.phoneNumber)
        ? tValidators("rxIcelandicPhoneNumber")
        : "",
      EMAIL: !rxGenericEmail.test(formState.email)
        ? tValidators("rxGenericEmail")
        : "",
    }

    setFormState({
      localValidationErrors: {
        ...formState.localValidationErrors,
        ...validations,
      },
    })
  }

  const handleChangeFormState = (keyValue: Partial<FormState>) =>
    setFormState({
      ...keyValue,
    })

  // Convenience function to set status and message for Input component
  const validationEffects = (
    key: keyof typeof formState.localValidationErrors
  ) => ({
    status:
      formState.localValidationErrors[key].length > 0
        ? ("error" as const)
        : ("default" as const),
    message: formState.localValidationErrors[key],
  })

  const selectedSpecialty = specialtyStore.getState().value as ProviderSpecialty

  return (
    <Modal
      isOpen
      onClose={onClose}
      closeOnClickOutside
      title={selectedProvider.name}
      footer={
        <div className={styles.footer}>
          {mutationErrors.length > 0 && (
            <Panel status="error" children={mutationErrors.join()} />
          )}
          <Button onClick={onClose} variant="filled">
            Done
          </Button>
        </div>
      }
    >
      <div className={styles.modalInner}>
        <div>
          <FormGrid
            onSubmit={handleSubmitProfileConfig}
            onBlur={checkValidations}
          >
            <Input
              value={formState.name}
              label={t("Name")}
              {...validationEffects("NAME")}
              onChange={({ currentTarget }) =>
                handleChangeFormState({ name: currentTarget.value })
              }
            />

            <Input
              type="email"
              label={t("Email")}
              {...validationEffects("EMAIL")}
              value={formState.email}
              onChange={({ currentTarget }) =>
                handleChangeFormState({ email: currentTarget.value })
              }
            />
            <Input
              type="tel"
              label={t("phoneNumber")}
              {...validationEffects("PHONE")}
              value={formState.phoneNumber}
              onChange={({ currentTarget }) =>
                handleChangeFormState({ phoneNumber: currentTarget.value })
              }
            />

            <Select
              label={t("specialty")}
              options={Object.values(ProviderSpecialty).map((specialty) => ({
                label: t(`enums:ProviderSpecialty.${specialty}`),
                value: specialty,
              }))}
              selectStore={specialtyStore}
            />

            {isPhysician(selectedSpecialty) && (
              <Input
                type="string"
                label={t("doctorNumber")}
                value={formState.doctorNumber || ""}
                onChange={({ currentTarget }) =>
                  handleChangeFormState({ doctorNumber: currentTarget.value })
                }
              />
            )}

            {/* {config.leviosaKindId === "LITE" && (
                <Input
                  label={t("externalEhrId")}
                  value={formState.externalEhrId ?? ""}
                  onChange={({ currentTarget }) =>
                    handleChangeFormState({
                      externalEhrId: currentTarget.value,
                    })
                  }
                />
              )} */}

            <FormFooter>
              <Button
                type="submit"
                disabled={loadingUpdateProvider}
                variant="filled"
                status={hasValidationErrors ? "error" : "default"}
              >
                {t("submit")}
              </Button>
            </FormFooter>
            {errorUpdateProvider && (
              <Panel status="error">{errorUpdateProvider.message}</Panel>
            )}
          </FormGrid>
        </div>

        <hr />
        <Select<string[]>
          label="Access Roles"
          selectStore={rolesStore}
          // minValues={1}
          onSelectChange={(selectedRole) => handleRolesChange(selectedRole)}
          icon={isRoleLoading && <Icon name="loader-4-line" spin />}
          options={allRoles.map(({ id }) => ({
            value: id,
            label: id,
            selected: selectedProvider.roles.some((role) => role.id === id),
          }))}
        />

        <hr />

        <Heading>{tRoutes("activationStatus")}</Heading>
        <div className={styles.activation}>
          <PanelWithIcon status="info" iconName="information-line">
            {tRoutes("activationStatusInfo")}
          </PanelWithIcon>
          <Button
            variant="outline"
            disabled={actor.id === selectedProvider.id}
            onClick={() => {
              if (isActive)
                setStatusInactive({
                  variables: { deactivateUserId: selectedProvider.id },
                })
              else
                setStatusActive({
                  variables: { activateUserId: selectedProvider.id },
                })
            }}
            status={isActive ? "error" : "default"}
            size="large"
          >
            {isActive ? "Deactivate" : "Activate"}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
