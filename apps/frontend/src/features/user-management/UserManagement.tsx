import { useTranslation } from "react-i18next"
import { generatePath, useParams, NavLink } from "react-router-dom"

import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { RouteStrings } from "routes/RouteStrings"
import { Button, ButtonGroup, Grid, Heading } from "ui"

import { PermissionKey } from "generated/graphql"

import styles from "./UserManagement.module.css"
import { ActiveInactiveProviders } from "./components/ActiveInactiveProviders/ActiveInactiveProviders"
import InvitedProviders from "./components/InvitedProviders/InvitedProviders"

export enum View {
  Activated = "active",
  Deactivated = "deactivated",
  Invited = "invited",
}

const UserManagement = () => {
  const { view = View.Activated } = useParams<{ view: View }>()
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "UserManagementView",
  })

  const { hasPermission } = usePermissions()

  const canInvite = hasPermission(PermissionKey.AccountsProviderInvite)
  return (
    /* COMEBACK this should be restricted in PowerMenu not here */
    <Restricted
      to={PermissionKey.AccountsProviderAdminEdit}
      fallback={<UnauthorizedPage />}
    >
      <Grid rowGap={4}>
        <Heading as="h2" size="display" className={styles.heading}>
          Providers
        </Heading>
        <ButtonGroup className={styles.buttonGroup}>
          <Button
            as={NavLink}
            to={generatePath(RouteStrings.userManagement, {
              view: View.Activated,
            })}
          >
            {tEnum(View.Activated)}
          </Button>

          <Button
            as={NavLink}
            to={generatePath(RouteStrings.userManagement, {
              view: View.Deactivated,
            })}
          >
            {tEnum(View.Deactivated)}
          </Button>
          {canInvite && (
            <Button
              as={NavLink}
              to={generatePath(RouteStrings.userManagement, {
                view: View.Invited,
              })}
            >
              {tEnum(View.Invited)}
            </Button>
          )}
        </ButtonGroup>
        {(view === View.Activated || view === View.Deactivated) && (
          <ActiveInactiveProviders view={view} />
        )}

        {canInvite && view === View.Invited && <InvitedProviders view={view} />}
      </Grid>
    </Restricted>
  )
}

export default UserManagement
