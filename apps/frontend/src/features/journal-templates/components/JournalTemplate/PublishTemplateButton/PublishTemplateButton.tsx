import { useTranslation } from "react-i18next"

import { <PERSON>u, <PERSON>uButton, MenuItem, MenuProvider } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { Button, ButtonText, notification, TextWithIcon } from "ui"

import {
  JournalTemplateType,
  TemplateAccessScope,
  usePublishJournalTemplateMutation,
  useSetJournalTemplateAccessScopeMutation,
} from "generated/graphql"

import { getTemplateAccessScopeIcon } from "../../TemplateAccessScopeMenu/TemplateAccessScopeMenu"

type PublishTemplateButtonProps = {
  id: string
  templateType: JournalTemplateType
  canEdit: boolean
}

export const PublishTemplateButton = ({
  id,
  templateType,
  canEdit,
}: PublishTemplateButtonProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })
  const [publishJournalTemplate] = usePublishJournalTemplateMutation()
  const [setAccessScope] = useSetJournalTemplateAccessScopeMutation()

  const handlePublishTemplate = (accessScope?: TemplateAccessScope) => {
    publishJournalTemplate({
      variables: {
        id,
      },
      onCompleted: () => {
        notification.create({
          message: tRoutes("publishSuccess"),
          status: "success",
        })

        // Access scope is only used for extended document templates,
        // and it is to determine who can view and edit the template
        if (!accessScope) return

        setAccessScope({
          variables: {
            id,
            accessScope,
          },
          onError: () => {
            notification.create({
              message: tRoutes("publishAccessScopeError"),
              status: "error",
            })
          },
        })
      },
      onError: (e) => {
        const errorMsg = typeof e?.message === "string" ? e.message : ""
        let parsedMsg
        if (/must have at least one section/.test(errorMsg)) {
          parsedMsg = tRoutes("publishErrorNoSection")
        } else if (/All sections must have a title/.test(errorMsg)) {
          parsedMsg = tRoutes("publishErrorNoTitle")
        }

        notification.create({
          message: tRoutes("publishError", { error: parsedMsg }),
          status: "error",
        })
      },
    })
  }

  const menuStore = useMenuStore({})

  const menuOptions = Object.values(TemplateAccessScope).map((accessScope) => {
    return {
      label: tRoutes(`publishTemplate.${accessScope}`),
      value: accessScope,
      onSelect: () => {
        handlePublishTemplate(accessScope)
      },
      icon: getTemplateAccessScopeIcon(accessScope),
    }
  })

  const isExtendedDocumentTemplate =
    templateType === JournalTemplateType.ExtendedDocumentTemplate

  if (isExtendedDocumentTemplate) {
    return (
      <MenuProvider store={menuStore}>
        <MenuButton
          store={menuStore}
          iconEnd={<Icon name="arrow-down-s-line" />}
          variant="filled"
          size="large"
        >
          {tRoutes("publish")}
        </MenuButton>
        <Menu>
          {menuOptions.map((option) => (
            <MenuItem key={option.value} onClick={option.onSelect}>
              {option.icon ? (
                <TextWithIcon iconName={option.icon} weight="bold">
                  {option.label}
                </TextWithIcon>
              ) : (
                <ButtonText> {option.label}</ButtonText>
              )}
            </MenuItem>
          ))}
        </Menu>
      </MenuProvider>
    )
  }

  return (
    <Button
      variant="filled"
      disabled={!canEdit}
      size="large"
      onClick={() => handlePublishTemplate()}
    >
      {tRoutes("publish")}
    </Button>
  )
}
