import { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { InlineGuideInput } from "components/InlineGuide/InlineGuideInput"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import { EditFieldForm } from "features/dashboard/components/EditFieldForm/EditFieldForm"
import EditableText from "features/subject-journal/components/EditableText/EditableText"
import { RouteStrings } from "routes/RouteStrings"
import {
  Button,
  Heading,
  Input,
  Label,
  Modal,
  notification,
  Tag,
  Text,
  TextWithIcon,
} from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  JournalTemplateType,
  namedOperations,
  Permission<PERSON>ey,
  useAddSectionToJournalEntryTemplateMutation,
  useEditJournalTemplateMutation,
  useExtendJournalTemplateMutation,
} from "generated/graphql"

import styles from "./DocumentTemplate.module.css"
import baseStyles from "./JournalTemplate.module.css"
import { JournalTemplateEditor } from "./JournalTemplateEditor"

type DocumentTemplateProps = {
  template: JournalTemplateFragmentFragment
}

export const DocumentTemplate = ({ template }: DocumentTemplateProps) => {
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })

  const { hasPermission } = usePermissions()
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const navigate = useNavigate()

  const [updateJournalTemplate] = useEditJournalTemplateMutation()
  const [addSectionToTemplate] = useAddSectionToJournalEntryTemplateMutation()
  const [extendDocumentTemplate] = useExtendJournalTemplateMutation()

  const [addSectionModalOpen, setAddSectionModalOpen] = useState(false)

  const noTemplate =
    !template ||
    template.status === JournalTemplateStatus.Archived ||
    !template.documentType

  // Get the latest updated date from the template and its sections
  const lastUpdatedAt = useMemo(() => {
    if (noTemplate) return
    const lastUpdatedAt = new Date(template.updatedAt)
    const sectionsUpdatedAt = template.sections.map(
      (section) => new Date(section.updatedAt)
    )

    const allDates = [lastUpdatedAt, ...sectionsUpdatedAt]
    return new Date(Math.max(...allDates.map((date) => date.getTime())))
  }, [template])

  if (noTemplate) {
    return <div className={baseStyles.wrap} />
  }

  const canEdit = hasPermission(
    PermissionKey.SubjectJournalDocumentTemplateEdit
  )

  const dateCreated = new Date(template.createdAt)

  const createdAt = tRoutes("createdAt", {
    date: dateFormat(dateCreated),
    time: timeFormat(dateCreated),
  })

  const updatedAt = lastUpdatedAt
    ? tRoutes("updatedAt", {
        date: dateFormat(lastUpdatedAt),
        time: timeFormat(lastUpdatedAt),
      })
    : ""

  return (
    <>
      <header className={baseStyles.header}>
        <div className={baseStyles.topHeading}>
          <Tag
            className={baseStyles.documentType}
            color="blue"
            size="small"
            weight="bold"
            test-id="template-document-type"
          >
            {tEnum(`DocumentType.${template.documentType}.label`)}
          </Tag>

          <div className={baseStyles.tagsContainer}>
            <Restricted to={PermissionKey.SubjectJournalDocumentTemplateEdit}>
              <Tag
                size="small"
                color={
                  template.status === JournalTemplateStatus.Published
                    ? "levGreen"
                    : "gray"
                }
                weight="bold"
                className={baseStyles.status}
                test-id="journal-template-status"
              >
                {tEnum(`JournalTemplateStatus.${template.status}`)}
              </Tag>
            </Restricted>
            {template.status === JournalTemplateStatus.Published && (
              <Button
                className={styles.customiseButton}
                size="small"
                icon={<Icon name="edit-line" />}
                onClick={() => {
                  extendDocumentTemplate({
                    variables: {
                      templateId: template.id,
                    },
                    refetchQueries: [namedOperations.Query.GetJournalTemplates],
                    onCompleted: (data) => {
                      const templateId = data?.extendJournalTemplate?.id
                      if (!templateId) return

                      navigate(
                        generatePath(RouteStrings.journalTemplates, {
                          templateType:
                            JournalTemplateType.ExtendedDocumentTemplate.toLowerCase(),
                          templateId,
                        })
                      )
                    },
                    onError: () => {
                      notification.create({
                        message: tRoutes("customiseFailed"),
                        status: "error",
                      })
                    },
                  })
                }}
              >
                {tRoutes("customise")}
              </Button>
            )}
          </div>
        </div>
        <div className={baseStyles.heading}>
          <EditableText
            title={template.name}
            textSize="large"
            inputSize="small"
            onSave={(input) => {
              if (!input) {
                notification.create({
                  message: tRoutes("titleRequired"),
                  status: "error",
                })
                return
              }
              updateJournalTemplate({
                variables: {
                  input: {
                    id: template.id,
                    name: input,
                  },
                },
              })
            }}
            isEditable={canEdit}
            wrapperElement={Heading}
            fallbackText={tRoutes("addTitle")}
            testid="journal-template-title"
          />
        </div>
        <div className={baseStyles.subHeading}>
          <div className={baseStyles.creator}>
            <Label>{template.createdBy.name} •</Label>
            <Label secondary>
              {tEnum(`ProviderSpecialty.${template.createdBy.specialty}`)}
            </Label>
          </div>
          <TextWithIcon
            as={Label}
            iconName="calendar-2-line"
            secondary
            title={createdAt}
            size="small"
            className={baseStyles.updatedAt}
          >
            {updatedAt}
          </TextWithIcon>
        </div>
        <div>
          {canEdit ? (
            <EditFieldForm
              name={"template-description"}
              value={template.description || ""}
              label={tRoutes("description")}
              hideLabel
              placeholder={tRoutes("addDescription")}
              onSubmit={(input) => {
                updateJournalTemplate({
                  variables: {
                    input: {
                      id: template.id,
                      description: input,
                    },
                  },
                })
              }}
              testid="template-description"
            />
          ) : (
            <Text>{template.description}</Text>
          )}
        </div>
        <InlineGuideInput
          formHeading={tRoutes("addTemplateInlineGuide")}
          fallbackText={tRoutes("clickToAddTemplateInlineGuide")}
          canEdit={canEdit}
          onChange={(value) => {
            updateJournalTemplate({
              variables: {
                input: {
                  id: template.id,
                  inlineGuide: value,
                },
              },
            })
          }}
          value={template.inlineGuide || ""}
        />
      </header>
      <JournalTemplateEditor template={template} canEdit={canEdit} />
      {canEdit && (
        <div className={styles.addSectionWrapper}>
          <Button
            disabled={!canEdit}
            icon={<Icon name="add-box-line" />}
            className={styles.addSectionButton}
            variant="clear"
            onClick={() => {
              // If the template is published, open the modal to add a section so we don't
              // add a section without a title to a published template
              if (template.status === JournalTemplateStatus.Published) {
                setAddSectionModalOpen(true)
                return
              }
              addSectionToTemplate({
                variables: {
                  input: {
                    content: "",
                    isResolutionNote: false,
                    journalTemplateId: template.id,
                  },
                },
              })
            }}
          >
            {tRoutes("addSection")}
          </Button>
          <hr className={styles.ruler} />
        </div>
      )}
      <Modal
        isOpen={addSectionModalOpen}
        contentClassName={styles.addSectionModal}
        title={tRoutes("addSection")}
      >
        <form
          onSubmit={(e: React.FormEvent<HTMLFormElement>) => {
            e.preventDefault()
            const formData = new FormData(e.currentTarget)
            const sectionTitle = formData.get("section-title") as string

            addSectionToTemplate({
              variables: {
                input: {
                  content: "",
                  isResolutionNote: false,
                  journalTemplateId: template.id,
                  title: sectionTitle,
                },
              },
            })
            setAddSectionModalOpen(false)
          }}
        >
          <Input
            name="section-title"
            label={tRoutes("sectionTitle")}
            required
          />
          <div className={styles.modalFooter}>
            <Button
              variant="clear"
              onClick={() => setAddSectionModalOpen(false)}
            >
              {tRoutes("cancel")}
            </Button>
            <Button variant="filled" type="submit">
              {tRoutes("add")}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  )
}
