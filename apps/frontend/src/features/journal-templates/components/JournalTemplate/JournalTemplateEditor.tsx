import c from "classnames"
import { maxBy } from "lodash"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { useDebouncedCallback } from "use-debounce"

import AnimateMount from "components/AnimateMount/AnimateMount"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { InlineGuideInput } from "components/InlineGuide/InlineGuideInput"
import Restricted from "features/authentication/components/Restricted/Restricted"
import LexicalEditor from "features/rich-text-editor/LexicalEditor"
import EditableText from "features/subject-journal/components/EditableText/EditableText"
import SaveStatus, {
  getSaveStatus,
} from "features/subject-journal/components/SaveStatus/SaveStatus"
import { Button, Heading, IconButton, notification, Text } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import Switch from "ui/components/Switch/Switch"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  JournalTemplateType,
  PermissionKey,
  useDeleteJournalBlockTemplateMutation,
  useEditJournalEntryTemplateSectionMutation,
} from "generated/graphql"

import styles from "./JournalTemplate.module.css"

type JournalTemplateEditorProps = {
  template: JournalTemplateFragmentFragment
  canEdit: boolean
}

export const JournalTemplateEditor = ({
  template,
  canEdit,
}: JournalTemplateEditorProps) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })

  const [
    editJournalEntryTemplateSection,
    { loading: isLoading, called: isCalled, error },
  ] = useEditJournalEntryTemplateSectionMutation()

  const [deleteJournalBlockTemplate] = useDeleteJournalBlockTemplateMutation()

  const [sectionToDelete, setSectionToDelete] = useState<{
    id: string
    index: number
  } | null>(null)

  const [localContents, setLocalContents] = useState(
    template.sections.map((section) => section.content || "")
  )

  const [localTitles, setLocalTitles] = useState(
    template.sections.map((section) => section.title || "")
  )

  const [focusedSection, setFocusedSection] = useState(0)

  // Use useEffect to update localTitles and localContents when template.sections changes
  useEffect(() => {
    setLocalContents(template.sections.map((section) => section.content || ""))
    setLocalTitles(template.sections.map((section) => section.title || ""))
  }, [template.sections])

  const saveContentCallback = (content: string, sectionId: string) => {
    if (template.status === JournalTemplateStatus.Archived) return

    editJournalEntryTemplateSection({
      variables: {
        id: sectionId,
        input: {
          content,
        },
      },
    })
  }

  const debouncedSaveContent = useDebouncedCallback(saveContentCallback, 2000, {
    // 10 second max wait time
    maxWait: 10000,
  })

  const handleChange = (updatedContent: string, index: number) => {
    if (updatedContent === localContents[index]) return

    const newLocalContents = [...localContents]
    newLocalContents[index] = updatedContent
    setLocalContents(newLocalContents)
    debouncedSaveContent(updatedContent, template.sections[index].id)
  }

  const handleTitleChange = (updatedTitle: string, index: number) => {
    if (!updatedTitle) {
      notification.create({
        message: tRoutes("sectionTitleRequired"),
        status: "error",
      })
      return
    }

    if (updatedTitle === localTitles[index]) return

    const newLocalTitles = [...localTitles]
    newLocalTitles[index] = updatedTitle
    setLocalTitles(newLocalTitles)
    editJournalEntryTemplateSection({
      variables: {
        id: template.sections[index].id,
        input: {
          title: updatedTitle,
          content: template.sections[index].content,
        },
      },
    })
  }

  const handleBlur = (index: number) => {
    if (
      template.sections.length > 0 &&
      template.sections[index].content === localContents[index]
    )
      return

    debouncedSaveContent.flush()
  }
  useEffect(
    () => () => {
      debouncedSaveContent.flush()
    },
    [debouncedSaveContent]
  )

  const saveStatus = getSaveStatus({
    isLoading: isLoading,
    isError: !!error,
    isCalled: isCalled,
    isContentSync: localContents.every(
      (content, index) => content === template.sections[index]?.content
    ),
  })

  const mostRecentUpdatedAt = maxBy(template.sections, "updatedAt")?.updatedAt
  const templateHasResolutionNote = template.sections.some(
    (s) => s.isResolutionNote
  )

  const shouldHideResolutionNoteSwitch = (isResolutionNote: boolean) => {
    // For document templates, we hide the switch if the template
    // has a section marked as resolution note and the current section is not a resolution note
    if (template.templateType === JournalTemplateType.DocumentTemplate) {
      return templateHasResolutionNote && !isResolutionNote
    }

    // For extended document templates, we only show the switch if the current section is a resolution note
    if (
      template.templateType === JournalTemplateType.ExtendedDocumentTemplate
    ) {
      return !isResolutionNote
    }

    // For inline templates, we never show the switch
    return true
  }

  const documentTemplateCanEdit =
    canEdit && template.templateType === JournalTemplateType.DocumentTemplate

  const handleDeleteSection = (sectionId: string, index: number) => {
    deleteJournalBlockTemplate({
      variables: {
        blockTemplateId: sectionId,
      },
      onCompleted: () => {
        const newLocalContents = [...localContents]
        const newLocalTitles = [...localTitles]
        newLocalContents.splice(index, 1)
        newLocalTitles.splice(index, 1)
        setLocalContents(newLocalContents)
        setLocalTitles(newLocalTitles)
        notification.create({
          message: tRoutes("deleteSectionSuccess"),
          status: "success",
        })
      },
      onError: () =>
        notification.create({
          message: tRoutes("deleteSectionError"),
          status: "error",
        }),
    })

    setSectionToDelete(null)
  }

  if (!canEdit) {
    return (
      <div className={styles.editorWrapper}>
        {template.sections.map((section, index) => (
          <div key={section.id} className={styles.viewSection}>
            {section.title && <Heading>{localTitles[index]}</Heading>}
            <InlineGuideInput
              value={section.inlineGuide || ""}
              canEdit={false}
            />
            <div
              className={c(styles.sectionContent, {
                [styles.noContent]: !section.content,
              })}
              dangerouslySetInnerHTML={{
                __html: section.content || "&para;",
              }}
            />
          </div>
        ))}
      </div>
    )
  }

  return (
    <div className={styles.editorWrapper}>
      {template.sections.map((section, index) => (
        <div key={section.id} className={styles.section}>
          {template.templateType !== JournalTemplateType.InlineTemplate && (
            <>
              <div className={styles.sectionHeading}>
                <EditableText
                  title={localTitles[index]}
                  inputSize="small"
                  onSave={(input) => handleTitleChange(input, index)}
                  isEditable={documentTemplateCanEdit}
                  wrapperElement={Heading}
                  fallbackText={tRoutes("addSectionTitle")}
                  testid={`${section.id}-section-title`}
                />
                {
                  // Only show delete button for document templates
                  documentTemplateCanEdit && (
                    <Tooltip tooltipContent={tRoutes("deleteSection")}>
                      <IconButton
                        variant="clear"
                        iconName="delete-bin-line"
                        className={styles.deleteSectionButton}
                        onClick={() =>
                          setSectionToDelete({ id: section.id, index })
                        }
                      />
                    </Tooltip>
                  )
                }
              </div>
              <InlineGuideInput
                formHeading={tRoutes("addSectionInlineGuide")}
                fallbackText={tRoutes("clickToAddSectionInlineGuide")}
                canEdit={documentTemplateCanEdit}
                onChange={(value) => {
                  editJournalEntryTemplateSection({
                    variables: {
                      id: section.id,
                      input: {
                        content: section.content,
                        inlineGuide: value,
                      },
                    },
                  })
                }}
                value={section.inlineGuide || ""}
              />
            </>
          )}
          {index === focusedSection ? (
            <LexicalEditor
              id={template.id}
              onChange={(updatedContent) => handleChange(updatedContent, index)}
              onBlur={() => handleBlur(index)}
              initialContent={section.content}
              journalEntryId={""}
              sectionId={section.id}
              journalTemplates={[]}
              snippets={[]}
            />
          ) : (
            <button
              id={section.id}
              className={c(styles.sectionContent, styles.clickable)}
              onClick={() => setFocusedSection(index)}
              dangerouslySetInnerHTML={{
                __html: section.content || "&para;",
              }}
            />
          )}
          <Restricted to={PermissionKey.SubjectJournalResolutionNoteEdit}>
            {canEdit && (
              <div
                className={c(styles.switch, {
                  [styles.visuallyHidden]: shouldHideResolutionNoteSwitch(
                    section.isResolutionNote
                  ),
                })}
              >
                <Text
                  size="small"
                  as="label"
                  htmlFor={`${section.id}-resolution-note-toggle`}
                >
                  {t("Resolution note")}
                </Text>
                <Switch
                  id={`${section.id}-resolution-note-toggle`}
                  checked={section.isResolutionNote}
                  disabled={
                    template.templateType ===
                    JournalTemplateType.ExtendedDocumentTemplate
                  }
                  onToggle={() => {
                    editJournalEntryTemplateSection({
                      variables: {
                        id: section.id,
                        input: {
                          content: section.content,
                          isResolutionNote: !section.isResolutionNote,
                        },
                      },
                    })
                  }}
                />
              </div>
            )}
          </Restricted>
        </div>
      ))}
      {mostRecentUpdatedAt && (
        <div className={styles.saveStatus}>
          <AnimateMount show={canEdit}>
            <SaveStatus status={saveStatus} updatedAt={mostRecentUpdatedAt} />
          </AnimateMount>
        </div>
      )}
      <Dialog
        isOpen={!!sectionToDelete}
        onClose={() => setSectionToDelete(null)}
        title={tRoutes("deleteSection")}
        actions={
          <>
            <Button
              onClick={() => {
                setSectionToDelete(null)
              }}
              variant="clear"
            >
              {tRoutes("keepSection")}
            </Button>
            <Button
              status="error"
              onClick={() => {
                if (!sectionToDelete) return
                handleDeleteSection(sectionToDelete.id, sectionToDelete.index)
              }}
            >
              {t("doDelete")}
            </Button>
          </>
        }
      >
        <Text>{tRoutes("deleteSectionConfirmation")}</Text>
      </Dialog>
    </div>
  )
}
