mutation CreateMedicalCertificate(
  $input: CreateMedicalCertificateInput!
  $createMedicalCertificateId: UUID!
) {
  createMedicalCertificate(input: $input, id: $createMedicalCertificateId) {
    id
    ...MedicalCertificateSupplementFragment
    entry {
      ... on JournalEntry {
        id
        blocks {
          id
        }
      }
    }
  }
  setSubjectJournalFocusedItem(
    input: {
      encounterId: null
      journalEntryBlockId: $createMedicalCertificateId
    }
  ) {
    subjectJournal {
      id
      focusedItemId
    }
  }
}
