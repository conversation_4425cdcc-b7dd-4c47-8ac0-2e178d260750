import { useMemo } from "react"
import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import usePermissions from "features/authentication/hooks/usePermissions"
import { EditFieldForm } from "features/dashboard/components/EditFieldForm/EditFieldForm"
import EditableText from "features/subject-journal/components/EditableText/EditableText"
import { Heading, Label, Tag, Text, TextWithIcon } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  PermissionKey,
  useEditJournalTemplateMutation,
} from "generated/graphql"

import styles from "./JournalTemplate.module.css"
import { JournalTemplateEditor } from "./JournalTemplateEditor"

type InlineTemplateProps = {
  template: JournalTemplateFragmentFragment
}

export const InlineTemplate = ({ template }: InlineTemplateProps) => {
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })
  const { hasPermission } = usePermissions()
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()

  const { globalData } = useGlobalState()

  const actorId = globalData.actor.id

  const [updateJournalTemplate] = useEditJournalTemplateMutation()

  const dateUpdated = useMemo(() => {
    if (!template) return
    const dateUpdated = new Date(template.updatedAt)
    const sectionsUpdatedAt = template.sections.map(
      (section) => new Date(section.updatedAt)
    )

    const allDates = [dateUpdated, ...sectionsUpdatedAt]
    return new Date(Math.max(...allDates.map((date) => date.getTime())))
  }, [template])

  if (!template || template.status === JournalTemplateStatus.Archived) {
    return <div className={styles.wrap} />
  }

  const canEdit =
    hasPermission(PermissionKey.SubjectJournalTemplateEdit) &&
    template.createdBy.id === actorId

  const dateCreated = new Date(template.createdAt)

  const createdAt = `Created at: ${dateFormat(dateCreated)}, ${timeFormat(
    dateCreated
  )}`

  const updatedAt = dateUpdated
    ? `${dateFormat(dateUpdated)}, ${timeFormat(dateUpdated)}`
    : ""

  return (
    <>
      <header className={styles.header}>
        <div className={styles.topHeading}>
          <Tag
            size="small"
            color={
              template.status === JournalTemplateStatus.Published
                ? "levGreen"
                : "gray"
            }
            weight="bold"
            className={styles.status}
            test-id="journal-template-status"
          >
            {tEnum(`JournalTemplateStatus.${template.status}`)}
          </Tag>
        </div>
        <div className={styles.heading}>
          <EditableText
            title={template.name}
            textSize="large"
            inputSize="small"
            onSave={(input) =>
              updateJournalTemplate({
                variables: {
                  input: {
                    id: template.id,
                    name: input,
                  },
                },
              })
            }
            isEditable={canEdit}
            wrapperElement={Heading}
            fallbackText={tRoutes("addTitle")}
            testid="journal-template-title"
          />
        </div>
        <TextWithIcon
          as={Label}
          iconName="calendar-2-line"
          secondary
          title={createdAt}
          size="small"
          className={styles.updatedAt}
        >
          {updatedAt}
        </TextWithIcon>
        <div>
          {canEdit ? (
            <EditFieldForm
              name={"template-description"}
              value={template.description || ""}
              label={tRoutes("description")}
              hideLabel
              placeholder={tRoutes("addDescription")}
              onSubmit={(input) => {
                updateJournalTemplate({
                  variables: {
                    input: {
                      id: template.id,
                      description: input,
                    },
                  },
                })
              }}
              testid="template-description"
            />
          ) : (
            <Text>{template.description}</Text>
          )}
        </div>
      </header>

      <JournalTemplateEditor template={template} canEdit={canEdit} />
    </>
  )
}
