mutation ApplyJournalTemplate(
  $id: UUID!
  $encounterId: UUID
  $existingJournalEntry: ApplyToExistingJournalEntryInput
) {
  applyJournalTemplate(
    id: $id
    encounterId: $encounterId
    existingJournalEntry: $existingJournalEntry
  ) {
    ...JournalEntryFragment
    encounter {
      id
      journalEntries {
        id
        sections {
          id
        }
        blocks {
          id
        }
      }
      status {
        status
        allowedTransitions
      }
    }
  }
}
