import c from "classnames"
import { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { InlineGuideInput } from "components/InlineGuide/InlineGuideInput"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { EditFieldForm } from "features/dashboard/components/EditFieldForm/EditFieldForm"
import EditableText from "features/subject-journal/components/EditableText/EditableText"
import {
  Button,
  Heading,
  Input,
  Label,
  Modal,
  notification,
  Tag,
  Text,
  TextWithIcon,
} from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  PermissionKey,
  TemplateAccessScope,
  useEditJournalTemplateMutation,
} from "generated/graphql"

import { TemplateAccessScopeMenu } from "../TemplateAccessScopeMenu/TemplateAccessScopeMenu"
import styles from "./ExtendedDocumentTemplate.module.css"
import baseStyles from "./JournalTemplate.module.css"
import { JournalTemplateEditor } from "./JournalTemplateEditor"

type ExtendedDocumentTemplateProps = {
  template: JournalTemplateFragmentFragment
}

export const ExtendedDocumentTemplate = ({
  template,
}: ExtendedDocumentTemplateProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums")
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "journalTemplates",
  })
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()
  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const [showAPIreferenceModal, setShowAPIreferenceModal] = useState(false)
  const [referenceKeyError, setReferenceKeyError] = useState<string | null>(
    null
  )

  const [updateJournalTemplate] = useEditJournalTemplateMutation()

  const noTemplate =
    !template ||
    template.status === JournalTemplateStatus.Archived ||
    !template.documentType

  // Get the latest updated date from the template and its sections
  const lastUpdatedAt = useMemo(() => {
    if (noTemplate) return
    const lastUpdatedAt = new Date(template.updatedAt)
    const sectionsUpdatedAt = template.sections.map(
      (section) => new Date(section.updatedAt)
    )

    const allDates = [lastUpdatedAt, ...sectionsUpdatedAt]
    return new Date(Math.max(...allDates.map((date) => date.getTime())))
  }, [template])

  if (noTemplate) {
    return <div className={baseStyles.wrap} />
  }

  const closeReferenceModal = () => {
    setShowAPIreferenceModal(false)
    setReferenceKeyError(null)
  }

  // The user can edit the template if they are the creator or the template has access scope public write
  const canEdit =
    actor.id === template.createdBy.id ||
    template.accessScope === TemplateAccessScope.PublicWrite

  const dateCreated = new Date(template.createdAt)

  const createdAt = tRoutes("createdAt", {
    date: dateFormat(dateCreated),
    time: timeFormat(dateCreated),
  })

  const updatedAt = lastUpdatedAt
    ? tRoutes("updatedAt", {
        date: dateFormat(lastUpdatedAt),
        time: timeFormat(lastUpdatedAt),
      })
    : ""

  const extendedFromDocument = template.extendedFromTemplate?.name

  return (
    <>
      <header className={baseStyles.header}>
        <div className={baseStyles.topHeading}>
          <Tag
            color="blue"
            size="small"
            className={baseStyles.documentType}
            weight="bold"
            test-id="template-document-type"
          >
            {tEnum(`DocumentType.${template.documentType}.label`)}
          </Tag>
          <div className={baseStyles.tagsContainer}>
            {actor.id === template.createdBy.id && (
              <Tag
                size="small"
                color={
                  template.status === JournalTemplateStatus.Published
                    ? "levGreen"
                    : "gray"
                }
                weight="bold"
                className={baseStyles.status}
                test-id="journal-template-status"
              >
                {tEnum(`JournalTemplateStatus.${template.status}`)}
              </Tag>
            )}

            <TemplateAccessScopeMenu
              templateId={template.id}
              currentScope={template.accessScope}
              canEdit={actor.id === template.createdBy.id}
            />
          </div>
        </div>
        <div className={baseStyles.heading}>
          <Label secondary test-id={"base-document-template"}>
            {`${tRoutes("documentBasedOn")} ${extendedFromDocument}`}
          </Label>
          <EditableText
            title={template.name}
            textSize="large"
            inputSize="small"
            onSave={(input) => {
              if (!input) {
                notification.create({
                  message: tRoutes("titleRequired"),
                  status: "error",
                })
                return
              }
              updateJournalTemplate({
                variables: {
                  input: {
                    id: template.id,
                    name: input,
                  },
                },
              })
            }}
            isEditable={canEdit}
            wrapperElement={Heading}
            fallbackText={tRoutes("addTitle")}
            testid="journal-template-title"
          />
        </div>
        <Restricted to={PermissionKey.SubjectJournalResolutionNoteEdit}>
          <button
            className={c(styles.apiReferenceButton, {
              [styles.noValue]: !template.reference,
            })}
            onClick={() => {
              setShowAPIreferenceModal(true)
            }}
            title={template.reference || ""}
          >
            <Text className={styles.apiReference}>
              {template.reference || tRoutes("addReferenceKey")}
            </Text>
            <Icon name="edit-line" />
          </button>
        </Restricted>
        <div className={baseStyles.subHeading}>
          <div className={baseStyles.creator}>
            <Label>{template.createdBy.name} •</Label>
            <Label secondary>
              {tEnum(`ProviderSpecialty.${template.createdBy.specialty}`)}
            </Label>
          </div>
          <TextWithIcon
            as={Label}
            iconName="calendar-2-line"
            secondary
            title={createdAt}
            size="small"
            className={baseStyles.updatedAt}
          >
            {updatedAt}
          </TextWithIcon>
        </div>
        <div>
          {canEdit ? (
            <EditFieldForm
              name={"template-description"}
              value={template.description || ""}
              label={tRoutes("description")}
              hideLabel
              placeholder={tRoutes("addDescription")}
              onSubmit={(input) => {
                updateJournalTemplate({
                  variables: {
                    input: {
                      id: template.id,
                      description: input,
                    },
                  },
                })
              }}
              testid="template-description"
            />
          ) : (
            <Text>{template.description}</Text>
          )}
        </div>
        <InlineGuideInput
          formHeading={tRoutes("addTemplateInlineGuide")}
          fallbackText={tRoutes("clickToAddTemplateInlineGuide")}
          canEdit={canEdit}
          onChange={(value) => {
            updateJournalTemplate({
              variables: {
                input: {
                  id: template.id,
                  inlineGuide: value,
                },
              },
            })
          }}
          value={template.inlineGuide || ""}
        />
      </header>
      <JournalTemplateEditor template={template} canEdit={canEdit} />
      <Modal isOpen={showAPIreferenceModal} onClose={closeReferenceModal}>
        <form
          className={styles.modal}
          onSubmit={(e) => {
            e.preventDefault()
            const formData = new FormData(e.target as HTMLFormElement)
            const newValue = formData.get("template-api-reference") as string
            updateJournalTemplate({
              variables: {
                input: {
                  id: template.id,
                  reference: {
                    set: newValue,
                  },
                },
              },
              onCompleted: () => {
                closeReferenceModal()
              },
              onError: (error) => {
                const referenceDuplicateKeyError = `reference must be unique`
                if (error.message.includes(referenceDuplicateKeyError)) {
                  setReferenceKeyError(tRoutes("uniqueReferenceError"))
                  return
                }

                closeReferenceModal()
                notification.create({
                  message: tRoutes("updateReferenceError"),
                  status: "error",
                })
              },
            })
          }}
        >
          <Heading>{tRoutes("referenceKeyHeading")}</Heading>
          <Text>{tRoutes("referenceKeyDescription")}</Text>
          <Input
            name="template-api-reference"
            defaultValue={template.reference || ""}
            label={tRoutes("apiReferenceKey")}
            status={referenceKeyError ? "error" : "default"}
            message={referenceKeyError}
            className={styles.referenceInput}
            onKeyDown={(e) => {
              if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault()
                e.currentTarget.form?.dispatchEvent(
                  new Event("submit", { bubbles: true, cancelable: true })
                )
              }
            }}
          />
          <div className={styles.modalFooter}>
            <Button variant="clear" onClick={closeReferenceModal}>
              {t("cancel")}
            </Button>
            <Button type="submit" variant="filled">
              {t("Update")}
            </Button>
          </div>
        </form>
      </Modal>
    </>
  )
}
