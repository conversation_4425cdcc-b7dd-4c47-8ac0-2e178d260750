.apiReferenceButton {
  cursor: pointer;
  width: fit-content;
  border-radius: var(--radius-button);
  padding: 4px 8px;
  margin: 0 -8px;
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--color-brand-primary-blue);
}

.apiReferenceButton:hover {
  background-color: var(--color-blue-primary-on-white-active);
}

.apiReferenceButton > svg {
  opacity: 0;
  color: var(--color-brand-primary-blue);
  flex-shrink: 0;
}

.apiReferenceButton:hover > svg {
  opacity: 1;
}

.apiReference {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noValue {
  color: var(--color-gray-60);
}

.modal {
  display: grid;
  gap: 16px;
  max-width: 500px;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.referenceInput > input {
  text-transform: uppercase;
}
