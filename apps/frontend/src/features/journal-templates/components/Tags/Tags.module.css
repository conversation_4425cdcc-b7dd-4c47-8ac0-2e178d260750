.wrap {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.tag {
  border-radius: 7px;
  border: 1px solid var(--color-lev-blue);
  background: var(--color-white);
  padding: 6px 18px;
  color: var(--color-lev-blue);
  cursor: pointer;
}

.tag[data-is-selected="true"] {
  background: var(--color-lev-blue);
  color: var(--color-white);
}

.addIcon {
  width: 32px;
  height: 32px;
  color: var(--color-lev-blue);
  cursor: pointer;
}
