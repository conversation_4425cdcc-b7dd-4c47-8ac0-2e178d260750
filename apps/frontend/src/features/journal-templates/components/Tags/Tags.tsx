import Icon from "components/Icon/Icon"

import styles from "./Tags.module.css"

export const Tags = () => {
  const tags = [
    { name: "Cardiology", isSelected: true },
    { name: "Neurology", isSelected: true },
    { name: "Dermatology", isSelected: false },
    { name: "Pediatric<PERSON>", isSelected: false },
    { name: "Radiology", isSelected: false },
  ]

  return (
    <div className={styles.wrap}>
      {tags.map((tag) => (
        <span className={styles.tag} data-is-selected={tag.isSelected}>
          {tag.name}
        </span>
      ))}

      <button>
        <Icon className={styles.addIcon} name="add-box-line" />
      </button>
    </div>
  )
}
