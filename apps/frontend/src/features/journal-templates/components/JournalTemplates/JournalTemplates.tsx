import { generatePath, useNavigate, useParams } from "react-router-dom"

import usePermissions from "features/authentication/hooks/usePermissions"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { RouteStrings } from "routes/RouteStrings"

import {
  JournalTemplateFragmentFragment,
  JournalTemplateStatus,
  PermissionKey,
  useGetJournalTemplatesQuery,
} from "generated/graphql"

import { JournalTemplate } from "../JournalTemplate/JournalTemplate"
import { NoTemplateAvailable } from "../JournalTemplate/NoTemplateAvailable"
import { JournalTemplateSidebar } from "../JournalTemplatesSidebar/JournalTemplatesSidebar"
import styles from "./JournalTemplates.module.css"

type TemplateProps = {
  children: React.ReactNode
}

export const JournalTemplates = ({ children }: TemplateProps) => {
  const { templateId, templateType } = useParams<{
    templateId: string
    templateType: string
  }>()
  const navigate = useNavigate()
  const { hasPermission } = usePermissions()

  const { data: journalTemplatesData, loading } = useGetJournalTemplatesQuery({
    variables: {
      filter: {
        status: [JournalTemplateStatus.Draft, JournalTemplateStatus.Published],
      },
    },
    onCompleted: (data) => {
      // If there is no templateId in the url, redirect to the first template of the given type
      const templates =
        data?.journalTemplates.filter(
          (t) => t.templateType === templateType?.toUpperCase()
        ) || []

      const shouldNavigate =
        data?.journalTemplates &&
        data.journalTemplates.length > 0 &&
        !templateId

      if (shouldNavigate) {
        navigate(
          generatePath(RouteStrings.journalTemplates, {
            templateType: templateType?.toLowerCase(),
            templateId: templates[0].id,
          })
        )
      }
    },
  })

  const templates: JournalTemplateFragmentFragment[] = [
    ...(journalTemplatesData?.journalTemplates || []),
  ]

  if (!hasPermission(PermissionKey.SubjectJournalTemplateView)) {
    return <UnauthorizedPage />
  }

  if (templates.length === 0 && !loading) {
    return (
      <div className={styles.wrap}>
        <JournalTemplateSidebar templates={templates} />
        <div className={styles.templateContainer}>
          <NoTemplateAvailable />
        </div>
        {children}
      </div>
    )
  }

  return (
    <div className={styles.wrap}>
      <JournalTemplateSidebar templates={templates} />
      <div className={styles.templateContainer}>
        {templateId && <JournalTemplate templateId={templateId} />}
      </div>
      {children}
    </div>
  )
}
