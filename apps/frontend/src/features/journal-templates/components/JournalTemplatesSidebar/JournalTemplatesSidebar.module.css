.wrap {
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 472px;
  overflow-y: auto;
  padding-right: 36px;
  margin-right: -36px;
  padding-bottom: 60px;
}

.heading {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  padding-bottom: 8px;
}

.searchIcon {
  color: var(--color-gray-30);
}

.list {
  display: grid;
  gap: 16px;
}

.list > :first-child {
  margin-bottom: 8px;
}
