.template {
  width: 100%;
  padding: 12px;
  display: grid;
  gap: 8px;
  cursor: pointer;
  color: inherit;
  text-decoration: none;
  --color-text: var(--color-black);
  position: relative;
  transform: background-color 0.1s;
}

.template:hover,
.template:global(.active) {
  background-color: var(--color-lev-blue-200);
  border-radius: 16px;
}

.templateTitle {
  display: flex;
  justify-content: space-between;
}

.menuButton {
  position: absolute;
  top: 8px;
  right: 8px;
}

.menuItemDelete {
  color: var(--color-critical);
}

.templateInfo {
  display: grid;
  gap: 4px;
}

.name {
  margin-bottom: 4px;
}

.description,
.name {
  /* add text ellipsis after 3 lines */
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.creator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.creator div {
  color: var(--color-lev-blue-500);
}

.hidden {
  visibility: hidden;
}

.tags {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.statusTag {
  display: flex;
  gap: 6px;
  align-items: center;

  svg {
    margin-top: 2px;
  }
}
