import { gql, useApolloClient } from "@apollo/client"
import { matchSorter } from "match-sorter"
import { startTransition, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  ComboboxProvider,
  SelectItem,
  SelectPopover,
  SelectProvider,
} from "components/Ariakit"
import { SelectPopoverFallback } from "components/SelectPopoverFallback/SelectPopoverFallback"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { Text, TableCellSelect } from "ui"

import {
  PermissionKey,
  useGetAllTeamsQuery,
  useUpdateListItemsMutation,
} from "generated/graphql"

type SelectedTeamProps = {
  teamName?: string
}
const SelectedTeam = ({ teamName }: SelectedTeamProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  return teamName ? (
    <>{teamName}</>
  ) : (
    <Text secondary>{tRoutes("team.unassigned")}</Text>
  )
}

type WaitingListTeamSelectProps = {
  isEditable?: boolean
  value?: string
  waitingListItem: WaitingListItem
  portal?: boolean
}

export const WaitingListTeamSelect = ({
  isEditable = true,
  value,
  waitingListItem,
}: WaitingListTeamSelectProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const [searchValue, setSearchValue] = useState("")

  const [updateListItems] = useUpdateListItemsMutation()

  const client = useApolloClient()

  const itemId = waitingListItem.id

  const onTeamSelect = (teamId: string) => {
    const cachedTeam = client.readFragment<{
      id: string
      name: string
    }>({
      id: `Team:${teamId}`,
      fragment: gql`
        fragment TeamFragment on Team {
          id
          name
        }
      `,
    })

    updateListItems({
      variables: {
        input: {
          id: itemId,
          teamId: {
            set: teamId || null,
          },
        },
      },

      optimisticResponse: {
        updateListItems: [
          {
            ...waitingListItem,
            team: cachedTeam?.id
              ? {
                  id: teamId,
                  name: cachedTeam.name,
                  __typename: "Team",
                }
              : null,
          },
        ],

        __typename: "Mutation" as const,
      },
    })
  }

  if (!isEditable) {
    return <SelectedTeam teamName={waitingListItem.team?.name} />
  }

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListEdit}
      fallback={<SelectedTeam teamName={waitingListItem.team?.name} />}
    >
      <ComboboxProvider
        resetValueOnHide
        setValue={(value) => {
          startTransition(() => {
            setSearchValue(value)
          })
        }}
      >
        <SelectProvider defaultValue={value || ""} setValue={onTeamSelect}>
          <TableCellSelect
            tooltipContent={tRoutes(value ? "team.change" : "team.add")}
          >
            <SelectedTeam teamName={waitingListItem.team?.name} />
          </TableCellSelect>
          <WaitingListTeamSelectPopover searchValue={searchValue} />
        </SelectProvider>
      </ComboboxProvider>
    </Restricted>
  )
}

type WaitingListTeamSelectPopoverProps = {
  searchValue: string
  portal?: boolean
}

const WaitingListTeamSelectPopover = ({
  searchValue,
}: WaitingListTeamSelectPopoverProps) => {
  const { data, loading, error } = useGetAllTeamsQuery()
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const allOptions = data?.teams || []

  const matches = useMemo(() => {
    return matchSorter(allOptions, searchValue, {
      keys: ["name"],
    })
  }, [searchValue, allOptions])

  const noMatchesNoSearch = !searchValue && !matches.length && !loading

  const hasNoMatches = !matches.length && searchValue && !loading

  return (
    <SelectPopover sameWidth={false} unmountOnHide>
      <Combobox autoSelect placeholder={tRoutes("team.search")} />
      <ComboboxList>
        <SelectPopoverFallback
          noMatchesNoSearch={noMatchesNoSearch}
          hasNoMatches={hasNoMatches}
          loading={loading}
          error={error}
        >
          {matches.map(({ id, name }) => (
            <SelectItem
              key={id}
              value={id}
              render={<ComboboxItem>{name}</ComboboxItem>}
            />
          ))}
        </SelectPopoverFallback>
      </ComboboxList>
    </SelectPopover>
  )
}
