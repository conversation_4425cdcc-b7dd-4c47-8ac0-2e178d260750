import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { ListItemAssigneeSelect } from "components/ListItemAssigneeSelect/ListItemAssigneeSelect"
import { Pagination } from "components/Pagination/Pagination"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import ReferralModal from "features/waiting-list/components/ReferralModal/ReferralModal"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { PrivateRoutes } from "routes/RouteStrings"
import { Color } from "styles/colors"
import { Loading, Table, Tag, TextWithIcon } from "ui"
import { includesTypenames } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"

import {
  ListItemType,
  PermissionKey,
  useGetWaiting<PERSON><PERSON><PERSON><PERSON>y,
  UuidFilterOptionInput,
  namedOperations,
} from "generated/graphql"

import { HandleWaitingListItem } from "../HandleWaitingListItem/HandleWaitingListItem"
import { ManageWaitingListItem } from "../ManageWaitingListItem/ManageWaitingListItem"
import { WaitingListBookAppointmentModal } from "../WaitingListBookAppointmentModal/WaitingListBookAppointmentModal"
import { WaitingListNote } from "./WaitingListNote/WaitingListNote"
import { WaitingListPrioritySelect } from "./WaitingListPrioritySelect/WaitingListPrioritySelect"
import { WaitingListServiceTypeSelect } from "./WaitingListServiceTypeSelect/WaitingListServiceTypeSelect"
import { WaitingListSubjectSelect } from "./WaitingListSubjectSelect/WaitingListSubjectSelect"
import styles from "./WaitingListTable.module.css"
import { WaitingListTeamSelect } from "./WaitingListTeamSelect/WaitingListTeamSelect"

type WaitingListTableView = "unassigned" | "me"

type WaitingListTableProps = {
  view?: WaitingListTableView
}

const entryTypeColorMap: Record<string, Color> = {
  [ListItemType.Referral]: "pink",
  [ListItemType.AppointmentRequest]: "levGreen",
}

const limit = 30

export default function WaitingListTable({ view }: WaitingListTableProps) {
  const { t: tEnumItemType } = useTranslation("enums", {
    keyPrefix: "ListItemType",
  })
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const { pageNumber } = useParams()
  const { hasPermission } = usePermissions()

  const { globalData } = useGlobalState()

  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const [bookAppointmentItem, setBookAppointmentItem] =
    useState<WaitingListItem | null>(null)
  const [showReferralModal, setShowReferralModal] = useState<string | null>(
    null
  )

  const pageNumberInt = parseInt(pageNumber ?? "1")

  const offset = (pageNumberInt - 1) * limit

  const dateFormat = useDateFormatter()

  const getAssigneeFilter = (): UuidFilterOptionInput | undefined => {
    if (view === "me") {
      return {
        values: [globalData.actor.id],
        excludeNullValues: true,
      }
    }

    if (view === "unassigned") {
      return {
        excludeNullValues: false,
      }
    }
    return undefined
  }

  const { data, loading } = useGetWaitingListQuery({
    variables: {
      filter: {
        assigneeIds: getAssigneeFilter(),
      },
      pagination: {
        limit,
        offset,
      },
    },
    skip: isNaN(offset), // Skip fetching data when page is invalid
  })

  const rejectItemRefetchQueries = [
    namedOperations.Query.GetWaitingList,
    namedOperations.Query.GetWaitingListItemsCount,
  ]

  if (!data) {
    return null
  }

  const totalPages = Math.ceil(data.waitingListItems.totalCount / limit)

  const waitingListItems = data.waitingListItems.items.filter(
    includesTypenames(["AppointmentRequestItem", "ReferralItem"])
  )

  if (loading) {
    return <Loading large />
  }

  return (
    <>
      <Table className={styles.table} stickyHeader>
        <thead>
          <tr>
            <th></th>
            <th>{tRoutes("tableColumns.subject")}</th>
            <th>{tRoutes("tableColumns.assignee")}</th>
            <th>{tRoutes("tableColumns.team")}</th>
            <th>{tRoutes("tableColumns.serviceType")}</th>
            <th>{tRoutes("tableColumns.dateAdded")}</th>
            <th>{tRoutes("tableColumns.entryType")}</th>
            <th></th>
            <th></th>
          </tr>
        </thead>
        <tbody>
          {waitingListItems.length === 0 && (
            <tr>
              <td colSpan={9}>
                <TextWithIcon iconName="information-line">
                  {tRoutes("noWaitingListItems")}
                </TextWithIcon>
              </td>
            </tr>
          )}

          {waitingListItems.map((waitingListItem) => {
            const isHandled = !!waitingListItem.handledAt
            const showHandleActions =
              !isHandled && hasPermission(PermissionKey.ListsWaitingListEdit)
            return (
              <tr
                key={waitingListItem.id}
                id={waitingListItem.id}
                className={c(styles.row, {
                  [styles.notHandledRow]: !isHandled,
                })}
              >
                <td className={styles.priorityRow}>
                  <WaitingListPrioritySelect
                    value={waitingListItem.priority}
                    waitingListItem={waitingListItem}
                  />
                </td>
                <td>
                  <WaitingListSubjectSelect
                    {...waitingListItem.subject}
                    canViewSubjectJournal={canViewSubjectJournal}
                  />
                </td>
                <td>
                  <ListItemAssigneeSelect
                    key={waitingListItem.id}
                    id={waitingListItem.id}
                    value={waitingListItem.assignee?.name}
                    waitingListItem={waitingListItem}
                    refetchQueries={[
                      namedOperations.Query.GetWaitingListItemsCount,
                      namedOperations.Query.ProviderBox,
                    ]}
                  />
                </td>
                <td>
                  <WaitingListTeamSelect
                    value={waitingListItem.team?.id}
                    waitingListItem={waitingListItem}
                  />
                </td>
                <td>
                  <WaitingListServiceTypeSelect
                    value={waitingListItem.serviceType?.name}
                    waitingListItem={waitingListItem}
                  />
                </td>
                <td>{dateFormat(new Date(waitingListItem.createdAt))}</td>
                <td>
                  <Tag
                    as="span"
                    color={entryTypeColorMap[waitingListItem.itemType]}
                    className={styles.entryType}
                    {...(waitingListItem.__typename === "ReferralItem" && {
                      as: "button",
                      onClick: () => setShowReferralModal(waitingListItem.id),
                    })}
                  >
                    {tEnumItemType(waitingListItem.itemType)}
                  </Tag>
                </td>
                <td
                  className={styles.comment}
                  colSpan={showHandleActions ? 1 : 2}
                >
                  <WaitingListNote
                    className={styles.note}
                    value={waitingListItem.comment}
                    waitingListItem={waitingListItem}
                  />
                  {isHandled && (
                    <Restricted to={PermissionKey.ListsWaitingListEdit}>
                      <div className={styles.itemActions}>
                        <ManageWaitingListItem
                          waitingListItem={waitingListItem}
                          refetchQueries={rejectItemRefetchQueries}
                          onBookAppointment={() =>
                            setBookAppointmentItem(waitingListItem)
                          }
                        />
                      </div>
                    </Restricted>
                  )}
                </td>
                {showHandleActions && (
                  <td className={styles.rowActions}>
                    <HandleWaitingListItem
                      id={waitingListItem.id}
                      type={waitingListItem.itemType}
                      subjectName={waitingListItem.subject.name}
                    />
                  </td>
                )}
              </tr>
            )
          })}
        </tbody>
      </Table>
      {totalPages > 1 && (
        <Pagination
          currentPage={pageNumberInt}
          totalPages={totalPages}
          routeString={PrivateRoutes.waitingList}
        />
      )}
      <WaitingListBookAppointmentModal
        waitingListItem={bookAppointmentItem}
        onClose={() => setBookAppointmentItem(null)}
      />
      {showReferralModal && (
        <ReferralModal
          id={showReferralModal}
          onClose={() => setShowReferralModal(null)}
        />
      )}
    </>
  )
}
