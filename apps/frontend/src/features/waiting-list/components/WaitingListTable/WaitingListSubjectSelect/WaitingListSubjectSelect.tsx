import { useTranslation } from "react-i18next"
import { generatePath } from "react-router-dom"

import { SubjectDetailsCard } from "components/SubjectDetailsCard/SubjectDetailsCard"
import { RouteStrings } from "routes/RouteStrings"

import { WaitingListItem } from "../../WaitingListPage/WaitingListPage"
import styles from "./WaitingListSubjectSelect.module.css"

type WaitingListSubjectSelectProps = WaitingListItem["subject"] & {
  canViewSubjectJournal: boolean
}
export const WaitingListSubjectSelect = ({
  id,
  name,
  personaId,
  phoneNumber,
  gender,
  age,
  canViewSubjectJournal,
}: WaitingListSubjectSelectProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "waitingList.subject" })

  return (
    <div className={styles.waitingListSubjectCell}>
      <SubjectDetailsCard
        id={id}
        name={name}
        personaId={personaId}
        phoneNumber={phoneNumber}
        gender={gender}
        age={age}
        tooltipContent={t(
          canViewSubjectJournal ? "openJournal" : "selectSubject"
        )}
        telLinkCn={styles.waitingListSubjectTelLink}
        {...(canViewSubjectJournal && {
          to: generatePath(RouteStrings.subjectJournal, {
            subjectId: id,
          }),
        })}
      />
    </div>
  )
}
