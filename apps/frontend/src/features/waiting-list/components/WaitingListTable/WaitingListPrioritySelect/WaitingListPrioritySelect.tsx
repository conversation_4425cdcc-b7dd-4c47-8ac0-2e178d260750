import { useTranslation } from "react-i18next"

import { SelectProvider } from "components/Ariakit"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { TableCellSelect } from "ui"

import {
  ListItemPriority,
  PermissionKey,
  useUpdateListItemsMutation,
} from "generated/graphql"

import { NO_PRIORITY, PriorityIcon } from "../../PrioritySelect/PriorityIcon"
import { PrioritySelectPopover } from "../../PrioritySelect/PrioritySelect"
import styles from "./WaitingListPrioritySelect.module.css"

type SelectedPriority = {
  value: ListItemPriority | null
}
const SelectedPriority = ({ value }: SelectedPriority) => (
  <PriorityIcon className={styles.priority} priority={value} />
)

type WaitingListPrioritySelect = {
  isEditable?: boolean
  value: ListItemPriority | null
  waitingListItem: WaitingListItem
}

export const WaitingListPrioritySelect = ({
  isEditable = true,
  value,
  waitingListItem,
}: WaitingListPrioritySelect) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const [updateListItems] = useUpdateListItemsMutation()

  const itemId = waitingListItem.id

  if (!isEditable) {
    return <SelectedPriority value={value} />
  }

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListEdit}
      fallback={<SelectedPriority value={value} />}
    >
      <SelectProvider
        defaultValue={value || ""}
        setValue={(priority: ListItemPriority | "NO_PRIORITY") => {
          updateListItems({
            variables: {
              input: {
                id: itemId,
                priority: {
                  set: priority === NO_PRIORITY ? null : priority,
                },
              },
            },
            optimisticResponse: {
              updateListItems: [
                {
                  ...waitingListItem,
                  priority: priority === NO_PRIORITY ? null : priority,
                },
              ],
              __typename: "Mutation" as const,
            },
          })
        }}
      >
        <TableCellSelect
          tooltipContent={tRoutes("changePrioritySelectTooltip")}
          hideIcon={true}
        >
          <SelectedPriority value={value} />
        </TableCellSelect>
        <PrioritySelectPopover sameWidth={false} unmountOnHide />
      </SelectProvider>
    </Restricted>
  )
}
