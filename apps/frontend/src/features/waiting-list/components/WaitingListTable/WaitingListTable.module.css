.row {
  position: relative;
}

.assigneeTableHeader {
  display: flex;
  align-items: center;
  gap: 4px;
}

.notHandledRow > td {
  --color-table-bg: var(--color-background-grey-light);
}

.priorityRow {
  width: 70px;
  min-width: 70px;
}
.iconPriority {
  font-size: 24px;
}
.entryType {
  white-space: wrap;
}
button.entryType {
  cursor: pointer;
}

.rowActions {
  width: 113px;
}

.itemActions {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  display: flex;
  opacity: 0;
  pointer-events: none;
  background: var(--color-table-bg);
  padding-right: 16px;
  align-items: center;
  gap: 8px;
  z-index: 1;
  transition: opacity 0.1s;
}
.itemActions:focus-within {
  /* When menu is open, we need to bump the
   * z-index so that it floats over the row below 
   * the waiting list note hovercard has --z-index-popover so we add 1 */
  z-index: calc(var(--z-index-popover) + 1);
}

.itemActions::before {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  left: -30px;
  width: 30px;
  background: linear-gradient(to right, transparent, var(--color-table-bg));

  z-index: -1;
  pointer-events: none;
}

.row:hover > td,
.row:has(:focus-visible) > td,
.row:has([aria-expanded="true"]) > td {
  --color-table-bg: var(--color-table-bg-hover);
}

.row:hover:not(.notHandledRow):not(:has(.note:hover)) .itemActions,
.itemActions:focus-within {
  opacity: 1;
  pointer-events: all;
}

.comment {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300px;
  min-width: 120px;
}
.comment[colspan="2"] {
  min-width: 300px;
}

@media (max-width: 1600px) {
  .table.table * {
    font-size: 14px;
  }
  .table.table th {
    font-size: 12px;
  }
}
