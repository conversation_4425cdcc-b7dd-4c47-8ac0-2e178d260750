fragment WaitingListItem on ListItem {
  id
  team {
    id
    name
  }
  comment
  assignee {
    id
    name
    specialty
  }
  priority
  itemType
  handledAt
  createdAt
  ... on AppointmentRequestItem {
    serviceType {
      id
      name
    }
    subject {
      ...WaitingListItemSubjectFragment
    }
  }
  ... on ReferralItem {
    serviceType {
      id
      name
    }
    subject {
      ...WaitingListItemSubjectFragment
    }
    inboundData {
      id
      date
      inboundType
      communicationStatus: status
    }
    eventInstance {
      ...UpcomingEventFragment
    }
    rejectedBy {
      ...ProviderInfoFragment
    }
    rejectedAt
    rejectionReason
    referralResolution: resolution
  }
  ... on DoctorLetterItem {
    rejectedBy {
      ...ProviderInfoFragment
    }
    rejectedAt
    rejectionReason
    doctorLetterResolution: resolution
  }
}
