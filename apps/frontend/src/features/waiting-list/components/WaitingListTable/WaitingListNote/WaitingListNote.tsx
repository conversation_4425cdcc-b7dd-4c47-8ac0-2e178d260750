import c from "classnames"
import { FormEvent, useState, useEffect, useRef } from "react"
import { useTranslation } from "react-i18next"

import {
  <PERSON><PERSON><PERSON>,
  HovercardAnchor,
  useHovercardStore,
} from "components/Ariakit/Hovercard/Hovercard"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import Restricted from "features/authentication/components/Restricted/Restricted"
import usePermissions from "features/authentication/hooks/usePermissions"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { Button, notification, Text, Textarea } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import { PermissionKey, useUpdateListItemsMutation } from "generated/graphql"

import styles from "./WaitingListNote.module.css"

const useIsOverflowing = (value: string | null) => {
  const noteTextRef = useRef<HTMLDivElement>(null)
  const [isOverflowing, setIsOverflowing] = useState(false)

  useEffect(() => {
    if (noteTextRef.current && value) {
      setIsOverflowing(
        noteTextRef.current.scrollWidth > noteTextRef.current.clientWidth
      )
    }
  }, [value])

  return [isOverflowing, noteTextRef] as const
}

type WaitingListNoteProps = {
  className?: string
  isEditable?: boolean
  value: string | null
  waitingListItem: WaitingListItem
}

export const WaitingListNote = ({
  isEditable = true,
  ...props
}: WaitingListNoteProps) => {
  if (!isEditable) {
    return <RestrictedNote value={props.value} className={props.className} />
  }

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListEdit}
      fallback={
        <RestrictedNote value={props.value} className={props.className} />
      }
    >
      <EditableNote {...props} />
    </Restricted>
  )
}

const EditableNote = ({
  value,
  waitingListItem,
  className = "",
}: WaitingListNoteProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const { hasPermission } = usePermissions()

  const [showNoteModal, setShowNoteModal] = useState(false)
  const [updateListItems] = useUpdateListItemsMutation()

  const hovercardStore = useHovercardStore({
    placement: "bottom-start",
    timeout: 100,
    animated: false,
  })

  const [isOverflowing, noteTextRef] = useIsOverflowing(value)

  const handleUpdateNote = (
    e: FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLFormElement>
  ) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const note = (formData.get("note") as string) || ""
    const itemId = waitingListItem.id

    updateListItems({
      variables: {
        input: {
          id: itemId,
          comment: {
            set: note || null,
          },
        },
      },
      onCompleted: () => {
        setShowNoteModal(false)
      },
      onError: () => {
        notification.create({
          status: "error",
          message: tRoutes("note.failedToUpdateError"),
        })
      },
      optimisticResponse: {
        updateListItems: [
          {
            ...waitingListItem,
            comment: note,
          },
        ],

        __typename: "Mutation" as const,
      },
    })
  }
  return (
    <>
      <HovercardAnchor
        store={hovercardStore}
        render={(props) => (
          <button
            {...props}
            type="button"
            className={c(styles.note, className, styles.editableNote, {
              [styles.hasOverflow]: isOverflowing,
            })}
            onClick={() => {
              setShowNoteModal(true)
            }}
          >
            <PiiSensitive
              as={Text}
              {...(!value ? { isNnMode: false } : {})}
              secondary={!value}
              className={styles.noteText}
              ref={noteTextRef}
            >
              {value || tRoutes("note.addButton")}
            </PiiSensitive>
            <Icon
              name="edit-line"
              className={c(styles.icon, {
                [styles.secondary]: !value,
              })}
            />
          </button>
        )}
      />
      {isOverflowing && (
        <Hovercard
          store={hovercardStore}
          gutter={-29}
          unmountOnHide
          animated={false}
          render={(props) => (
            <button
              aria-label={tRoutes(
                hasPermission(PermissionKey.ListsWaitingListEdit)
                  ? "note.edit"
                  : "note.view"
              )}
              type="button"
              className={c(styles.hovercard, className)}
              {...props}
              onClick={() => {
                setShowNoteModal(true)
              }}
            />
          )}
        >
          <PiiSensitive className={styles.hovercardNote}>{value}</PiiSensitive>

          <Icon name="edit-line" />
        </Hovercard>
      )}
      <NoteDialog
        isOpen={showNoteModal}
        onClose={() => setShowNoteModal(false)}
        value={value}
        handleUpdateNote={handleUpdateNote}
      />
    </>
  )
}

type RestrictedNoteProps = {
  value: string | null
  className?: string
}

const RestrictedNote = ({ value, className }: RestrictedNoteProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const [isOverflowing, noteTextRef] = useIsOverflowing(value)
  const hovercardStore = useHovercardStore({
    placement: "bottom-start",
    timeout: 100,
    animated: false,
  })
  const [showNoteModal, setShowNoteModal] = useState(false)

  return (
    <>
      <HovercardAnchor
        store={hovercardStore}
        render={(props) => (
          <button
            type="button"
            className={c(styles.note, className)}
            {...props}
          >
            <PiiSensitive
              as={Text}
              className={styles.noteText}
              ref={noteTextRef}
            >
              {value}
            </PiiSensitive>
          </button>
        )}
      />
      {isOverflowing && (
        <Hovercard
          store={hovercardStore}
          gutter={-29}
          unmountOnHide
          animated={false}
          render={(props) => (
            <button
              aria-label={tRoutes("note.view")}
              type="button"
              className={styles.hovercard}
              {...props}
              onClick={() => {
                setShowNoteModal(true)
              }}
            />
          )}
        >
          <PiiSensitive className={styles.hovercardNote}>{value}</PiiSensitive>
        </Hovercard>
      )}
      <NoteDialog
        isOpen={showNoteModal}
        onClose={() => setShowNoteModal(false)}
        value={value}
        isEditable={false}
      />
    </>
  )
}

type NoteDialogProps = {
  isOpen: boolean
  onClose: () => void
  value: string | null
  isEditable?: boolean
  handleUpdateNote?: (
    e: FormEvent<HTMLFormElement> | React.KeyboardEvent<HTMLFormElement>
  ) => void
}

function NoteDialog({
  isOpen,
  onClose,
  value,
  isEditable = true,
  handleUpdateNote,
}: NoteDialogProps) {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const { hasPermission } = usePermissions()

  const textareaRef = useRef<HTMLTextAreaElement>(null)
  useEffect(() => {
    if (isOpen && textareaRef.current) {
      textareaRef.current.focus()
      if (value)
        textareaRef.current.setSelectionRange(value.length, value.length)
    }
  }, [isOpen, textareaRef.current, value])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLFormElement>) => {
    if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
      handleUpdateNote?.(e)
    }
  }

  const formId = "noteForm"

  return (
    <Dialog
      title={tRoutes(
        hasPermission(PermissionKey.ListsWaitingListEdit)
          ? "note.edit"
          : "note.view"
      )}
      isOpen={isOpen}
      contentClassName={styles.dialog}
      onClose={onClose}
      closeOnClickOutside
      actions={
        <div className={styles.dialogFooter}>
          {isEditable && (
            <Restricted
              to={PermissionKey.ListsWaitingListEdit}
              fallback={
                <Button onClick={onClose}>{tRoutes("note.closeButton")}</Button>
              }
            >
              <Button onClick={onClose} variant="clear">
                {tRoutes("note.cancelButton")}
              </Button>
              <Button type="submit" variant="filled" form={formId}>
                {tRoutes("note.saveButton")}
              </Button>
            </Restricted>
          )}
        </div>
      }
    >
      <Restricted
        to={PermissionKey.ListsWaitingListEdit}
        fallback={
          <PiiSensitive className={styles.fallbackDialogContent}>
            {value}
          </PiiSensitive>
        }
      >
        <form id={formId} onSubmit={handleUpdateNote} onKeyDown={handleKeyDown}>
          <PiiSensitive
            as={Textarea}
            ref={textareaRef}
            name="note"
            label={tRoutes("note.label")}
            rows={4}
            defaultValue={value || ""}
            autoGrow
          />
        </form>
      </Restricted>
    </Dialog>
  )
}
