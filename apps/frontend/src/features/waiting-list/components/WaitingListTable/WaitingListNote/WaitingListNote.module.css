.wrap {
  width: fit-content;
  border-radius: var(--radius-button-half);
  max-width: 100%;
  min-width: 120px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.note {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 8px;
  padding: 4px 8px;
  margin: -4px -8px;
  min-width: 114px;
  max-width: 100%;
  min-height: 32px;
  background-color: transparent;
  border: none;
  border-radius: var(--radius-button-half);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.editableNote {
  cursor: pointer;
}

/* Skip showing these styles when the note has overflow (then we are showing the hovercard) */
.editableNote:not(.hasOverflow):hover,
.editableNote:not(.hasOverflow):focus-visible,
.editableNote:not(.hasOverflow)[aria-expanded="true"] {
  background: var(--color-lev-blue-on-white-hover);
  border: none;
}
.editableNote.note:focus-visible {
  /* Add a focus ring */
  border: 2px var(--color-lev-blue) solid;
  /* Reduce the padding to account for the border */
  padding: 2px 6px;
}

.noteText {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}
.hovercard {
  composes: popover from "../../../../../components/Popover/Popover.module.css";
  border: none;
  background: var(--color-lev-blue-on-white-hover);
  max-width: 400px;
  overflow: auto;
  white-space: normal;
  word-break: break-word;
  cursor: pointer;
  display: grid;
  grid-template-columns: 1fr auto;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  z-index: var(--z-index-popover);
  padding: 8px 13px;
  margin: -4px;
  max-height: var(--popover-available-height);
  min-width: var(--popover-anchor-width);
}
.hovercardNote {
  white-space: pre-wrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 16; /* number of lines to show */
  line-clamp: 16;
  -webkit-box-orient: vertical;
}
.hovercardOverflowText {
  margin-top: 8px;
}

.icon {
  opacity: 0;
  flex-shrink: 0;
}
.fallbackDialogContent {
  white-space: pre-wrap;
  word-break: break-word;
}

/* Skip showing the icon when the note has overflow (then we are showing the hovercard) */
.note:not(.hasOverflow):hover > .icon,
.note:not(.hasOverflow):focus-visible > .icon,
.note:not(.hasOverflow)[aria-expanded="true"] > .icon,
.note.note:focus-visible > .icon,
.icon:hover {
  opacity: 1;
}

.dialog {
  min-width: 500px;
}

.dialogFooter {
  display: flex;
  gap: 8px;
}

.secondary {
  color: var(--color-grey-d2);
}
