import { gql, useApolloClient } from "@apollo/client"
import { matchSorter } from "match-sorter"
import { startTransition, useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  ComboboxProvider,
  SelectItem,
  SelectPopover,
  SelectProvider,
} from "components/Ariakit"
import { SelectPopoverFallback } from "components/SelectPopoverFallback/SelectPopoverFallback"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { Text, TableCellSelect } from "ui"

import {
  PermissionKey,
  useGetServiceTypesQuery,
  useUpdateReferralItemsMutation,
  useUpdateWaitingListItemsMutation,
} from "generated/graphql"

type SelectedServiceType = {
  value?: string
}
const SelectedServiceType = ({ value }: SelectedServiceType) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  return value ? (
    <>{value}</>
  ) : (
    <Text secondary>{tRoutes("service.unassigned")}</Text>
  )
}

type WaitingListServiceTypeSelectProps = {
  isEditable?: boolean
  value?: string
  waitingListItem: WaitingListItem
}

export const WaitingListServiceTypeSelect = ({
  isEditable = true,
  value,
  waitingListItem,
}: WaitingListServiceTypeSelectProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const [searchValue, setSearchValue] = useState("")

  const [updateWaitingListItems] = useUpdateWaitingListItemsMutation()
  const [updateReferralItems] = useUpdateReferralItemsMutation()

  const client = useApolloClient()

  const itemId = waitingListItem.id

  const handleUpdate = (serviceTypeId: string) => {
    const cachedServiceType = client.readFragment<{
      id: string
      name: string
    }>({
      id: `ExternalServiceType:${serviceTypeId}`,
      fragment: gql`
        fragment ServiceTypeFragment on ExternalServiceType {
          id
          name
        }
      `,
    })

    if (waitingListItem.__typename === "AppointmentRequestItem") {
      updateWaitingListItems({
        variables: {
          input: {
            id: waitingListItem.id,
            serviceTypeId: {
              set: serviceTypeId || null,
            },
          },
        },
        optimisticResponse: {
          updateAppointmentRequestItems: [
            {
              ...waitingListItem,
              id: itemId,
              serviceType: cachedServiceType?.name
                ? {
                    id: serviceTypeId,
                    name: cachedServiceType.name,
                    __typename: "ExternalServiceType",
                  }
                : null,
              __typename: "AppointmentRequestItem",
            },
          ],

          __typename: "Mutation" as const,
        },
      })
    }

    if (waitingListItem.__typename === "ReferralItem") {
      updateReferralItems({
        variables: {
          input: {
            id: waitingListItem.id,
            serviceTypeId: {
              set: serviceTypeId || null,
            },
          },
        },
        optimisticResponse: {
          updateReferralItems: [
            {
              ...waitingListItem,
              id: itemId,
              serviceType: cachedServiceType?.name
                ? {
                    id: serviceTypeId,
                    name: cachedServiceType.name,
                    __typename: "ExternalServiceType",
                  }
                : null,
              __typename: "ReferralItem",
            },
          ],

          __typename: "Mutation" as const,
        },
      })
    }
  }

  if (!isEditable) {
    return <SelectedServiceType value={value} />
  }

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListEdit}
      fallback={<SelectedServiceType value={value} />}
    >
      <ComboboxProvider
        resetValueOnHide
        setValue={(value) => {
          startTransition(() => {
            setSearchValue(value)
          })
        }}
      >
        <SelectProvider
          defaultValue={value || ""}
          setValue={handleUpdate}
          // setValue={(serviceTypeId: string) => {
          //   handleUpdate(serviceTypeId)
          // }}
        >
          <TableCellSelect
            tooltipContent={tRoutes(value ? "service.change" : "service.add")}
          >
            <SelectedServiceType value={value} />
          </TableCellSelect>
          <WaitingListServiceTypeSelectPopover searchValue={searchValue} />
        </SelectProvider>
      </ComboboxProvider>
    </Restricted>
  )
}

type WaitingListServiceTypeSelectPopoverProps = {
  searchValue: string
}

const WaitingListServiceTypeSelectPopover = ({
  searchValue,
}: WaitingListServiceTypeSelectPopoverProps) => {
  const { data, loading, error } = useGetServiceTypesQuery()

  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const allOptions = data?.externalServiceTypes || []

  const matches = useMemo(() => {
    return matchSorter(allOptions, searchValue, {
      keys: ["name"],
    })
  }, [searchValue, allOptions])

  const noMatchesNoSearch = !searchValue && !matches.length && !loading

  const hasNoMatches = !matches.length && searchValue && !loading

  return (
    <SelectPopover sameWidth={false} unmountOnHide>
      <Combobox autoSelect placeholder={tRoutes("service.search")} />
      <ComboboxList>
        <SelectPopoverFallback
          noMatchesNoSearch={noMatchesNoSearch}
          hasNoMatches={hasNoMatches}
          loading={loading}
          error={error}
        >
          {matches.map(({ id, name }) => (
            <SelectItem
              key={id}
              value={id}
              render={<ComboboxItem>{name}</ComboboxItem>}
            />
          ))}
        </SelectPopoverFallback>
      </ComboboxList>
    </SelectPopover>
  )
}
