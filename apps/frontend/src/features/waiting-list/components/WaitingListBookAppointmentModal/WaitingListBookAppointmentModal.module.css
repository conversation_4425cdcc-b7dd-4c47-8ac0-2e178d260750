.wrapper {
  display: flex;
  gap: 16px;
}

.subInfo {
  display: flex;
  gap: 12px;
}

.inputsContainer {
  width: 420px;
  padding-right: 24px;
  border-right: 1px solid var(--color-lev-blue-200);
  display: flex;
  flex-direction: column;
  gap: var(--grid-gap);
}

.calendarContainer {
  width: 628px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: var(--grid-gap);
}

.errorPanel {
  margin: 16px 0;
}
