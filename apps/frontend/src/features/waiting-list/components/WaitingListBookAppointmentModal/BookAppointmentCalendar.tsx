import { addMonths, startOfMonth } from "date-fns"
import { useTranslation } from "react-i18next"

import ClockIllustration from "@leviosa/assets/illustrations/clock.svg?react"
import {
  Calendar,
  ClickableElement,
  Card,
  CardTitle,
} from "@leviosa/components"

import { Loading, Text } from "ui"
import { naiveDate } from "utils/naiveDate"
import useTimeFormatter from "utils/useTimeFormatter"

import { useGetOpenSlotsQuery } from "generated/graphql"

import styles from "./BookAppointmentCalendar.module.css"

export type AppointmentSlot = { fromTime: string; toTime: string }

const EmptyState = ({ message }: { message: string }) => {
  return (
    <div className={styles.emptyState}>
      <ClockIllustration />
      <Text>{message}</Text>
    </div>
  )
}

type BookAppointmentCalendarProps = {
  serviceTypeId?: string
  providerId?: string
  selectedDate: Date
  selectedSlot?: AppointmentSlot
  selectSlot: (slot: AppointmentSlot) => void
  setSelectedDate: (date: Date) => void
  availableDates: string[]
}

export const BookAppointmentCalendar = ({
  serviceTypeId,
  providerId,
  selectedDate,
  setSelectedDate,
  selectedSlot,
  selectSlot,
  availableDates,
}: BookAppointmentCalendarProps) => {
  const format = useTimeFormatter()
  const { t } = useTranslation()

  const { data: slotsData, loading: loadingSlotsData } = useGetOpenSlotsQuery({
    variables: {
      filter: {
        serviceTypeId: serviceTypeId || "",
        fromDate: naiveDate(selectedDate),
        toDate: naiveDate(selectedDate),
        providerId,
      },
    },
    skip: !serviceTypeId || !selectedDate || !providerId,
  })

  const availableDateObject = new Date(selectedDate)
  const startOfCurrentMonth = startOfMonth(new Date())
    .toISOString()
    .split("T")[0]
  const nextAvailableMonth = startOfMonth(addMonths(availableDateObject, 1))
    .toISOString()
    .split("T")[0]

  const previousAvailableMonth = startOfMonth(
    addMonths(availableDateObject, -1)
  )
    .toISOString()
    .split("T")[0]

  const handleMonthChange = (direction: "previous" | "next") => {
    const currentMonth = selectedDate.getMonth()
    const newDate = new Date(selectedDate)
    newDate.setMonth(
      direction === "previous" ? currentMonth - 1 : currentMonth + 1
    )
    setSelectedDate(newDate)
  }

  if (!serviceTypeId) {
    return (
      <EmptyState
        message={t(
          "You need to select a service to see available appointment slots"
        )}
      />
    )
  }

  if (!providerId) {
    return (
      <EmptyState
        message={t(
          "You need to select a provider to see available appointment slots"
        )}
      />
    )
  }

  if (loadingSlotsData && availableDates.length === 0) {
    return (
      <div className={styles.emptyState}>
        <Loading large />
      </div>
    )
  }

  if (availableDates.length === 0) {
    return <EmptyState message={t("This provider is fully booked")} />
  }

  return (
    <>
      <Calendar
        selectedDate={selectedDate}
        nextAvailableMonth={nextAvailableMonth}
        previousAvailableMonth={
          previousAvailableMonth < startOfCurrentMonth
            ? null
            : previousAvailableMonth
        }
        availableDates={availableDates || []}
        onMonthChange={handleMonthChange}
        onDateChange={(date) => setSelectedDate(date)}
        clickableElement={ClickableElement.button}
      />
      <input
        type="hidden"
        name="selectedDate"
        value={selectedDate.toISOString().split("T")[0]}
      />
      <ul className={styles.slotList}>
        {slotsData?.openTimeSlots[0]?.times.map((slot: AppointmentSlot) => (
          <li key={slot.fromTime}>
            <label className={styles.label}>
              <input
                type="radio"
                name="appointmentSlot"
                className={styles.input}
                value={slot.fromTime}
                checked={selectedSlot?.fromTime === slot.fromTime}
                onChange={() => {
                  selectSlot(slot)
                }}
              />
              <Card className={styles.card}>
                <CardTitle>{format(new Date(slot.fromTime))}</CardTitle>
              </Card>
            </label>
          </li>
        ))}
      </ul>
    </>
  )
}
