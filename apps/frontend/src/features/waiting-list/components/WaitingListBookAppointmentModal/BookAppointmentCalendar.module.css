.input {
  appearance: none;
}

.label {
  cursor: pointer;
  text-align: center;
  height: fit-content;
  display: flex;
}

.slotList {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(104px, 1fr));
  gap: 24px;
}

.card {
  padding: 16px;
  min-height: fit-content;
  width: 104px;
  text-align: center;
}

.input:checked + .card {
  background-color: var(--color-brand-background-blue-15);
  color: var(--color-brand-primary-blue);
}

.emptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  height: 100%;
  gap: 16px;
}
