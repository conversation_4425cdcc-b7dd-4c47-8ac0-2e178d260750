import { useState, useEffect } from "react"
import { useTranslation } from "react-i18next"
import { matchPath, useLocation } from "react-router-dom"

import { formatPersonaId } from "@leviosa/utils"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import ProviderSelect from "components/ProviderSelect/ProviderSelect"
import { SelectServiceType } from "features/calendar/components/SelectServiceType/SelectServiceType"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { PrivateRoutes } from "routes/RouteStrings"
import {
  Button,
  FormGrid,
  Modal,
  notification,
  Text,
  Textarea,
  TextWithIcon,
} from "ui"
import { InputWrap } from "ui/components/Layout/FormGrid"
import useDateFormatter from "utils/useDateFormatter"

import {
  namedOperations,
  useCreateEventInstanceInSlotMutation,
  useGetDatesWithOpenSlotsQuery,
} from "generated/graphql"

import {
  BookAppointmentCalendar,
  AppointmentSlot,
} from "./BookAppointmentCalendar"
import styles from "./WaitingListBookAppointmentModal.module.css"

type WaitingListBookAppointmentModalProps = {
  waitingListItem: WaitingListItem | null
  onClose: () => void
}

export const WaitingListBookAppointmentModal = ({
  waitingListItem,
  onClose,
}: WaitingListBookAppointmentModalProps) => {
  const formId = "waitingListBookAppointmentForm"
  const { t } = useTranslation()
  const formatDate = useDateFormatter()
  const { globalState } = useGlobalState()
  const { isNnMode } = globalState
  const { pathname } = useLocation()

  const isInSubjectJournal = matchPath(
    { path: PrivateRoutes.subjectJournal },
    pathname
  )

  const [serviceTypeId, setServiceTypeId] = useState<string | undefined>(
    waitingListItem?.serviceType?.id
  )

  const [providerId, setProviderId] = useState<string | undefined>(
    waitingListItem?.assignee?.id
  )

  const [description, setDescription] = useState<string>(
    waitingListItem?.comment || ""
  )

  const [error, setError] = useState<string | null>(null)

  const [
    createEvent,
    { loading: createEventLoading, error: createEventError },
  ] = useCreateEventInstanceInSlotMutation()

  const { data: datesData } = useGetDatesWithOpenSlotsQuery({
    variables: {
      filter: {
        serviceTypeId: serviceTypeId || "",
        providerId,
      },
    },
    skip: !serviceTypeId || !providerId,
  })

  const userSelectedDate = new Date().toISOString().split("T")[0]

  const [selectedDate, setSelectedDate] = useState<Date>(
    new Date(userSelectedDate)
  )

  const [selectedSlot, setSelectedSlot] = useState<AppointmentSlot | undefined>(
    undefined
  )

  useEffect(() => {
    if (!waitingListItem) return

    // If the waiting list item changes, update the service type and provider
    setServiceTypeId(waitingListItem?.serviceType?.id)
    setProviderId(waitingListItem?.assignee?.id)
  }, [waitingListItem])

  useEffect(() => {
    // If any value changes, reset the error
    setError(null)
  }, [serviceTypeId, providerId, selectedSlot])

  useEffect(() => {
    if (!datesData) return

    // Calculate the first available date from the dates with open time slots
    const firstAvailableDate = datesData.datesWithOpenTimeSlots.filter(
      (d) => d >= userSelectedDate
    ).length
      ? datesData.datesWithOpenTimeSlots
          .filter((d) => d >= userSelectedDate)
          .sort()[0]
      : userSelectedDate

    setSelectedDate(new Date(firstAvailableDate))
  }, [datesData, userSelectedDate])

  const handleClose = () => {
    onClose()
    setError(null)
    setServiceTypeId(undefined)
    setProviderId(undefined)
    setSelectedSlot(undefined)
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    const subjectId = waitingListItem?.subject?.id

    if (!serviceTypeId || !providerId || !subjectId) {
      setError(t("Please fill in all required fields"))
      return
    }

    // We already have a message on the screen if there are no available dates
    if (datesData?.datesWithOpenTimeSlots?.length === 0) {
      return
    }

    if (!selectedSlot) {
      setError(t("Select a time slot to complete booking"))
      return
    }

    createEvent({
      variables: {
        input: {
          serviceTypeId: serviceTypeId || "",
          providerId: providerId || "",
          subjectId: waitingListItem?.subject?.id,
          listId: waitingListItem?.id,
          description,
          fromDate: new Date(selectedSlot.fromTime),
          toDate: new Date(selectedSlot.toTime),
        },
      },
      onCompleted: () => {
        notification.create({
          status: "success",
          message: t(`Appointment booked for {{name}} on {{date}}`, {
            name: !isNnMode ? waitingListItem?.subject?.name : t("subject"),
            date: formatDate(new Date(selectedSlot.fromTime)),
          }),
        })

        onClose()
      },
      refetchQueries: isInSubjectJournal
        ? [namedOperations.Query.GetSubjectJournal]
        : [namedOperations.Query.GetWaitingList],
    })
  }

  return (
    <Modal
      isOpen={!!waitingListItem}
      title={t("Book appointment")}
      onClose={handleClose}
    >
      <div className={styles.wrapper}>
        <div className={styles.inputsContainer}>
          <Panel status="info">
            <PiiSensitive as={Text} size="large" weight="bold">
              {waitingListItem?.subject?.name}
            </PiiSensitive>
            <PiiSensitive className={styles.subInfo}>
              <Text size="small">
                {formatPersonaId(waitingListItem?.subject?.personaId)}
              </Text>

              <Text size="small">{waitingListItem?.subject?.phoneNumber}</Text>
            </PiiSensitive>
          </Panel>
          <FormGrid onSubmit={handleSubmit} id={formId}>
            <InputWrap>
              <SelectServiceType
                serviceTypeId={serviceTypeId}
                required
                hideMessage
                onChange={(value) => setServiceTypeId(value)}
              />
            </InputWrap>
            <InputWrap>
              <ProviderSelect
                name="providerId"
                required
                providerId={waitingListItem?.assignee?.id}
                onChange={(value) => setProviderId(value || undefined)}
              />
            </InputWrap>
            <InputWrap>
              <PiiSensitive
                as={Textarea}
                label={t("Description")}
                rows={3}
                id="description"
                placeholder={t("Some notes")}
                name="description"
                value={description}
                onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                  setDescription(e.target.value)
                }
                autoGrow
              />
            </InputWrap>
          </FormGrid>
        </div>
        <div className={styles.calendarContainer}>
          <BookAppointmentCalendar
            serviceTypeId={serviceTypeId}
            providerId={providerId}
            selectedSlot={selectedSlot}
            selectSlot={(slot) => setSelectedSlot(slot)}
            selectedDate={selectedDate}
            setSelectedDate={(date) => {
              setSelectedDate(date)
              setSelectedSlot(undefined)
            }}
            availableDates={datesData?.datesWithOpenTimeSlots || []}
          />
          {(error || createEventError) && (
            <Panel status="info" className={styles.errorPanel}>
              <TextWithIcon size="small" color="error">
                {error || t("Something went wrong, could not book appointment")}
              </TextWithIcon>
            </Panel>
          )}
        </div>
      </div>

      <div className={styles.footer}>
        <Button variant="clear" onClick={handleClose}>
          {t("Cancel")}
        </Button>
        <Button
          form={formId}
          type="submit"
          variant="filled"
          iconEnd={
            createEventLoading ? <Icon name="loader-4-line" spin /> : undefined
          }
          disabled={createEventLoading}
        >
          {t("Confirm booking")}
        </Button>
      </div>
    </Modal>
  )
}
