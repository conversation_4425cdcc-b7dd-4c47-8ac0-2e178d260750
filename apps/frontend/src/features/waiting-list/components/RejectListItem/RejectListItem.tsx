import { RefetchQueriesInclude } from "@apollo/client"
import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"

import { useMenuStore } from "components/Ariakit/hooks"
import { ButtonMenu } from "components/ButtonMenu/ButtonMenu"
import Icon from "components/Icon/Icon"
import { Button, notification, Text, Textarea } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import {
  ListItemType,
  useDeclineListItemMutation,
  useRejectListItemMutation,
} from "generated/graphql"

import styles from "./RejectListItem.module.css"

type RejectionType = "cancel" | "decline"

type CancelDialogProps = {
  show: boolean
  hideDialog: () => void
  handleCancel: (e: FormEvent<HTMLFormElement>) => void
  title: string
  message: string
  reasonRequired: boolean
  actionButtonLabel: string
  rejectionType: RejectionType | null
}
const CancelDialog = ({
  show,
  hideDialog,
  handleCancel,
  title,
  message,
  reasonRequired,
  actionButtonLabel,
  rejectionType,
}: CancelDialogProps) => {
  const { t } = useTranslation()
  const formId = "cancel-form"

  return (
    <Dialog
      title={title}
      isOpen={show}
      contentClassName={styles.dialog}
      onClose={hideDialog}
      closeOnClickOutside
      actions={
        <>
          <Button onClick={hideDialog} variant="clear">
            {t("Close")}
          </Button>
          <Button form={formId} variant="filled" type="submit">
            {actionButtonLabel}
          </Button>
        </>
      }
    >
      <form id={formId} className={styles.form} onSubmit={handleCancel}>
        <Text>{message}</Text>
        <Textarea
          name="reason"
          label={
            rejectionType === "cancel"
              ? t("Reason for cancelling")
              : t("Reason for declining")
          }
          autoGrow
          required={reasonRequired}
          onKeyDown={(e) => {
            if (e.key === "Enter" && (e.metaKey || e.ctrlKey)) {
              e.preventDefault()
              e.currentTarget.form?.dispatchEvent(
                new Event("submit", { bubbles: true, cancelable: true })
              )
            }
          }}
        />
      </form>
    </Dialog>
  )
}

type RejectListItemProps = {
  id: string
  type: ListItemType
  buttonLabel?: string
  refetchQueries?: RefetchQueriesInclude
}

export const RejectListItem = ({
  id,
  type,
  buttonLabel,
  refetchQueries = [],
}: RejectListItemProps) => {
  const { t } = useTranslation()
  const menu = useMenuStore({})
  const [showDialog, setShowDialog] = useState<RejectionType | null>(null)

  const [declineListItem] = useDeclineListItemMutation()
  const [rejectListItem] = useRejectListItemMutation()

  const handleCancel = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const reason = formData.get("reason") as string

    if (showDialog === "cancel") {
      // Subject cancelled
      declineListItem({
        variables: {
          input: {
            id,
            listItemType: type,
            reason,
          },
        },
        onCompleted: () => {
          setShowDialog(null)
          notification.create({
            status: "success",
            message:
              type === ListItemType.Referral
                ? t("Referral has been cancelled")
                : t("Request has been cancelled"),
          })
        },
        onError: () => {
          notification.create({
            status: "error",
            message:
              type === ListItemType.Referral
                ? t("Error when cancelling referral, please try again")
                : t("Error when cancelling request, please try again"),
          })
        },
        refetchQueries,
      })
    } else if (showDialog === "decline") {
      // Request declined by organization
      rejectListItem({
        variables: {
          input: {
            id,
            listItemType: type,
            reason,
          },
        },
        onCompleted: () => {
          setShowDialog(null)
          notification.create({
            status: "success",
            message:
              type === ListItemType.Referral
                ? t("Referral has been declined")
                : t("Request has been declined"),
          })
        },
        onError: () => {
          notification.create({
            status: "error",
            message:
              type === ListItemType.Referral
                ? t("Failed to decline the referral, please try again")
                : t("Failed to decline the request, please try again"),
          })
        },
        refetchQueries,
      })
    }
  }

  const options: { value: RejectionType; label: string }[] = [
    {
      value: "decline",
      label:
        type === ListItemType.Referral
          ? t("Decline referral")
          : t("Decline request"),
    },
    {
      value: "cancel",
      label: t("Subject cancelled"),
    },
  ]

  return (
    <>
      <ButtonMenu
        menuStore={menu}
        options={options}
        onSelect={(value) => {
          if (value === "cancel" || value === "decline") {
            setShowDialog(value)
          }
        }}
        icon={<Icon name="close-line" />}
        menuClassName={styles.menu}
      >
        {buttonLabel}
      </ButtonMenu>
      <CancelDialog
        show={showDialog !== null}
        hideDialog={() => setShowDialog(null)}
        handleCancel={handleCancel}
        rejectionType={showDialog}
        reasonRequired={
          showDialog === "decline" && type === ListItemType.Referral
        }
        title={
          type === ListItemType.Referral
            ? t("Decline referral")
            : t("Remove request")
        }
        message={
          type === ListItemType.Referral
            ? t("Please add a reason for declining the referral")
            : t("Are you sure you want to remove the request?")
        }
        actionButtonLabel={
          type === ListItemType.Referral ? t("Decline") : t("Remove")
        }
      />
    </>
  )
}
