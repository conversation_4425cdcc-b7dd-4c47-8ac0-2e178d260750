import { useTranslation } from "react-i18next"

import Panel from "components/Panel/Panel"
import { InboundData } from "features/subject-journal/components/InboundData/InboundData"
import { Modal } from "ui"

import { useGetReferralByIdQuery } from "generated/graphql"

import styles from "./ReferralModal.module.css"

type ReferralModalProps = {
  id: string
  onClose: () => void
}

export default function ReferralModal({ id, onClose }: ReferralModalProps) {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "waitingList",
  })
  const { data, error } = useGetReferralByIdQuery({
    variables: {
      filter: {
        ids: [id],
      },
    },
  })

  const referral = data?.listItems?.items?.[0] || null

  if (referral?.__typename !== "ReferralItem") return null

  return (
    <Modal isOpen onClose={onClose} contentClassName={styles.modal}>
      {referral && (
        <InboundData
          {...referral.inboundData}
          open
          setOpen={() => undefined}
          headerClassName={styles.header}
        />
      )}
      {error && <Panel status="error">{tRoutes("referralModalError")}</Panel>}
    </Modal>
  )
}
