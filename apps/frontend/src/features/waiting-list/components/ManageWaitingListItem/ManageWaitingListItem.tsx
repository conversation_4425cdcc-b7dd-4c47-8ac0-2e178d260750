import { addYears } from "date-fns"
import { useMemo, useState } from "react"
import { useTranslation } from "react-i18next"

import { Menu, MenuButton, MenuItem, MenuProvider } from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { Button, ButtonText, Text } from "ui"
import { isTypename } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  UpcomingEventFragmentFragment,
  useGetSubjectEventsQuery,
} from "generated/graphql"

import { RejectListItem } from "../RejectListItem/RejectListItem"
import { WaitingListLinkAppointmentModal } from "../WaitingListLinkAppointmentModal/WaitingListLinkAppointmentModal"
import { WaitingListItem } from "../WaitingListPage/WaitingListPage"
import styles from "./ManageWaitingListItem.module.css"

type ManageWaitingListItemProps = {
  waitingListItem: WaitingListItem
  onBookAppointment: () => void
  refetchQueries: string[]
}

export const ManageWaitingListItem = ({
  waitingListItem,
  onBookAppointment,
  refetchQueries,
}: ManageWaitingListItemProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })
  const dateFormat = useDateFormatter()
  const timeFormat = useTimeFormatter()

  const [eventToLink, setEventToLink] =
    useState<UpcomingEventFragmentFragment | null>(null)

  const subjectId = waitingListItem.subject.id

  const menuStore = useMenuStore()

  const queryVariables = useMemo(
    () => ({
      inputFilter: {
        participantSubjectId: [subjectId],
        fromDate: new Date(waitingListItem.createdAt), // fetch event from the date the waiting list item was created
        toDate: addYears(new Date(), 5), // Setting toDate 5 years from now to have some timeframe
        includeCanceled: false,
      },
      limit: 3, // We only need the first 3 events
    }),
    [subjectId]
  )

  const { data: subjectEventsData } = useGetSubjectEventsQuery({
    variables: queryVariables,
  })

  const existingEvents = subjectEventsData?.eventInstances || []

  return (
    <>
      <div className={styles.wrapper}>
        {existingEvents.length > 0 ? (
          <MenuProvider store={menuStore}>
            <MenuButton
              store={menuStore}
              variant="filled"
              iconEnd={<Icon name="arrow-down-s-line" />}
            >
              {tRoutes("linkAppointment")}
            </MenuButton>
            <Menu>
              {existingEvents.map((event) => {
                const eventProviderNameList =
                  event.participants
                    .filter(isTypename("ParticipantProvider"))
                    .map((participant) => participant.provider.name) || []

                const eventProviderNames =
                  eventProviderNameList.join(", ") || tRoutes("noProvider")
                const fromDateObj = new Date(event.fromDate)
                const toDateObj = new Date(event.toDate)
                const dateAndTimeLabel = `${dateFormat(fromDateObj, {
                  dateStyle: "full",
                })}, ${timeFormat(fromDateObj)} - ${timeFormat(toDateObj)}`
                return (
                  <MenuItem
                    key={event.id}
                    className={styles.menuItem}
                    onClick={() => {
                      setEventToLink(event)
                    }}
                  >
                    <ButtonText>{eventProviderNames}</ButtonText>
                    <Text size="small" secondary>
                      {dateAndTimeLabel}
                    </Text>
                  </MenuItem>
                )
              })}
              <MenuItem
                className={styles.menuItem}
                onClick={() => {
                  onBookAppointment()
                }}
              >
                <ButtonText>{tRoutes("bookAppointmentButton")}</ButtonText>
              </MenuItem>
            </Menu>
          </MenuProvider>
        ) : (
          <Button
            variant="filled"
            onClick={() => {
              onBookAppointment()
            }}
          >
            {tRoutes("bookAppointmentButton")}
          </Button>
        )}

        <Tooltip
          tooltipContent={tRoutes("removeFromWaitingListButtonTooltip")}
          placement="top"
          focusable={false}
        >
          <RejectListItem
            id={waitingListItem.id}
            type={waitingListItem.itemType}
            refetchQueries={refetchQueries}
          />
        </Tooltip>
      </div>
      {eventToLink && (
        <WaitingListLinkAppointmentModal
          waitingListItemId={waitingListItem.id}
          listItemType={waitingListItem.itemType}
          eventInstance={eventToLink}
          subject={waitingListItem.subject}
          onClose={() => setEventToLink(null)}
        />
      )}
    </>
  )
}
