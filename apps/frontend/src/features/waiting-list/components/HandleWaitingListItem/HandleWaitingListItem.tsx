import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import { Button, notification } from "ui"

import {
  ListItemType,
  namedOperations,
  useAcceptListItemMutation,
} from "generated/graphql"

import { RejectListItem } from "../RejectListItem/RejectListItem"
import styles from "./HandleWaitingListItem.module.css"

const refetchQueries = [
  namedOperations.Query.GetWaitingList,
  namedOperations.Query.GetWaitingListItemsCount,
  namedOperations.Query.ProviderBox,
]

type HandleWaitingListItemProps = {
  id: string
  type: ListItemType
  subjectName: string
}

export const HandleWaitingListItem = ({
  id,
  type,
  subjectName,
}: HandleWaitingListItemProps) => {
  const { t } = useTranslation()

  const [acceptWaitingListItem] = useAcceptListItemMutation({
    variables: { input: { id, listItemType: type } },
    refetchQueries,
    onCompleted: () => {
      notification.create({
        status: "success",
        message: t("{{name}} was accepted to the waiting list", {
          name: !isNnMode ? subjectName : t("Subject"),
        }),
      })
    },
    onError: () => {
      notification.create({
        status: "error",
        message: t("Failed to accept {{name}} to the waiting list", {
          name: !isNnMode ? subjectName : t("subject"),
        }),
      })
    },
  })

  const { globalState } = useGlobalState()
  const { isNnMode } = globalState

  const handleItem = () => {
    acceptWaitingListItem()
  }

  return (
    <div className={styles.wrapper}>
      <Tooltip
        tooltipContent={t("Accept to waiting list")}
        placement="top"
        unmountOnHide
      >
        <Button
          icon={<Icon name="check-line" />}
          variant="filled"
          onClick={handleItem}
        />
      </Tooltip>
      <Tooltip
        tooltipContent={t("Remove request")}
        placement="top"
        unmountOnHide
      >
        <RejectListItem id={id} type={type} refetchQueries={refetchQueries} />
      </Tooltip>
    </div>
  )
}
