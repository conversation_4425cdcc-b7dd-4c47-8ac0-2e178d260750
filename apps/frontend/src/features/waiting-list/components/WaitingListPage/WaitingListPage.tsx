import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, NavLink, Outlet } from "react-router-dom"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Restricted from "features/authentication/components/Restricted/Restricted"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import CreateWaitingListItemModalModal from "features/waiting-list/components/WaitingListPage/CreateWaitingListItemModal"
import { RouteStrings } from "routes/RouteStrings"
import { Button, ButtonGroup, Heading } from "ui"
import ExtractByTypename from "utils/ExtractByTypename"

import {
  PermissionKey,
  useGetWaitingListItemsCountQuery,
  WaitingListItemFragment,
} from "generated/graphql"

import styles from "./WaitingListPage.module.css"

export type WaitingListItem = ExtractByTypename<
  WaitingListItemFragment,
  "AppointmentRequestItem" | "ReferralItem"
>

export default function WaitingListPage() {
  const { t } = useTranslation("routes", { keyPrefix: "waitingList" })
  const [showAddModal, setShowAddModal] = useState(false)
  const { globalData } = useGlobalState()
  const actor = globalData.actor

  // Get count of waiting list items for unassigned and assigned to me
  const { data } = useGetWaitingListItemsCountQuery({
    variables: {
      actorId: actor.id,
    },
  })

  const unassignedCount = data?.unassigned.totalCount || 0
  const assignedToMeCount = data?.myItems.totalCount || 0

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListView}
      fallback={<UnauthorizedPage />}
    >
      <div className={styles.pageWrap}>
        <div className={styles.pageHeader}>
          <Heading size="large">{t("mainHeading")}</Heading>
          <ButtonGroup aria-label="Page">
            <Button
              as={NavLink}
              to={generatePath(RouteStrings.waitingList, {
                pageNumber: 1,
              })}
              end
            >
              {t("filterAll")}
            </Button>
            <Button
              as={NavLink}
              to={generatePath(RouteStrings.waitingList, {
                pageNumber: 1,
                view: "unassigned",
              })}
            >
              {t("filterUnassignedCount", {
                count: unassignedCount,
              })}
            </Button>
            <Button
              as={NavLink}
              to={generatePath(RouteStrings.waitingList, {
                pageNumber: 1,
                view: "me",
              })}
            >
              {t("filterAssignedCount", {
                count: assignedToMeCount,
              })}
            </Button>
          </ButtonGroup>

          <div className={styles.toolbar}>
            {/* Commenting search out for now since it's not supported yet */}
            {/* <search>
            <Input
              label={t("Search")}
              hideLabel
              hideMessage
              iconStart={<Icon name="search-line" />}
              type="text"
              placeholder="Search"
              size="small"
            />
          </search> */}

            {/* Commenting filter button out for now since its not supported yet 
          <Button
            icon={<Icon name="filter-line" />}
            aria-label={t("Filter")}
          ></Button> */}
            <Restricted
              to={PermissionKey.ListsWaitingListEdit}
              fallback={
                <Tooltip tooltipContent={t("addNewButtonDisabledTooltip")}>
                  <Button icon={<Icon name="add-line" />} disabled={true}>
                    {t("addNewButton")}
                  </Button>
                </Tooltip>
              }
            >
              <Button
                icon={<Icon name="add-line" />}
                onClick={() => setShowAddModal(true)}
              >
                {t("addNewButton")}
              </Button>
            </Restricted>
          </div>
        </div>
        <CreateWaitingListItemModalModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
        />
        <Outlet />
      </div>
    </Restricted>
  )
}
