import { useTranslation } from "react-i18next"
import z from "zod"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import ProviderSelect from "components/ProviderSelect/ProviderSelect"
import SubjectSelect from "components/SubjectSelect/SubjectSelect"
import { SelectServiceType } from "features/calendar/components/SelectServiceType/SelectServiceType"
import { SelectTeam } from "features/calendar/components/SelectTeam/SelectTeam"
import PrioritySelect from "features/waiting-list/components/PrioritySelect/PrioritySelect"
import { Button, FormGrid, Modal, notification, Textarea } from "ui"
import { InputWrap } from "ui/components/Layout/FormGrid"
import { ModalProps } from "ui/components/Modal/Modal"

import {
  ListItemPriority,
  useCreateAppointmentRequestItemMutation,
  namedOperations,
} from "generated/graphql"

import { NO_PRIORITY } from "../PrioritySelect/PriorityIcon"
import styles from "./CreateWaitingListItemModal.module.css"

const schema = z.object({
  subjectId: z.string(),
  assigneeId: z.string().transform((v) => v || null),
  priority: z
    .string()
    .nullable()
    .transform((v) =>
      v && v !== NO_PRIORITY ? (v as ListItemPriority) : null
    ),
  comment: z.string().transform((v) => v || null),
  teamId: z.string().transform((v) => v || null),
  serviceTypeId: z.string().transform((v) => v || null),
})

export default function CreateWaitingListItemModalModal({
  isOpen,
  onClose,
}: Omit<ModalProps, "title">) {
  const formId = "addWaitingListForm"

  const { t } = useTranslation()
  const { globalState, globalData } = useGlobalState()
  const { recentSubjectInteractions } = globalData.actor
  const { isNnMode } = globalState

  const [createItem, { loading, error }] =
    useCreateAppointmentRequestItemMutation()

  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {
    e.preventDefault()
    if (loading) return
    const formData = new FormData(e.currentTarget)
    const input = {
      subjectId: formData.get("subjectId"),
      assigneeId: formData.get("assigneeId"),
      priority: formData.get("priority"),
      comment: formData.get("comment"),
      teamId: formData.get("teamId"),
      serviceTypeId: formData.get("serviceTypeId"),
    }
    const parsed = schema.parse(input)

    createItem({
      variables: {
        input: parsed,
      },
      refetchQueries: [
        namedOperations.Query.GetWaitingList,
        namedOperations.Query.GetWaitingListItemsCount,
      ],
      onCompleted: (data) => {
        const subjectName = data?.createAppointmentRequestItem?.subject?.name
        notification.create({
          status: "success",
          message: t("{{name}} was added to the waiting list", {
            name: !isNnMode && subjectName ? subjectName : t("Subject"),
          }),
        })
      },
      onError: () => {
        notification.create({
          status: "error",
          message: t("Failed to add subject to the waiting list"),
        })
      },
    }).then(() => {
      onClose?.()
    })
  }

  return (
    <Modal
      title={t("Add to waiting list")}
      isOpen={isOpen}
      onClose={onClose}
      contentClassName={styles.modal}
      footer={
        <footer className={styles.footer}>
          <Button
            variant="filled"
            type="submit"
            form={formId}
            icon={loading && <Icon name="loader-4-line" spin />}
          >
            {loading ? t("Adding to waiting list") : t("Add to waiting list")}
          </Button>
          <Button onClick={onClose} variant="clear" disabled={loading}>
            {t("cancel")}
          </Button>
        </footer>
      }
    >
      <FormGrid onSubmit={handleSubmit} id={formId}>
        <InputWrap>
          <SubjectSelect
            required
            name="subjectId"
            selectedSubjectId={recentSubjectInteractions[0]?.subject.id}
          />
        </InputWrap>
        <InputWrap>
          <ProviderSelect name="assigneeId" />
        </InputWrap>
        <InputWrap>
          <PrioritySelect name="priority" />
        </InputWrap>
        <InputWrap>
          <SelectTeam />
        </InputWrap>
        <InputWrap>
          <SelectServiceType useDefaultServiceType />
        </InputWrap>

        <Textarea label={t("comment")} name="comment" />

        {error && <Panel status="error">{error.message}</Panel>}
      </FormGrid>
    </Modal>
  )
}
