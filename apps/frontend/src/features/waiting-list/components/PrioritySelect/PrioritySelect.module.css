.selectItem {
  display: flex;
  align-items: center;
  gap: 12px;
  color: var(--color-text);
}

.selectItem svg {
  font-size: 20px;
}

.unset {
  padding-left: 46px;
}

.critical,
.high {
  color: var(--color-critical);
}
.medium {
  /* COMEBACK var(--color-orange-secondary-text) */
  color: #7b431e;
}
.low,
.veryLow {
  color: #2126e2;
}

.noPriority {
  color: var(--color-brand-primary-green);
}
