import { SelectPopoverProps, useSelectContext } from "@ariakit/react"
import c from "classnames"
import { useTranslation } from "react-i18next"

import { SelectItem, SelectLabel, SelectPopover } from "components/Ariakit"
import {
  Select,
  SelectArrow,
  SelectProps,
  SelectProvider,
} from "components/Ariakit/Select/Select"
import { Text } from "ui"

import { ListItemPriority } from "generated/graphql"

import { PriorityIcon, priorityIcons } from "./PriorityIcon"
import styles from "./PrioritySelect.module.css"

export default function PrioritySelect(props: SelectProps) {
  const { t } = useTranslation()
  return (
    <SelectProvider defaultValue="">
      <SelectLabel>{t("Priority")}</SelectLabel>
      <Select {...props}>
        <Value />
        <SelectArrow />
      </Select>
      <PrioritySelectPopover />
    </SelectProvider>
  )
}

export const PrioritySelectPopover = ({
  sameWidth = true,
  ...rest
}: SelectPopoverProps) => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ListItemPriority",
  })

  return (
    <SelectPopover sameWidth={sameWidth} {...rest}>
      {Object.entries(priorityIcons).map(([priority]) => (
        <SelectItem
          key={priority}
          className={c(styles.selectItem)}
          value={priority}
        >
          <PriorityIcon
            priority={priority as ListItemPriority}
            className={styles.selectItem}
          />
          {tEnum(priority)}
        </SelectItem>
      ))}
    </SelectPopover>
  )
}

const Value = () => {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ListItemPriority",
  })
  const { t } = useTranslation()
  const store = useSelectContext()
  const value = store?.useState().value.toString() as ListItemPriority
  if (!value) return <Text secondary>{t("Select priority")}</Text>

  return (
    <div className={c(styles.selectItem)}>
      <PriorityIcon priority={value} />
      {tEnum(value as ListItemPriority)}
    </div>
  )
}
