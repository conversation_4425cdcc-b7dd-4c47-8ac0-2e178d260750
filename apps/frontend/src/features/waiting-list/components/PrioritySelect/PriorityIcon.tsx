import c from "classnames"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import { ListItemPriority } from "generated/graphql"

import styles from "./PrioritySelect.module.css"

type PriorityIconTypes = Extract<
  IconName,
  | "arrow-up-double-line"
  | "arrow-up-s-line"
  | "equal-line"
  | "arrow-down-s-line"
  | "arrow-down-double-line"
  | "inbox-bullet-line"
>
export const NO_PRIORITY = "NO_PRIORITY"

export const priorityIcons: Record<string, PriorityIconTypes> = {
  [ListItemPriority.Critical]: "arrow-up-double-line",
  [ListItemPriority.High]: "arrow-up-s-line",
  [ListItemPriority.Medium]: "equal-line",
  [ListItemPriority.Low]: "arrow-down-s-line",
  [ListItemPriority.VeryLow]: "arrow-down-double-line",
  NO_PRIORITY: "inbox-bullet-line",
}

export const styleMap: Record<string, string> = {
  [ListItemPriority.Critical]: styles.critical,
  [ListItemPriority.High]: styles.high,
  [ListItemPriority.Medium]: styles.medium,
  [ListItemPriority.Low]: styles.low,
  [ListItemPriority.VeryLow]: styles.veryLow,
  NO_PRIORITY: styles.noPriority,
}

type PriorityIconProps = {
  className?: string
  priority: ListItemPriority | null
}

export const PriorityIcon = ({ className, priority }: PriorityIconProps) => {
  const iconName = priorityIcons[priority || NO_PRIORITY]

  return (
    <Icon
      name={iconName}
      className={c(className, styleMap[priority || NO_PRIORITY])}
    />
  )
}
