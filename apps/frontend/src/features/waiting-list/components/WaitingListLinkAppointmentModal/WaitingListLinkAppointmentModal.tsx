import { Trans, useTranslation } from "react-i18next"

import { Icon } from "@leviosa/components"

import { UpcomingEventCard } from "features/dashboard/components/SubjectUpcomingEvents/UpcomingEventCard"
import { Button, Modal, notification, Text } from "ui"

import {
  ListItemType,
  namedOperations,
  UpcomingEventFragmentFragment,
  useLinkListItemToEventMutation,
  WaitingListItemSubjectFragmentFragment,
} from "generated/graphql"

import styles from "./WaitingListLinkAppointmentModal.module.css"

type WaitingListLinkAppointmentModalProps = {
  eventInstance: UpcomingEventFragmentFragment
  waitingListItemId: string
  listItemType: ListItemType
  subject: WaitingListItemSubjectFragmentFragment
  onClose: () => void
}

export const WaitingListLinkAppointmentModal = ({
  eventInstance,
  waitingListItemId,
  listItemType,
  subject,
  onClose,
}: WaitingListLinkAppointmentModalProps) => {
  const { t } = useTranslation()
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "waitingList",
  })

  const [linkListItemToEvent, { loading }] = useLinkListItemToEventMutation()

  const handleLink = () => {
    if (loading) return

    linkListItemToEvent({
      variables: {
        input: {
          id: waitingListItemId,
          eventInstanceId: eventInstance.id,
          listItemType,
        },
      },
      onCompleted: () => {
        notification.create({
          message: tRoutes("successLinkingItemToEvent"),
          status: "success",
        })
        onClose()
      },
      onError: () => {
        notification.create({
          message: tRoutes("errorLinkingItemToEvent"),
          status: "error",
        })
      },
      refetchQueries: [namedOperations.Query.GetWaitingList],
    })
  }

  return (
    <Modal isOpen={true} title={tRoutes("linkAppointment")} onClose={onClose}>
      <div className={styles.wrap}>
        <Text>
          <Trans
            i18nKey={tRoutes("linkAppointmentToWaitingListEntry", {
              subjectName: subject.name,
            })}
            components={{ bold: <b /> }}
          />
        </Text>

        <ul>
          <UpcomingEventCard eventInstance={eventInstance} />
        </ul>
        <div className={styles.footer}>
          <Button onClick={onClose}>{t("doCancel")}</Button>
          <Button
            variant="filled"
            onClick={handleLink}
            iconEnd={loading ? <Icon name="loader-4-line" spin /> : null}
          >
            {tRoutes("link")}
          </Button>
        </div>
      </div>
    </Modal>
  )
}
