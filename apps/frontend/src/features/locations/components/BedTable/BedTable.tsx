import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { Button, Table } from "ui"

import { Building } from "../BuildingOverview/BuildingOverview"
import tableStyles from "../LocationTable.module.css"

type BedTableProps = {
  beds: Building["rooms"]["rooms"][0]["beds"]["beds"]
  onDelete: (bedId: string) => void
}

export const BedTable = ({ beds, onDelete }: BedTableProps) => {
  const { t } = useTranslation()

  if (beds.length === 0) return null

  return (
    <Table className={tableStyles.table}>
      <thead>
        <tr>
          <th>{t("Label")}</th>
          <th>{t("Type")}</th>
          <th>{t("Delete")}</th>
        </tr>
      </thead>
      <tbody>
        {beds.map((bed) => (
          <tr key={bed.id}>
            <td>{bed.label}</td>
            <td>{bed.bedType}</td>
            <td>
              <Tooltip tooltipContent={t("Remove bed")}>
                <Button
                  icon={<Icon name="delete-bin-line" />}
                  onClick={() => onDelete(bed.id)}
                  variant="clear"
                />
              </Tooltip>
            </td>
          </tr>
        ))}
      </tbody>
    </Table>
  )
}
