import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { Button, notification } from "ui"

import {
  useCreateCorridorMutation,
  useCreateRoomMutation,
} from "generated/graphql"

import { CorridorForm } from "../forms/CorridorForm/CorridorForm"
import { RoomForm } from "../forms/RoomForm/RoomForm"
import styles from "./BuildingOverview.module.css"

type CreateRoomToBuildingProps = {
  buildingId: string
}

export const CreateFormsForBuilding = ({
  buildingId,
}: CreateRoomToBuildingProps) => {
  const [isFormState, setIsFormState] = useState<
    "Buttons" | "CorridorForm" | "RoomToBuildingForm"
  >("Buttons")
  const { t } = useTranslation()

  const navigate = useNavigate()

  const [createRoom, { loading: loadingCreateRoom, error: createRoomError }] =
    useCreateRoomMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("Room has been updated"),
            status: "success",
            maxWidth: "500px",
          })
        }

        setIsFormState("Buttons")
      },
    })

  const [
    createCorridor,
    { loading: loadingCreateCorridor, error: updateBuildingError },
  ] = useCreateCorridorMutation({
    onCompleted: (data) => {
      if (data) {
        notification.create({
          message: t("Corridor has been created"),
          status: "success",
          maxWidth: "500px",
        })
      }

      setIsFormState("Buttons")

      navigate(
        generatePath(RouteStrings.locations, {
          buildingId: buildingId,
          corridorId: data.createCorridor.id,
        })
      )
    },
  })

  if (isFormState === "Buttons")
    return (
      <div className={styles.addFormButtons}>
        <Button
          onClick={() => {
            setIsFormState("CorridorForm")
          }}
        >
          {t("Add Corridor")}
        </Button>
        <Button onClick={() => setIsFormState("RoomToBuildingForm")}>
          {t("Add Room to Building")}
        </Button>
      </div>
    )

  if (isFormState === "RoomToBuildingForm")
    return (
      <RoomForm
        onSubmit={(data) => {
          createRoom({
            variables: {
              input: {
                ...data,
                capacity: data.capacity || 0,
                buildingId,
              },
            },
          })
        }}
        onCancel={() => setIsFormState("Buttons")}
        loading={loadingCreateRoom}
        error={createRoomError}
      />
    )

  return (
    <CorridorForm
      loading={loadingCreateCorridor}
      error={updateBuildingError}
      onSubmit={({ label }) => {
        createCorridor({
          variables: {
            input: {
              buildingId: buildingId,
              label: label,
            },
          },
        })
      }}
      onCancel={() => setIsFormState("Buttons")}
    />
  )
}
