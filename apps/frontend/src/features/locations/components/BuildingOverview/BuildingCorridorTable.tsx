import { useTranslation } from "react-i18next"
import { generatePath, NavLink } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { Table, Text } from "ui"
import TrLink from "ui/components/Table/TrLink"

import tableStyles from "../LocationTable.module.css"
import { Building } from "./BuildingOverview"

type BuildingCorridorTableProps = {
  building: Building
  activeCorridor?: Building["corridors"]["corridors"][0]
}

export const BuildingCorridorTable = ({
  activeCorridor,
  building,
}: BuildingCorridorTableProps) => {
  const { t } = useTranslation()

  const { corridors } = building

  if (corridors.corridors.length === 0) return null

  return (
    <>
      <Table className={tableStyles.table}>
        <thead>
          <tr>
            <th>{t("Corridor")}</th>
            <th>{t("Rooms")}</th>
            <th>{t("Beds")}</th>
          </tr>
        </thead>
        <tbody>
          {corridors.corridors.map(
            ({
              id,
              label,
              rooms: { count: roomsCount },
              beds: { count: bedsCount },
            }) => (
              <TrLink key={id} data-is-active={id === activeCorridor?.id}>
                <td>
                  <Text
                    as={NavLink}
                    to={generatePath(RouteStrings.locations, {
                      buildingId: building.id,
                      corridorId: id,
                    })}
                    size="large"
                  >
                    {label}
                  </Text>
                  <br />
                </td>
                <td>{roomsCount}</td>
                <td>{bedsCount}</td>
              </TrLink>
            )
          )}
        </tbody>
      </Table>
      <br />
    </>
  )
}
