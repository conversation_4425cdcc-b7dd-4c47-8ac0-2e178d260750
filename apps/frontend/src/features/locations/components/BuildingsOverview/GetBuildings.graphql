fragment BedFields on Bed {
  id
  label
  bedType
}

fragment RoomFields on Room {
  id
  label
  roomType
  capacity
  beds {
    count
    beds {
      ...BedFields
    }
  }

  corridor {
    id
  }
}

query GetBuildings {
  locations(locationType: BUILDING) {
    ... on Building {
      id
      label
      address {
        addressLine1
        addressLine2
        city
        country
        postalCode
        region
      }

      corridors {
        count
        corridors {
          id
          label

          rooms {
            count
            rooms {
              ...RoomFields
            }
          }

          beds {
            count
            beds {
              ...BedFields
            }
          }
        }
      }

      beds {
        count
        beds {
          ...BedFields
        }
      }

      rooms {
        count
        rooms {
          ...RoomFields
        }
      }
    }
  }
}
