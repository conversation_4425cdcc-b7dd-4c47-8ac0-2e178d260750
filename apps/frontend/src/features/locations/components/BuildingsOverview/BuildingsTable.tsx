import { useTranslation } from "react-i18next"
import { generatePath, NavLink } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { Table, Text } from "ui"
import TrLink from "ui/components/Table/TrLink"

import { Building } from "../BuildingOverview/BuildingOverview"
import tableStyles from "../LocationTable.module.css"

type BuildingsTableProps = {
  buildings: Building[]
  activeBuilding?: Building
}

export const BuildingsTable = ({
  buildings,
  activeBuilding,
}: BuildingsTableProps) => {
  const { t } = useTranslation()

  if (buildings.length === 0) return null

  return (
    <Table className={tableStyles.table}>
      <thead>
        <tr>
          <th>{t("Buildings")}</th>
          <th>{t("Rooms")}</th>
          <th>{t("Beds")}</th>
        </tr>
      </thead>
      <tbody>
        {buildings.map(
          ({
            id,
            label,
            address,
            rooms: { count: roomsCount },
            beds: { count: bedsCount },
          }) => (
            <TrLink key={id} data-is-active={id === activeBuilding?.id}>
              <td>
                <Text
                  as={NavLink}
                  to={generatePath(RouteStrings.locations, {
                    buildingId: id,
                  })}
                  size="large"
                >
                  {label}
                </Text>
                <br />
                <Text size="small">
                  {address.addressLine1}
                  {address.addressLine2 ? `, ${address.addressLine2},` : ", "}
                  {address.city}
                </Text>
              </td>
              <td>{roomsCount}</td>
              <td>{bedsCount}</td>
            </TrLink>
          )
        )}
      </tbody>
    </Table>
  )
}
