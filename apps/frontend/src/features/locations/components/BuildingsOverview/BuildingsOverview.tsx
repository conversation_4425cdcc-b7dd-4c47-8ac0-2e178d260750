import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, notification } from "ui"
import { isTypename } from "utils/isTypename"

import {
  GetBuildingsQuery,
  namedOperations,
  useCreateBuildingMutation,
} from "generated/graphql"

import { Building } from "../BuildingOverview/BuildingOverview"
import { LocationBuildingForm } from "../forms/LocationBuildingForm/LocationBuildingForm"
import { BuildingsTable } from "./BuildingsTable"

type BuildingsOverviewProps = {
  locations: GetBuildingsQuery["locations"]
  activeBuilding?: Building
}

export const BuildingsOverview = ({
  locations,
  activeBuilding,
}: BuildingsOverviewProps) => {
  const [showForm, setShowForm] = useState(false)

  const { t: tLocation } = useTranslation("routes", {
    keyPrefix: "locations",
  })

  const navigate = useNavigate()

  const { t } = useTranslation()

  const [createBuilding, { loading, error: createBuildingError }] =
    useCreateBuildingMutation({
      onCompleted: (data) => {
        if (data) {
          notification.create({
            message: t("buildingCreated"),
            status: "success",
            maxWidth: "500px",
          })
        }

        setShowForm(false)

        navigate(
          generatePath(RouteStrings.locations, {
            buildingId: data.createBuilding.id,
          })
        )
      },
      // We can't get an update list of buildings from the
      // from the response so we need to refetch the query
      refetchQueries: [namedOperations.Query.GetBuildings],
    })

  const buildings = locations.filter(isTypename("Building"))

  const orgBuildingCount = buildings.length

  const orgRoomCount = buildings.reduce(
    (acc, building) => acc + building.rooms.count,
    0
  )

  const orgBedCount = buildings.reduce(
    (acc, building) => acc + building.beds.count,
    0
  )

  return (
    <>
      <Heading size="large">{t("Buildings")}</Heading>
      <br />
      <p>
        {tLocation("organisationTitle", {
          buildingCount: orgBuildingCount,
          roomsCount: orgRoomCount,
          bedsCount: orgBedCount,
        })}
      </p>
      <br />

      <BuildingsTable buildings={buildings} activeBuilding={activeBuilding} />

      <br />

      {!showForm ? (
        <Button onClick={() => setShowForm(true)}>{t("Add Building")}</Button>
      ) : (
        <LocationBuildingForm
          onSubmit={(data) => {
            const { label, ...rest } = data

            createBuilding({
              variables: { input: { label: label, address: { ...rest } } },
            })
          }}
          formData={{}}
          onCancel={() => setShowForm(false)}
          error={createBuildingError}
          loading={loading}
        />
      )}
    </>
  )
}
