import { IconName } from "@leviosa/assets"

export type BlockTypeKey =
  | "paragraph"
  | "h1"
  | "h2"
  | "h3"
  | "bullet"
  | "number"
  | "quote"
  | "check"
  | "code"

export const blockTypes: Record<
  BlockTypeKey,
  { label: string; ariaLabel: string; icon: IconName }
> = {
  paragraph: {
    label: "Normal",
    ariaLabel: "Format text as normal",
    icon: "paragraph",
  },
  h1: {
    label: "Heading 1",
    ariaLabel: "Format text as heading 1",
    icon: "heading1",
  },
  h2: {
    label: "Heading 2",
    ariaLabel: "Format text as heading 2",
    icon: "heading2",
  },
  h3: {
    label: "Heading 3",
    ariaLabel: "Format text as heading 3",
    icon: "heading3",
  },
  // h4: {
  //   label: "Heading 4",
  //   ariaLabel: "Format text as heading 4",
  //   icon: "heading4",
  // },
  // h5: {
  //   label: "Heading 5",
  //   ariaLabel: "Format text as heading 5",
  //   icon: "heading5",
  // },
  // h6: {
  //   label: "Heading 6",
  //   ariaLabel: "Format text as heading 6",
  //   icon: "heading6",
  // },
  bullet: {
    label: "Bullet List",
    ariaLabel: "Format text as bullet list",
    icon: "list-bullet",
  },
  number: {
    label: "Numbered List",
    ariaLabel: "Format text as numbered list",
    icon: "list-number",
  },
  quote: {
    label: "Quote",
    ariaLabel: "Format text as quote",
    icon: "quote",
  },
  check: {
    label: "Check List",
    ariaLabel: "Format text as check list",
    icon: "check-line",
  },
  code: {
    label: "Code Block",
    ariaLabel: "Format text as code block",
    icon: "code-line",
  },
} as const
