import { INSERT_TABLE_COMMAND } from "@lexical/table"
import { LexicalEditor } from "lexical"
import { useState } from "react"

import { Modal, FormFooter, Input, Checkbox, Button } from "ui"

import styles from "./EditorToolbar.module.css"

interface InsertTableModalProps {
  editor: LexicalEditor
  isOpen: boolean
  onClose: () => void
}

export function InsertTableModal({
  editor,
  isOpen,
  onClose,
}: InsertTableModalProps) {
  const [tableProps, setTableProps] = useState({
    columns: 4,
    rows: 3,
    useHeader: true,
  })
  const { rows, columns, useHeader } = tableProps

  const handleInsert = () => {
    editor.dispatchCommand(INSERT_TABLE_COMMAND, {
      columns: String(columns),
      rows: String(rows),
      includeHeaders: useHeader
        ? { rows: true, columns: false }
        : { rows: false, columns: false },
    })
    onClose()
  }

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      footer={
        <FormFooter>
          <Button onClick={onClose}>Cancel</Button>
          <Button
            disabled={!(rows > 0 && rows <= 500 && columns > 0 && columns <= 8)}
            onClick={handleInsert}
          >
            Insert table
          </Button>
        </FormFooter>
      }
    >
      <div className={styles.insertTableModal}>
        <Input
          label="Rows"
          type="number"
          value={rows}
          onChange={({ currentTarget }) =>
            setTableProps({
              ...tableProps,
              rows: Number(currentTarget.value),
            })
          }
        />
        <Input
          label="Columns"
          type="number"
          value={columns}
          onChange={({ currentTarget }) =>
            setTableProps({
              ...tableProps,
              columns: Number(currentTarget.value),
            })
          }
        />
      </div>
      <Checkbox
        label="Include header?"
        checked={useHeader}
        onChange={() =>
          setTableProps({
            ...tableProps,
            useHeader: !useHeader,
          })
        }
      />
    </Modal>
  )
}
