import { <PERSON>u<PERSON><PERSON>on } from "@ariakit/react"
import { $createCodeNode } from "@lexical/code"
import { $isLinkNode } from "@lexical/link"
import {
  $isListNode,
  INSERT_CHECK_LIST_COMMAND,
  INSERT_ORDERED_LIST_COMMAND,
  INSERT_UNORDERED_LIST_COMMAND,
  ListNode,
  REMOVE_LIST_COMMAND,
} from "@lexical/list"
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import {
  $createHeadingNode,
  $createQuoteNode,
  $isHeadingNode,
  HeadingTagType,
} from "@lexical/rich-text"
import { $setBlocksType } from "@lexical/selection"
import { $isTableSelection } from "@lexical/table"
import {
  $findMatchingParent,
  $getNearestNodeOfType,
  mergeRegister,
} from "@lexical/utils"
import {
  $createParagraphNode,
  $getSelection,
  $isRangeSelection,
  $isRootOrShadowRoot,
  CAN_REDO_COMMAND,
  CAN_UNDO_COMMAND,
  COMMAND_PRIORITY_CRITICAL,
  FORMAT_TEXT_COMMAND,
  INDENT_CONTENT_COMMAND,
  OUTDENT_CONTENT_COMMAND,
  REDO_COMMAND,
  SELECTION_CHANGE_COMMAND,
  UNDO_COMMAND,
} from "lexical"
import { useCallback, useEffect, useState } from "react"

import {
  Menu,
  MenuItem,
  MenuItemCheckbox,
  MenuProvider,
  SelectArrow,
  SelectItem,
} from "components/Ariakit"
import {
  Toolbar,
  ToolbarItem,
  ToolbarSelect,
  ToolbarSeparator,
} from "components/Ariakit/Toolbar/Toolbar"
import Icon from "components/Icon/Icon"
import { clearFormatting } from "features/rich-text-editor/EditorToolbar/utils"
import { Text } from "ui"
import { IS_APPLE } from "utils/environment"

import { INSERT_EXCALIDRAW_COMMAND } from "../Excalidraw/ExcalidrawPlugin"
import {
  SPEECH_TO_TEXT_COMMAND,
  SUPPORT_SPEECH_RECOGNITION,
} from "../plugins/SpeechToTextPlugin"
import styles from "./EditorToolbar.module.css"
import { EditorToolbarButton } from "./EditorToolbarButton"
import { InsertTableModal } from "./InsertTableModal"
import { BlockTypeKey, blockTypes } from "./blockTypes"
import { getSelectedNode } from "./getSelectedNode"

interface EditorToolbar {
  isTemplateEditor: boolean
  onAddAttachment?: () => void
  isSpeechToText: boolean
  setIsSpeechToText: (value: boolean) => void
}

export default function EditorToolbar({
  isTemplateEditor,
  onAddAttachment,
  isSpeechToText,
  setIsSpeechToText,
}: EditorToolbar): JSX.Element {
  const [editor] = useLexicalComposerContext()

  const [state, setState] = useState({
    blockType: "paragraph" as BlockTypeKey,
    isLink: false,
    isBold: false,
    isItalic: false,
    isUnderline: false,
    isStrikethrough: false,
    isSubscript: false,
    isSuperscript: false,
    canUndo: false,
    canRedo: false,
  })

  const [insertTableModal, setInsertTableModal] = useState(false)

  /**
   * Updates the toolbar state based on the current text selection.
   *
   * This function uses the current selection to determine the formatting
   * options (bold, italic, underline, etc.) and block type (heading, list, etc.)
   * that should be reflected in the toolbar state. It also checks if the
   * selected text is part of a link.
   */
  const $updateToolbar = useCallback(() => {
    const selection = $getSelection()
    if ($isRangeSelection(selection)) {
      const anchorNode = selection.anchor.getNode()
      let element =
        anchorNode.getKey() === "root"
          ? anchorNode
          : $findMatchingParent(anchorNode, (e) => {
              const parent = e.getParent()
              return parent !== null && $isRootOrShadowRoot(parent)
            })

      if (element === null) element = anchorNode.getTopLevelElementOrThrow()

      const elementKey = element.getKey()
      const elementDOM = editor.getElementByKey(elementKey)

      const newState: Partial<typeof state> = {
        isBold: selection.hasFormat("bold"),
        isItalic: selection.hasFormat("italic"),
        isUnderline: selection.hasFormat("underline"),
        isStrikethrough: selection.hasFormat("strikethrough"),
        isSubscript: selection.hasFormat("subscript"),
        isSuperscript: selection.hasFormat("superscript"),
      }

      const node = getSelectedNode(selection)
      newState.isLink = $isLinkNode(node.getParent()) || $isLinkNode(node)

      if (elementDOM !== null) {
        if ($isListNode(element)) {
          const parentList = $getNearestNodeOfType<ListNode>(
            anchorNode,
            ListNode
          )
          newState.blockType = parentList
            ? parentList.getListType()
            : element.getListType()
        } else {
          const type = $isHeadingNode(element)
            ? element.getTag()
            : element.getType()
          if (type in blockTypes) {
            newState.blockType = type as keyof typeof blockTypes
          }
        }
      }
      setState((prevState) => ({ ...prevState, ...newState }))
    }
  }, [editor, state])

  // Figure out whether undo/redo is available and update toolbar
  useEffect(() => {
    return mergeRegister(
      editor.registerUpdateListener(({ editorState }) => {
        editorState.read(() => $updateToolbar())
      }),
      editor.registerCommand<boolean>(
        CAN_UNDO_COMMAND,
        (canUndo) => {
          setState((prevState) => ({ ...prevState, canUndo }))
          return false
        },
        COMMAND_PRIORITY_CRITICAL
      ),
      editor.registerCommand<boolean>(
        CAN_REDO_COMMAND,
        (canRedo) => {
          setState((prevState) => ({ ...prevState, canRedo }))
          return false
        },
        COMMAND_PRIORITY_CRITICAL
      )
    )
  }, [editor, $updateToolbar])

  // Update toolbar when change of (content) selection e.g. cursor moved
  useEffect(() => {
    return editor.registerCommand(
      SELECTION_CHANGE_COMMAND,
      () => {
        $updateToolbar()
        return false
      },
      COMMAND_PRIORITY_CRITICAL
    )
  }, [editor, $updateToolbar])

  return (
    <Toolbar className={styles.toolbar}>
      <EditorToolbarButton
        disabled={!state.canUndo || !editor.isEditable()}
        onClick={() => editor.dispatchCommand(UNDO_COMMAND, undefined)}
        title={IS_APPLE ? "Undo (⌘Z)" : "Undo (Ctrl+Z)"}
        ariaLabel="Undo"
        iconName="arrow-go-back-line"
      />
      <EditorToolbarButton
        disabled={!state.canRedo || !editor.isEditable()}
        onClick={() => editor.dispatchCommand(REDO_COMMAND, undefined)}
        title={IS_APPLE ? "Redo (⌘Y)" : "Redo (Ctrl+Y)"}
        ariaLabel="Redo"
        iconName="arrow-go-forward-line"
      />

      <ToolbarSeparator />

      <ToolbarSelect
        value={state.blockType}
        label={
          <>
            <Icon name={blockTypes[state.blockType].icon} />
            <Text size="small" secondary>
              {blockTypes[state.blockType].label}
            </Text>
          </>
        }
        selectProps={{
          disabled: !editor.isEditable(),
          "aria-label": "Block types",
        }}
      >
        {Object.entries(blockTypes).map(
          ([type, { label, ariaLabel, icon }]) => (
            <SelectItem
              value={type}
              key={type}
              aria-label={ariaLabel}
              className={styles.selectItem}
              onClick={() => {
                switch (type) {
                  case "paragraph":
                    editor.update(() => {
                      const selection = $getSelection()
                      if (
                        $isRangeSelection(selection) ||
                        $isTableSelection(selection)
                      ) {
                        $setBlocksType(selection, () => $createParagraphNode())
                      }
                    })
                    break

                  case "bullet":
                    if (state.blockType !== "bullet") {
                      editor.dispatchCommand(
                        INSERT_UNORDERED_LIST_COMMAND,
                        undefined
                      )
                    } else {
                      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined)
                    }
                    break

                  case "code":
                    if (state.blockType !== "code") {
                      editor.update(() => {
                        const selection = $getSelection()
                        if (selection !== null) {
                          if (selection.isCollapsed()) {
                            $setBlocksType(selection, () => $createCodeNode())
                          } else {
                            const textContent = selection.getTextContent()
                            const codeNode = $createCodeNode()
                            selection.insertNodes([codeNode])

                            const selection2 = $getSelection()
                            if ($isRangeSelection(selection2)) {
                              selection2.insertRawText(textContent)
                            }
                          }
                        }
                      })
                    }
                    break

                  case "number":
                    if (state.blockType !== "number") {
                      editor.dispatchCommand(
                        INSERT_ORDERED_LIST_COMMAND,
                        undefined
                      )
                    } else {
                      editor.dispatchCommand(REMOVE_LIST_COMMAND, undefined)
                    }
                    break

                  case "quote":
                    editor.update(() => {
                      const selection = $getSelection()
                      if (
                        $isRangeSelection(selection) ||
                        $isTableSelection(selection)
                      ) {
                        $setBlocksType(selection, () => $createQuoteNode())
                      }
                    })
                    break

                  case "check":
                    if (state.blockType !== "check") {
                      editor.dispatchCommand(
                        INSERT_CHECK_LIST_COMMAND,
                        undefined
                      )
                    }
                    break

                  case "h1":
                  case "h2":
                  case "h3":
                    editor.update(() => {
                      const selection = $getSelection()
                      if (
                        $isRangeSelection(selection) ||
                        $isTableSelection(selection)
                      ) {
                        $setBlocksType(selection, () =>
                          $createHeadingNode(type as HeadingTagType)
                        )
                      }
                    })
                    break
                }
                setState((prevState) => ({
                  ...prevState,
                  blockType: type as BlockTypeKey,
                }))
              }}
            >
              <Icon name={icon} />
              <span>{label}</span>
            </SelectItem>
          )
        )}
      </ToolbarSelect>

      <ToolbarSeparator />

      <EditorToolbarButton
        disabled={!editor.isEditable()}
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "bold")
        }}
        title={IS_APPLE ? "Bold (⌘B)" : "Bold (Ctrl+B)"}
        ariaLabel={`Format text as bold. Shortcut: ${
          IS_APPLE ? "⌘B" : "Ctrl+B"
        }`}
        iconName="bold"
        isActive={state.isBold}
      />
      <EditorToolbarButton
        disabled={!editor.isEditable()}
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "italic")
        }}
        title={IS_APPLE ? "Italic (⌘I)" : "Italic (Ctrl+I)"}
        ariaLabel={`Format text as italics. Shortcut: ${
          IS_APPLE ? "⌘I" : "Ctrl+I"
        }`}
        iconName="italic"
        isActive={state.isItalic}
      />
      <EditorToolbarButton
        disabled={!editor.isEditable()}
        onClick={() => {
          editor.dispatchCommand(FORMAT_TEXT_COMMAND, "underline")
        }}
        title={IS_APPLE ? "Underline (⌘U)" : "Underline (Ctrl+U)"}
        ariaLabel={`Format text to underlined. Shortcut: ${
          IS_APPLE ? "⌘U" : "Ctrl+U"
        }`}
        iconName="underline"
        isActive={state.isUnderline}
      />

      <MenuProvider>
        <MenuButton
          disabled={!editor.isEditable()}
          aria-label="Formatting options for additional text styles"
          render={<ToolbarItem />}
        >
          <Icon name="font-size" />
          <SelectArrow />
        </MenuButton>
        <Menu portal sameWidth={false}>
          <MenuItem
            aria-label="Indent text"
            onClick={() =>
              editor.dispatchCommand(INDENT_CONTENT_COMMAND, undefined)
            }
            hideOnClick={false}
          >
            <Icon name="indent-increase" />
            <span>Indent</span>
          </MenuItem>
          <MenuItem
            aria-label="Outdent text"
            onClick={() =>
              editor.dispatchCommand(OUTDENT_CONTENT_COMMAND, undefined)
            }
            hideOnClick={false}
          >
            <Icon name="indent-decrease" />
            <span>Outdent</span>
          </MenuItem>
          <MenuItemCheckbox
            name="formatting"
            aria-label="Format text with a strikethrough"
            checked={state.isStrikethrough}
            onClick={() =>
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, "strikethrough")
            }
            hideOnClick={false}
          >
            <Icon name="strikethrough" />
            <span>Strikethrough</span>
          </MenuItemCheckbox>
          <MenuItemCheckbox
            name="formatting"
            value="subscript"
            aria-label="Format text with a subscript"
            checked={state.isSubscript}
            onClick={() =>
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, "subscript")
            }
            hideOnClick={false}
          >
            <Icon name="subscript" />
            <span>Subscript</span>
          </MenuItemCheckbox>
          <MenuItemCheckbox
            name="formatting"
            value="superscript"
            aria-label="Format text with a superscript"
            checked={state.isSuperscript}
            onClick={() =>
              editor.dispatchCommand(FORMAT_TEXT_COMMAND, "superscript")
            }
            hideOnClick={false}
          >
            <Icon name="superscript" />
            <span>Superscript</span>
          </MenuItemCheckbox>
          <MenuItem
            aria-label="Clear all text formatting"
            onClick={() => clearFormatting(editor)}
            hideOnClick={false}
          >
            <Icon name="close-circle-line" />
            <span>Clear Formatting</span>
          </MenuItem>
        </Menu>
      </MenuProvider>
      <ToolbarSeparator />

      {!isTemplateEditor && onAddAttachment && (
        <EditorToolbarButton
          onClick={onAddAttachment}
          title="Add attachments"
          ariaLabel="Add attachments"
          iconName="attachment-line"
          disabled={!editor.isEditable()}
        />
      )}
      <EditorToolbarButton
        onClick={() => setInsertTableModal(true)}
        title="Insert table"
        ariaLabel="Insert table"
        iconName="table-view"
        disabled={!editor.isEditable()}
      />

      {insertTableModal && (
        <InsertTableModal
          editor={editor}
          isOpen={insertTableModal}
          onClose={() => setInsertTableModal(false)}
        />
      )}

      <EditorToolbarButton
        onClick={() =>
          editor.dispatchCommand(INSERT_EXCALIDRAW_COMMAND, undefined)
        }
        title="Insert drawing"
        ariaLabel="Insert Excalidraw"
        iconName="sketching"
        disabled={!editor.isEditable()}
      />

      {SUPPORT_SPEECH_RECOGNITION && (
        <>
          <ToolbarSeparator />
          <EditorToolbarButton
            onClick={() => {
              editor.dispatchCommand(SPEECH_TO_TEXT_COMMAND, !isSpeechToText)
              setIsSpeechToText(!isSpeechToText)
            }}
            title="Speech to text"
            ariaLabel={`${
              isSpeechToText ? "Enable" : "Disable"
            } speech to text`}
            iconName="mic-line"
            isActive={isSpeechToText}
            disabled={!editor.isEditable()}
            className={isSpeechToText ? styles.microphoneActive : undefined}
          />
        </>
      )}
    </Toolbar>
  )
}
