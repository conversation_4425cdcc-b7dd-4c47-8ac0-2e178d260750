import { ToolbarItem } from "@ariakit/react"
import c from "classnames"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import styles from "./EditorToolbar.module.css"

/* For consistent styling, html attributes etc. */
export function EditorToolbarButton({
  disabled = false,
  title,
  iconName,
  ariaLabel,
  onClick,
  className,
  isActive = false,
}: {
  disabled?: boolean
  ariaLabel: string
  title: string
  iconName: IconName
  onClick: () => void
  className?: string
  isActive?: boolean
}): JSX.Element {
  return (
    <ToolbarItem
      disabled={disabled}
      aria-label={ariaLabel}
      className={c(className, {
        [styles.active]: isActive,
      })}
      onClick={onClick}
      title={title}
    >
      <Icon name={iconName} />
    </ToolbarItem>
  )
}
