.toolbar {
  margin-bottom: 1px;
  padding: 4px; /* padding has to sync with editor-shell border radius */
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  overflow: hidden;
  position: sticky;
  top: var(--editor-sticky-toolbar-offset, 0);
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  z-index: 1;
  backdrop-filter: blur(5px);
}

.toolbar > button {
  font-size: 18px;
  padding: 5px 8px;
  border: 0;
  border-radius: 8px;
  box-shadow: none;
  cursor: pointer;
}

/* leave buttons (icons) untouched */
.toolbar > nav {
  font-size: 0.9em;
}

.toolbar > nav:hover,
.toolbar > button:hover {
  background-color: var(--color-neutral-200);
}
.active {
  background-color: var(--color-lev-blue-300);
}
button.active:hover {
  background-color: var(--color-lev-blue-200);
}
.toolbar > nav:active,
.toolbar > button:active {
  background-color: var(--color-neutral-300);
}

.toolbar > button:disabled * {
  cursor: not-allowed;
  opacity: 0.5; /* using opacity b/c color doesn't work */
}
.selectItem.selectItem {
  padding-top: 8px;
  padding-bottom: 8px;

  svg {
    align-self: center;
  }
}

/* For elements that have styling matching Editor's selection */

.toolbar > hr {
  display: block;
  width: 1px;
  height: 16px;
  border: none;
  margin: 0 4px;
  padding: 0;
  background-color: var(--color-gray-30);
}

.microphoneActive {
  animation: mic-pulsate 2s infinite;
}

@keyframes mic-pulsate {
  0% {
    background-color: #ffdcdc;
  }
  50% {
    background-color: #ff8585;
  }
  100% {
    background-color: #ffdcdc;
  }
}

/* Dropdown component */
.dropDownNav > button {
  /* stack label & icon */
  display: flex;
  width: max-content; /* otherwise icon will wrap */
  cursor: pointer;
}

/* Insert table modal */
.insertTableModal {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
}

.insertTableModal input {
  width: 100px;
}
