/* Parses entered URL before creating link. Allows rejecting invalid URL. */
export const linkMatchers = [
  // URL
  (text: string) => {
    const rxeMatch =
      /((https?:\/\/(www\.)?)|(www\.))[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&//=]*)/.exec(
        text
      )
    if (rxeMatch === null) {
      return null
    }
    const fullMatch = rxeMatch[0]

    return {
      index: rxeMatch.index,
      length: fullMatch.length,
      text: fullMatch,
      url: fullMatch.startsWith("http") ? fullMatch : `https://${fullMatch}`,
    }
  },
]
