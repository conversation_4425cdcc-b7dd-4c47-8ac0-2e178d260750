import { $generateHtmlFromNodes } from "@lexical/html"
import { AutoLinkPlugin } from "@lexical/react/LexicalAutoLinkPlugin"
import { CheckListPlugin } from "@lexical/react/LexicalCheckListPlugin"
import { ClearEditorPlugin } from "@lexical/react/LexicalClearEditorPlugin"
import { ContentEditable } from "@lexical/react/LexicalContentEditable"
import { LexicalErrorBoundary } from "@lexical/react/LexicalErrorBoundary"
import { HistoryPlugin } from "@lexical/react/LexicalHistoryPlugin"
import { HorizontalRulePlugin } from "@lexical/react/LexicalHorizontalRulePlugin"
import { LinkPlugin } from "@lexical/react/LexicalLinkPlugin"
import { ListPlugin } from "@lexical/react/LexicalListPlugin"
import { MarkdownShortcutPlugin } from "@lexical/react/LexicalMarkdownShortcutPlugin"
import { OnChangePlugin } from "@lexical/react/LexicalOnChangePlugin"
import { RichTextPlugin } from "@lexical/react/LexicalRichTextPlugin"
import { TabIndentationPlugin } from "@lexical/react/LexicalTabIndentationPlugin"
import { TablePlugin } from "@lexical/react/LexicalTablePlugin"
import { FocusEventHandler, useState } from "react"

import { linkMatchers } from "features/rich-text-editor/linkMatchers"
import { AutoFocusPlugin } from "features/rich-text-editor/plugins/AutoFocusPlugin"

import {
  GlobalDataQuery,
  JournalTemplateFragmentFragment,
  LanguageId,
  SnippetType,
} from "generated/graphql"

import styles from "./Editor.module.css"
import EditorToolbar from "./EditorToolbar/EditorToolbar"
import ExcalidrawPlugin from "./Excalidraw/ExcalidrawPlugin"
import InsertMenuPlugin from "./InsertMenuPlugin/InsertMenuPlugin"
import ReferencePlugin from "./ReferenceNode/ReferencePlugin"
import { useSharedHistoryContext } from "./SharedHistoryContext"
import TableCellActionMenuPlugin from "./Table/TableActionMenu"
import TableCellResizer from "./Table/TableCellResizer"
import { markdownTransformers } from "./markdownTransformers"
// import AutocompletePlugin from "./plugins/AutocompletePlugin"
import ListMaxIndentLevelPlugin from "./plugins/ListMaxIndentLevelPlugin"
import SpeechToTextPlugin from "./plugins/SpeechToTextPlugin"
import TabFocusPlugin from "./plugins/TabFocusPlugin"
import { validateUrl } from "./utils/validateUrl"

export type EditorOnChange = (htmlContent: string) => void

export type EditorProps = {
  onChange?: EditorOnChange
  onBlur?: FocusEventHandler<HTMLDivElement>
  journalEntryId: string
  sectionId: string
  journalTemplates: JournalTemplateFragmentFragment[]
  onAddBillingItem?: () => void
  onAddAttachment?: () => void
  hideBilling?: boolean
  snippets: NonNullable<GlobalDataQuery["actor"]>["snippets"]
}

export default function Editor({
  onChange,
  onBlur,
  onAddBillingItem,
  onAddAttachment,
  journalEntryId,
  sectionId,
  journalTemplates,
  snippets,
}: EditorProps): JSX.Element {
  const { historyState } = useSharedHistoryContext()
  const isTemplateEditor = !journalEntryId
  const [isSpeechToText, setIsSpeechToText] = useState(false)

  // AutoComplete wants dictionary not array
  const snippetsDictionary: Record<string, string> = {}
  snippets.forEach(({ matchText, replaceText, snippetType, languageId }) => {
    if (snippetType === SnippetType.Note && languageId === LanguageId.Is) {
      snippetsDictionary[matchText] = replaceText
    }
  })

  const placeholder = isTemplateEditor
    ? "Start typing here"
    : `Start typing here, type / for insert menu…`

  return (
    <>
      <EditorToolbar
        onAddAttachment={onAddAttachment}
        isTemplateEditor={isTemplateEditor}
        isSpeechToText={isSpeechToText}
        setIsSpeechToText={setIsSpeechToText}
      />

      <div className={styles.container}>
        <ClearEditorPlugin />

        {/* Forces focus on editor when mounted */}
        <AutoFocusPlugin isFocused={true} />

        {/* Captures tab keypress and blocks it from moving focus out of editor */}
        <TabFocusPlugin />

        {/* Undo/redo */}
        <HistoryPlugin externalHistoryState={historyState} />

        {/* Typeahead menu */}
        <InsertMenuPlugin
          journalEntryId={journalEntryId}
          sectionId={sectionId}
          journalTemplates={journalTemplates}
          onAddBillingItem={onAddBillingItem}
          onAddAttachment={onAddAttachment}
        />

        {/* Inline ClinicalCoding */}
        {journalEntryId && <ReferencePlugin />}

        {onChange && (
          <OnChangePlugin
            onChange={(_, editor) => {
              editor.update(() => {
                onChange($generateHtmlFromNodes(editor, null))
              })
            }}
            ignoreSelectionChange
          />
        )}

        <RichTextPlugin
          contentEditable={
            <div className={styles.scroller}>
              {/* .editor is a must for baseTheme.css classes to be matched */}
              <div className="editor" onBlur={onBlur}>
                <ContentEditable
                  className={styles.contentEditable}
                  aria-placeholder={placeholder}
                  lang="is"
                  placeholder={
                    <div className={styles.placeholder}>{placeholder}</div>
                  }
                />
              </div>
            </div>
          }
          ErrorBoundary={LexicalErrorBoundary}
        />

        {/* Table features */}
        <TablePlugin />
        <TableCellResizer />
        <TableCellActionMenuPlugin />

        {/* Excalidraw */}
        <ExcalidrawPlugin />

        {/* Allows tab/shift+tab to indent/outdent */}
        <TabIndentationPlugin />

        {/* Bullet lists */}
        <ListPlugin />
        <ListMaxIndentLevelPlugin maxDepth={3} />
        <CheckListPlugin />

        {/* Lexical native link node handling */}
        <LinkPlugin
          attributes={{ target: "_blank", rel: "noopener noreferrer" }}
          validateUrl={validateUrl}
        />
        {/* Creates proper link from simple text (URL, email etc) - NOTE not handled by Markdown */}
        <AutoLinkPlugin matchers={linkMatchers} />

        <MarkdownShortcutPlugin transformers={markdownTransformers} />
        <HorizontalRulePlugin />

        {/* for evaluation purposes only hence only active on DEV. Breaks InlineClinicalCoding. */}
        {/* {isDev && !isTemplateEditor && Object.keys(snippetsDictionary) && (
          <AutocompletePlugin dictionary={snippetsDictionary} />
        )} */}

        <SpeechToTextPlugin language={LanguageId.Is} />
      </div>
    </>
  )
}
