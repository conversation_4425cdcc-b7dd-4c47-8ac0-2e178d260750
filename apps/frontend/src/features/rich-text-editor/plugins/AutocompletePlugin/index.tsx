import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { $isAtNodeEnd } from "@lexical/selection"
import { mergeRegister } from "@lexical/utils"
import type { BaseSelection, NodeKey, TextNode } from "lexical"
import {
  $createTextNode,
  $getNodeByKey,
  $getSelection,
  $isRangeSelection,
  $isTextNode,
  $setSelection,
  COMMAND_PRIORITY_LOW,
  KEY_ARROW_RIGHT_COMMAND,
  KEY_TAB_COMMAND,
} from "lexical"
import { useCallback, useEffect } from "react"
import { v4 } from "uuid"

import { logException } from "lib/sentry/sentry"

import {
  $createAutocompleteNode,
  AutocompleteNode,
} from "../../nodes/AutocompleteNode"

const HISTORY_MERGE = { tag: "history-merge" }

declare global {
  interface Navigator {
    userAgentData?: {
      mobile: boolean
    }
  }
}

type SearchPromise = {
  dismiss: () => void
  promise: Promise<null | string>
}

export const uuid = v4()

function $search(selection: null | BaseSelection): [boolean, string] {
  if (!$isRangeSelection(selection) || !selection.isCollapsed()) {
    return [false, ""]
  }
  const node = selection.getNodes()[0]
  const anchor = selection.anchor
  // Check siblings?
  if (!$isTextNode(node) || !node.isSimpleText() || !$isAtNodeEnd(anchor)) {
    return [false, ""]
  }
  const word = []
  const text = node.getTextContent()
  let i = node.getTextContentSize()
  let c
  while (i-- && i >= 0 && (c = text[i]) !== " ") {
    word.push(c)
  }
  if (word.length === 0) {
    return [false, ""]
  }
  return [true, word.reverse().join("")]
}

function useQuery(
  dictionary: Record<string, string>
): (searchText: string) => SearchPromise {
  return useCallback(
    (searchText: string) => {
      const server = new AutocompleteServer(dictionary)
      // console.time("query")
      const response = server.query(searchText)
      // console.timeEnd("query")
      return response
    },
    [dictionary]
  )
}

function formatSuggestionText(suggestion: string): string {
  const userAgentData = window.navigator.userAgentData
  const isMobile =
    userAgentData !== undefined
      ? userAgentData.mobile
      : window.innerWidth <= 800 && window.innerHeight <= 600

  return `${suggestion} ${isMobile ? "(SWIPE \u2B95)" : "(TAB)"}`
}

export default function AutocompletePlugin({
  dictionary,
}: {
  dictionary: Record<string, string>
}): JSX.Element | null {
  const [editor] = useLexicalComposerContext()
  const query = useQuery(dictionary)

  useEffect(() => {
    let autocompleteNodeKey: null | NodeKey = null
    let lastMatch: null | string = null
    let lastSuggestion: null | string = null
    let searchPromise: null | SearchPromise = null
    let prevNodeFormat = 0
    function $clearSuggestion() {
      const autocompleteNode =
        autocompleteNodeKey !== null ? $getNodeByKey(autocompleteNodeKey) : null
      if (autocompleteNode !== null && autocompleteNode.isAttached()) {
        autocompleteNode.remove()
        autocompleteNodeKey = null
      }
      if (searchPromise !== null) {
        searchPromise.dismiss()
        searchPromise = null
      }
      lastMatch = null
      lastSuggestion = null
      prevNodeFormat = 0
    }
    function updateAsyncSuggestion(
      refSearchPromise: SearchPromise,
      newSuggestion: null | string
    ) {
      if (searchPromise !== refSearchPromise || newSuggestion === null) {
        // Outdated or no suggestion
        return
      }
      editor.update(() => {
        const selection = $getSelection()
        const [hasMatch, match] = $search(selection)
        if (!hasMatch || match !== lastMatch || !$isRangeSelection(selection)) {
          // Outdated
          return
        }
        const selectionCopy = selection.clone()
        const prevNode = selection.getNodes()[0] as TextNode
        prevNodeFormat = prevNode.getFormat()
        const node = $createAutocompleteNode(
          formatSuggestionText(newSuggestion),
          uuid
        ).setFormat(prevNodeFormat)
        // .setStyle(`font-size: ${toolbarState.fontSize}`)
        autocompleteNodeKey = node.getKey()
        selection.insertNodes([node])
        $setSelection(selectionCopy)
        lastSuggestion = newSuggestion
      }, HISTORY_MERGE)
    }

    function $handleAutocompleteNodeTransform(node: AutocompleteNode) {
      const key = node.getKey()
      if (node.__uuid === uuid && key !== autocompleteNodeKey) {
        // Max one Autocomplete node per session
        $clearSuggestion()
      }
    }
    function handleUpdate() {
      editor.update(() => {
        const selection = $getSelection()
        const [hasMatch, match] = $search(selection)
        if (!hasMatch) {
          $clearSuggestion()
          return
        }
        if (match === lastMatch) {
          return
        }
        $clearSuggestion()
        searchPromise = query(match)
        searchPromise.promise
          .then((newSuggestion) => {
            if (searchPromise !== null) {
              updateAsyncSuggestion(searchPromise, newSuggestion)
            }
          })
          .catch((e) => {
            if (e !== "Dismissed") {
              logException(e)
            }
          })
        lastMatch = match
      }, HISTORY_MERGE)
    }
    function $handleAutocompleteIntent(): boolean {
      if (lastSuggestion === null || autocompleteNodeKey === null) {
        return false
      }
      const autocompleteNode = $getNodeByKey(autocompleteNodeKey)
      if (autocompleteNode === null) {
        return false
      }
      const selection = $getSelection()
      if (!$isRangeSelection(selection)) {
        return false
      }
      const anchor = selection.anchor
      // const focus = selection.focus
      const textNode = $createTextNode(lastSuggestion).setFormat(prevNodeFormat)
      // .setStyle(`font-size: ${toolbarState.fontSize}`)
      autocompleteNode.replace(textNode)
      textNode.selectNext()
      // Remove the matched text
      const node = selection.getNodes()[0] as TextNode
      const textContent = node.getTextContent()
      const newTextContent =
        lastMatch !== null
          ? textContent.slice(0, anchor.offset - lastMatch.length) +
            textContent.slice(anchor.offset)
          : textContent
      node.setTextContent(newTextContent)
      $clearSuggestion()
      return true
    }
    function $handleKeypressCommand(e: Event) {
      if ($handleAutocompleteIntent()) {
        e.preventDefault()
        return true
      }
      return false
    }
    function unmountSuggestion() {
      editor.update(() => {
        $clearSuggestion()
      }, HISTORY_MERGE)
    }

    return mergeRegister(
      editor.registerNodeTransform(
        AutocompleteNode,
        $handleAutocompleteNodeTransform
      ),
      editor.registerUpdateListener(handleUpdate),
      editor.registerCommand(
        KEY_TAB_COMMAND,
        $handleKeypressCommand,
        COMMAND_PRIORITY_LOW
      ),
      editor.registerCommand(
        KEY_ARROW_RIGHT_COMMAND,
        $handleKeypressCommand,
        COMMAND_PRIORITY_LOW
      ),
      // ...(rootElem !== null
      //   ? [addSwipeRightListener(rootElem, handleSwipeRight)]
      //   : []),
      unmountSuggestion
    )
  }, [editor, query /* toolbarState.fontSize */])

  return null
}

/*
 * Simulate an asynchronous autocomplete server (typical in more common use cases like GMail where
 * the data is not static).
 */
class AutocompleteServer {
  DATABASE: Record<string, string>
  LATENCY = 200

  constructor(dictionary: Record<string, string>) {
    this.DATABASE = dictionary
  }

  query = (searchText: string): SearchPromise => {
    let isDismissed = false

    const dismiss = () => {
      isDismissed = true
    }
    const promise: Promise<null | string> = new Promise((resolve, reject) => {
      setTimeout(() => {
        if (isDismissed) {
          // TODO cache result
          return reject("Dismissed")
        }
        const searchTextLength = searchText.length
        if (searchText === "" || searchTextLength < 2) {
          // Changed from 4 to 2
          return resolve(null)
        }
        const char0 = searchText.charCodeAt(0)
        const isCapitalized = char0 >= 65 && char0 <= 90
        const caseInsensitiveSearchText = isCapitalized
          ? String.fromCharCode(char0 + 32) + searchText.substring(1)
          : searchText
        const match = Object.keys(this.DATABASE).find(
          (dictionaryWord) =>
            dictionaryWord.startsWith(caseInsensitiveSearchText) ?? null
        )
        if (match === undefined) {
          return resolve(null)
        }
        // const matchCapitalized = isCapitalized
        //   ? String.fromCharCode(match.charCodeAt(0) - 32) + match.substring(1)
        //   : match
        const autocompleteChunk = this.DATABASE[match]
        if (autocompleteChunk === "") {
          return resolve(null)
        }
        return resolve(autocompleteChunk)
      }, this.LATENCY)
    })

    return {
      dismiss,
      promise,
    }
  }
}
