/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 * Modified Copyright (c) Leviosa ehf.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 */
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import type { LexicalCommand } from "lexical"
import {
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_EDITOR,
  createCommand,
} from "lexical"
import { useEffect, useRef, useState } from "react"

import { LanguageId } from "generated/graphql"

import { voiceCommands_EN } from "./voiceCommands_EN"
import { voiceCommands_IS } from "./voiceCommands_IS"

export const SPEECH_TO_TEXT_COMMAND: LexicalCommand<boolean> = createCommand(
  "SPEECH_TO_TEXT_COMMAND"
)

type SpeechToTextPluginProps = { language: LanguageId }

export const SUPPORT_SPEECH_RECOGNITION: boolean =
  "SpeechRecognition" in window || "webkitSpeechRecognition" in window

function SpeechToTextPlugin({ language }: SpeechToTextPluginProps): null {
  const [editor] = useLexicalComposerContext()
  const [isEnabled, setIsEnabled] = useState<boolean>(false)
  const SpeechRecognition =
    // @ts-expect-error - SpeechRecognition in on window but not GlobalThis (?)
    window.SpeechRecognition || window.webkitSpeechRecognition
  const recognition = useRef<typeof SpeechRecognition | null>(null)

  useEffect(() => {
    if (isEnabled && recognition.current === null) {
      recognition.current = new SpeechRecognition()
      recognition.current.continuous = true
      recognition.current.lang = language
      recognition.current.interimResults = true
      recognition.current.addEventListener(
        "result",
        (event: typeof SpeechRecognition) => {
          const resultItem = event.results.item(event.resultIndex)
          const { transcript } = resultItem.item(0)

          // Only handle final results
          if (!resultItem.isFinal) {
            return
          }

          editor.update(() => {
            const selection = $getSelection()

            if ($isRangeSelection(selection)) {
              // Use different set based on editor language
              const voiceCommands =
                language === LanguageId.Is ? voiceCommands_IS : voiceCommands_EN

              let parsedTranscript = transcript
                // For some reason, NLP will occasionally insert space at beginning which is never used
                .trimStart()
                // Inline replacements
                .replace(voiceCommands.Colon, ": ")
                // notice order so that "comma" doesn't override
                .replace(voiceCommands.SemiColon, "; ")
                .replace(voiceCommands.Comma, ", ")
                .replace(voiceCommands.Period, ". ")
                // Remove these NLP words from transcript, only used to invoke API commands
                .replace(voiceCommands.NewLine, "")
                .replace(voiceCommands.NewParagraph, "")

              // If there is a period at the end of the last sentence, capitalize the first letter and insert space
              const anchorNode = selection.anchor.getNode()
              const anchorOffset = selection.anchor.offset

              let textBeforeSelection = ""

              if (anchorOffset > 0) {
                textBeforeSelection = anchorNode
                  .getTextContent()
                  .slice(0, anchorOffset)
              } else {
                const prevSibling = anchorNode.getPreviousSibling()
                if (prevSibling) {
                  textBeforeSelection = prevSibling.getTextContent()
                }
              }

              const lastSentenceEndsWithPeriod = textBeforeSelection
                .trimEnd()
                .endsWith(".")

              const isBeginningOfFirstParagraph =
                textBeforeSelection === "" &&
                !anchorNode.getPreviousSibling() &&
                !anchorNode.getNextSibling()

              if (lastSentenceEndsWithPeriod || isBeginningOfFirstParagraph) {
                parsedTranscript =
                  parsedTranscript.charAt(0).toUpperCase() +
                  parsedTranscript.slice(1)
              }

              selection.insertText(parsedTranscript)

              // New line or paragraph (never both simultaneously) - is handled by Lexical API
              if ((voiceCommands.NewLine as RegExp).exec(transcript)) {
                selection.insertLineBreak()
              } else if (
                (voiceCommands.NewParagraph as RegExp).exec(transcript)
              ) {
                selection.insertParagraph()
              }
            }
          })
        }
      )
    }

    if (recognition.current) {
      if (isEnabled) {
        recognition.current.lang = language
        recognition.current.start()
      } else {
        recognition.current.stop()
      }
    }

    return () => {
      if (recognition.current !== null) {
        recognition.current.stop()
      }
    }
  }, [SpeechRecognition, editor, isEnabled, language])
  useEffect(() => {
    return editor.registerCommand(
      SPEECH_TO_TEXT_COMMAND,
      (_isEnabled: boolean) => {
        setIsEnabled(_isEnabled)
        return true
      },
      COMMAND_PRIORITY_EDITOR
    )
  }, [editor])

  return null
}

export default (SUPPORT_SPEECH_RECOGNITION
  ? SpeechToTextPlugin
  : () => null) as (props: SpeechToTextPluginProps) => null
