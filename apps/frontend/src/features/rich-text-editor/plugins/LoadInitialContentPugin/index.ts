import { $generateNodesFromDOM } from "@lexical/html"
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { $createParagraphNode, $getRoot, LexicalNode } from "lexical"
import { useEffect } from "react"

type LoadInitialContentPluginProps = { initialContent?: string }

export const LoadInitialContentPlugin = ({
  initialContent,
}: LoadInitialContentPluginProps) => {
  const [editor] = useLexicalComposerContext()

  useEffect(() => {
    if (!initialContent) {
      return
    }

    editor.update(() => {
      const parser = new DOMParser()
      const dom = parser.parseFromString(initialContent, "text/html")
      const nodes = $generateNodesFromDOM(editor, dom).map(
        (node: LexicalNode) => {
          // If the node is a text node, wrap it in a paragraph so we won't crash the editor
          if (node.getType() === "text") {
            const paragraphNode = $createParagraphNode().append(node)
            return paragraphNode
          }
          return node
        }
      )

      // Clear previous content
      $getRoot()
        .getChildren()
        .forEach((child) => child.remove())
      // Append new content
      $getRoot().append(...nodes)
    })
  }, [])
  return null
}
