import type { EditorThemeClasses } from "lexical"

import "./baseTheme.css"

/* 
  Lexical nodes that render html elements are styled as such in editor/index.module.css.
  Nodes that are not html rendered need to be styled through this file which decides classNames and should be matched in baseTheme.css.
*/
const baseTheme: EditorThemeClasses = {
  text: {
    strikethrough: "textStrikethrough",
    // W3C specifically states that <u> should not be used for styling, only semantics. Hence Lexical renders span.
    // https://developer.mozilla.org/en-US/docs/Web/HTML/Element/u
    underline: "textUnderline",
    underlineStrikethrough: "textUnderlineStrikethrough",
    italic: "textItalic",
    bold: "textBold",
  },

  list: {
    // we only support 3 levels of nesting (more depth = more complexity = more prone to bugs)
    checklist: "checklist",
    listitemChecked: "liChecked",
    listitemUnchecked: "liUnchecked",
  },
  // We don't need class for <table> since is styled by html element. But - if it is not included in theme file then <PERSON><PERSON> applies inline styling for a few properties such as bg-color on <th> which is not needed and we want to avoid, thus it is set as empty.
  table: "",
}

export default baseTheme
