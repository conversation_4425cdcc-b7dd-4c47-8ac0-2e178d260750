import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import {
  LexicalTypeaheadMenuPlugin,
  useBasicTypeaheadTriggerMatch,
} from "@lexical/react/LexicalTypeaheadMenuPlugin"
import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin"
import { $getNodeByKey, NodeKey } from "lexical"
import { useEffect } from "react"

import { ReferenceNode } from "features/rich-text-editor/ReferenceNode/ReferenceNode"
import useReferenceMenuState from "features/rich-text-editor/ReferenceNode/useReferenceMenuState"

import {
  ClinicalCodingType,
  useUnlinkClinicalCodingMutation,
} from "generated/graphql"

export class ReferenceCodeTypeaheadOption extends MenuOption {
  reference: string
  id: string
  isAlreadyLinked: boolean

  constructor(label: string, id: string, isAlreadyLinked?: boolean) {
    super(label)
    this.reference = label
    this.id = id
    this.isAlreadyLinked = !!isAlreadyLinked
  }
}
export class CreateReferenceTypeaheadOption extends MenuOption {
  referenceType: ClinicalCodingType

  constructor(type: ClinicalCodingType) {
    super(type)
    this.referenceType = type
  }
}

export default function ReferencePlugin(): JSX.Element | null {
  const [editor] = useLexicalComposerContext()
  const noteId = editor._config.namespace

  const [unlinkClinicalCoding] = useUnlinkClinicalCodingMutation()

  const checkForTriggerMatch = useBasicTypeaheadTriggerMatch("#", {
    minLength: 0,
  })

  useEffect(() => {
    const referenceNodeList = new Map<NodeKey, ReferenceNode>()

    if (!editor.hasNodes([ReferenceNode])) {
      throw new Error("ReferencePlugin: ReferenceNode not registered on editor")
    }

    return editor.registerMutationListener(ReferenceNode, (nodeMutations) => {
      for (const [nodeKey, mutation] of nodeMutations) {
        if (mutation === "created") {
          editor.update(() => {
            const referenceNode = $getNodeByKey<ReferenceNode>(nodeKey)
            if (!referenceNode) return

            // We should check if a link exists, and if not we should create one
            // by calling the mutation

            referenceNodeList.set(nodeKey, referenceNode)
            // console.log("created", nodeKey, referenceNode, referenceNodeList)
          })
        } else if (mutation === "destroyed") {
          // Remove node from list
          const referenceNode = referenceNodeList.get(nodeKey)

          if (referenceNode !== undefined) {
            referenceNodeList.delete(nodeKey)

            const isLinkedInAnotherNode = !![
              ...referenceNodeList.values(),
            ].find((node) => node.__id === referenceNode.__id)

            if (isLinkedInAnotherNode) return

            unlinkClinicalCoding({
              variables: {
                clinicalCodingId: referenceNode.getId(),
                noteId,
              },
            })
          }
        }
      }
    })
  }, [editor])

  const props = useReferenceMenuState()

  return (
    <LexicalTypeaheadMenuPlugin triggerFn={checkForTriggerMatch} {...props} />
  )
}
