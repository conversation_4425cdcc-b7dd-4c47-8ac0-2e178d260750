import {
  DOMConversionMap,
  DOMConversionOutput,
  DOMExportOutput,
  EditorConfig,
  LexicalNode,
  NodeKey,
  SerializedTextNode,
} from "lexical"
import { $applyNodeReplacement, TextNode } from "lexical"

type SerializedReferenceNode = SerializedTextNode & {
  id: string
  version: 1
  referenceType: "clinical-code"
  type: "reference"
}

type ReferenceNodeConstructorArguments = {
  id: string
  text: string
  version: 1
  referenceType: "clinical-code"
  key?: NodeKey
}

/** @noInheritDoc */
export class ReferenceNode extends TextNode {
  __id: string
  __referenceType: "clinical-code"
  __text: string
  __version: 1

  static getType(): string {
    return "reference"
  }

  static clone(node: ReferenceNode): ReferenceNode {
    const id = node.getId()
    const text = node.getTextContent()
    const version = node.getVersion()
    const referenceType = node.getReferenceType()
    const key = node.getKey()

    const clonedNode = new ReferenceNode({
      id,
      text,
      version,
      referenceType,
      key,
    })
    return clonedNode
  }

  constructor({
    id,
    text,
    version,
    referenceType,
    key,
  }: ReferenceNodeConstructorArguments) {
    super(text, key)

    this.__id = id
    this.__referenceType = referenceType
    this.__text = text
    this.__version = version
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: (node: HTMLSpanElement) => {
        return "referenceId" in node.dataset &&
          node.dataset.referenceId &&
          node.dataset.version === "1"
          ? {
              conversion: convertReferenceElement,
              priority: 2,
            }
          : null
      },
    }
  }

  createDOM(config: EditorConfig): HTMLElement {
    const element = super.createDOM(config)
    element.setAttribute("data-reference-id", this.getId())
    element.setAttribute("data-reference-type", "clinical-code")
    element.setAttribute("data-version", "1")
    element.innerText = this.getTextContent()
    return element
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement("span")
    element.setAttribute("data-reference-id", this.getId())
    element.setAttribute("data-reference-type", "clinical-code")
    element.setAttribute("data-version", "1")

    // Prevent unnecessary text updates
    if (element.innerText !== this.getTextContent()) {
      element.innerText = this.getTextContent()
    }
    return { element }
  }

  static importJSON(serializedNode: SerializedReferenceNode): ReferenceNode {
    const node = $createReferenceNode(serializedNode)
    node.setFormat(serializedNode.format)
    node.setDetail(serializedNode.detail)
    node.setMode(serializedNode.mode)
    node.setStyle(serializedNode.style)

    return node
  }

  exportJSON(): SerializedReferenceNode {
    return {
      ...super.exportJSON(),
      version: this.getVersion(),
      id: this.getId(),
      referenceType: this.getReferenceType(),
      type: "reference",
    }
  }

  canInsertTextBefore(): boolean {
    return false
  }
  canInsertTextAfter(): boolean {
    return false
  }
  canMergeWith() {
    return false
  }

  isTextEntity(): true {
    return true
  }

  getId() {
    return this.__id
  }
  getVersion() {
    return this.__version
  }
  getReferenceType() {
    return this.__referenceType
  }
}

/**
 * Generates a ReferenceNode, which is a string following the format of a # followed by some text, eg. #lexical.
 * @param text - The text used inside the ReferenceNode.
 * @returns - The ReferenceNode with the embedded text.
 */
export function $createReferenceNode(
  args: ReferenceNodeConstructorArguments
): ReferenceNode {
  const referenceNode = new ReferenceNode(args)
  referenceNode.setMode("token")
  return $applyNodeReplacement(referenceNode)
}

/**
 * Determines if node is a ReferenceNode.
 * @param node - The node to be checked.
 * @returns true if node is a ReferenceNode, false otherwise.
 */
export function $isReferenceNode(
  node: LexicalNode | null | undefined
): node is ReferenceNode {
  return node instanceof ReferenceNode
}

function convertReferenceElement(domNode: Node): null | DOMConversionOutput {
  if (domNode instanceof HTMLSpanElement && "referenceId" in domNode.dataset) {
    const version = parseInt(domNode.dataset.version || "0")
    const referenceType = domNode.dataset.referenceType
    const id = domNode.dataset.referenceId

    if (!(id && version === 1 && referenceType === "clinical-code")) {
      return null
    }

    const text = domNode.innerText || ""

    if (version === 1 && referenceType === "clinical-code") {
      const node = $createReferenceNode({
        id,
        version,
        referenceType,
        text,
      })
      return { node }
    }
  } else if (domNode instanceof HTMLSpanElement) {
    console.error("no success", domNode)
  }
  return null
}
