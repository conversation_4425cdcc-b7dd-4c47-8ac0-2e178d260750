import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { MenuRenderFn } from "@lexical/react/LexicalTypeaheadMenuPlugin"
import { TextNode } from "lexical"
import { useCallback, useMemo, useState } from "react"
import { createPortal } from "react-dom"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import Popover from "components/Popover/Popover"
import PopoverMenuHeading from "components/Popover/PopoverMenuHeading"
import { PopoverMenuItem } from "components/Popover/PopoverMenuItem"
import { $createReferenceNode } from "features/rich-text-editor/ReferenceNode/ReferenceNode"
import {
  CreateReferenceTypeaheadOption,
  ReferenceCodeTypeaheadOption,
} from "features/rich-text-editor/ReferenceNode/ReferencePlugin"

import {
  ClinicalCodingType,
  LanguageId,
  useGetSubjectClinicalCodesQuery,
  useLinkClinicalCodingMutation,
} from "generated/graphql"

import { $createCreateReferenceNode } from "./CreateReferenceNode"

// At most, 5 suggestions are shown in the popup.
const SUGGESTION_LIST_LENGTH_LIMIT = 5

const orderedCodingTypes = [
  ClinicalCodingType.Diagnosis,
  ClinicalCodingType.Operation,
  ClinicalCodingType.Allergy,
  ClinicalCodingType.Behavior,
  ClinicalCodingType.TreatmentRestriction,
]

function useInitialReferenceStep(queryString: string | null) {
  const [editor] = useLexicalComposerContext()
  const noteId = editor._config.namespace
  const [linkClinicalCoding] = useLinkClinicalCodingMutation()
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ClinicalCoding_CodingType",
  })

  // We should not be doing this here since this creates a dependency on the
  // router. This should be moved to the parent component.
  const { subjectId } = useParams<{ subjectId: string }>()

  const { data: subjectClinicalCodeData } = useGetSubjectClinicalCodesQuery({
    variables: {
      subjectId: subjectId || "",
      languageId: LanguageId.Is,
    },
    fetchPolicy: "cache-first",
  })

  const openClinicalCodes =
    subjectClinicalCodeData?.subject?.clinicalCodings.filter(
      ({ closedAt }) => !closedAt
    ) || []

  const newCodeOptions = useMemo(
    () =>
      orderedCodingTypes.map(
        (result) => new CreateReferenceTypeaheadOption(result)
      ),
    []
  )

  const onSelectOption = useCallback(
    async (
      selectedOption:
        | ReferenceCodeTypeaheadOption
        | CreateReferenceTypeaheadOption,
      nodeToReplace: TextNode | null,
      closeMenu: () => void
    ) => {
      if (selectedOption instanceof ReferenceCodeTypeaheadOption) {
        editor.update(() => {
          const referenceNode = $createReferenceNode({
            id: selectedOption.id,
            // eslint-disable-next-line no-irregular-whitespace
            text: `# ${selectedOption.reference}`,
            version: 1,
            referenceType: "clinical-code",
          })

          if (nodeToReplace) {
            nodeToReplace.replace(referenceNode)
          }
          referenceNode.select()
          closeMenu()
        })
        if (selectedOption.isAlreadyLinked) return

        linkClinicalCoding({
          variables: {
            clinicalCodingId: selectedOption.id,
            noteId,
            languageId: LanguageId.Is,
          },
        })
        return
      } else {
        editor.update(() => {
          const createReferenceNode = $createCreateReferenceNode({
            referenceType: selectedOption.referenceType,
          })

          if (nodeToReplace) {
            nodeToReplace.replace(createReferenceNode)
          }
          closeMenu()
        })
      }
    },
    [editor]
  )

  const options: (
    | ReferenceCodeTypeaheadOption
    | CreateReferenceTypeaheadOption
  )[] = useMemo(
    () =>
      (openClinicalCodes || [])
        .map<CreateReferenceTypeaheadOption | ReferenceCodeTypeaheadOption>(
          (result) => {
            const displayLabel = result.code?.displayLabel || ""
            const id = result.id
            const isAlreadyLinked = result.notes
              .map((n) => n.id)
              .includes(noteId)

            return new ReferenceCodeTypeaheadOption(
              displayLabel,
              id,
              isAlreadyLinked
            )
          }
        )
        .slice(0, SUGGESTION_LIST_LENGTH_LIMIT)
        .concat(newCodeOptions)
        .filter((result) => {
          const optionLabel =
            result instanceof ReferenceCodeTypeaheadOption
              ? result.reference
              : result.referenceType
          return optionLabel.toLowerCase().includes(queryString || "")
        }),
    [openClinicalCodes, queryString]
  )

  const menuRenderFn: MenuRenderFn<
    ReferenceCodeTypeaheadOption | CreateReferenceTypeaheadOption
  > = useCallback(
    (
      anchorElementRef,
      { selectedIndex, selectOptionAndCleanUp, setHighlightedIndex, options }
    ) => {
      const activeCodeOptions = options.filter(
        (o): o is ReferenceCodeTypeaheadOption =>
          o instanceof ReferenceCodeTypeaheadOption
      )
      const createCodeTypes: CreateReferenceTypeaheadOption[] = options.filter(
        (o): o is CreateReferenceTypeaheadOption =>
          o instanceof CreateReferenceTypeaheadOption
      )

      return anchorElementRef.current
        ? createPortal(
            <Popover style={{ width: 300, marginTop: 25 }}>
              {!!activeCodeOptions.length && (
                <>
                  <PopoverMenuHeading>Reference Active Code</PopoverMenuHeading>
                  <ul>
                    {activeCodeOptions.map((option, index) => (
                      <PopoverMenuItem
                        key={option.id}
                        id={option.key}
                        role="option"
                        isActive={index === selectedIndex}
                        onClick={() => {
                          setHighlightedIndex(0)
                          selectOptionAndCleanUp(option)
                        }}
                        onMouseEnter={() => setHighlightedIndex(index)}
                      >
                        {option.reference}
                      </PopoverMenuItem>
                    ))}
                  </ul>
                </>
              )}
              {!!createCodeTypes.length && (
                <>
                  <PopoverMenuHeading>Register New Code</PopoverMenuHeading>
                  <ul>
                    {createCodeTypes.map((option, index) => (
                      <PopoverMenuItem
                        key={option.key}
                        role="option"
                        id={option.key}
                        isActive={
                          index + activeCodeOptions.length === selectedIndex
                        }
                        onClick={() => {
                          setHighlightedIndex(0)
                          selectOptionAndCleanUp(option)
                        }}
                        onMouseEnter={() =>
                          setHighlightedIndex(index + activeCodeOptions.length)
                        }
                      >
                        {tEnum(option.referenceType)}
                      </PopoverMenuItem>
                    ))}
                  </ul>
                </>
              )}
            </Popover>,
            anchorElementRef.current
          )
        : null
    },
    []
  )

  return {
    onSelectOption,
    options,
    menuRenderFn,
  }
}

export default function useReferenceMenuState() {
  const [queryString, setQueryString] = useState<string | null>(null)

  const initialStep = useInitialReferenceStep(queryString)

  return {
    onQueryChange: setQueryString,
    referenceType: null,
    ...initialStep,
  }
}
