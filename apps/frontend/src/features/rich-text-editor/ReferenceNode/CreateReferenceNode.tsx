import { <PERSON>over<PERSON>rovider, PopoverDisclosure } from "@ariakit/react"
import {
  DecoratorNode,
  DOMConversionMap,
  DOMExportOutput,
  LexicalEditor,
  LexicalNode,
  NodeKey,
  SerializedLexicalNode,
} from "lexical"
import { $applyNodeReplacement } from "lexical"
import { createPortal } from "react-dom"

import { Popover } from "components/Ariakit/Popover/Popover"
import ClinicalCodingForm from "features/subject-journal/components/ClinicalCodingForm/ClinicalCodingForm"

import { ClinicalCodingType } from "generated/graphql"

import { $createReferenceNode } from "./ReferenceNode"

type SerializedCreateReferenceNode = SerializedLexicalNode & {
  referenceType: ClinicalCodingType
  type: "create-reference"
}

type CreateReferenceNodeConstructorArguments = {
  referenceType: ClinicalCodingType
  key?: NodeKey
}

/** @noInheritDoc */
export class CreateReferenceNode extends DecoratorNode<JSX.Element> {
  __referenceType: ClinicalCodingType

  static getType(): string {
    return "create-reference"
  }

  static clone(node: CreateReferenceNode): CreateReferenceNode {
    const referenceType = node.__referenceType
    const key = node.__key

    return new CreateReferenceNode({ referenceType, key })
  }

  constructor({ referenceType, key }: CreateReferenceNodeConstructorArguments) {
    super(key)
    this.__referenceType = referenceType
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: () => {
        return null
      },
    }
  }

  createDOM(): HTMLElement {
    const elem = document.createElement("span")
    elem.style.display = "inline-block"
    return elem
  }

  updateDOM(): false {
    return false
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement("span")
    return { element }
  }

  static importJSON(
    serializedNode: SerializedCreateReferenceNode
  ): CreateReferenceNode {
    return $createCreateReferenceNode(serializedNode)
  }

  exportJSON(): SerializedCreateReferenceNode {
    return {
      ...super.exportJSON(),
      referenceType: this.getReferenceType(),
      type: "create-reference",
    }
  }

  canInsertTextBefore(): boolean {
    return true
  }

  canInsertTextAfter(): boolean {
    return true
  }

  getReferenceType() {
    return this.__referenceType
  }

  isKeyboardSelectable(): boolean {
    return true
  }

  isIsolated(): boolean {
    return false
  }

  decorate(editor: LexicalEditor): JSX.Element {
    //const noteId = editor._config.namespace

    const onSelectOption = (id: string, value: string) => {
      editor.update(() => {
        const referenceNode = $createReferenceNode({
          id: id,
          text: `# ${value}`,
          version: 1,
          referenceType: "clinical-code",
        })

        const currentNode = this.getLatest()

        if (currentNode) {
          currentNode.replace(referenceNode)
          referenceNode.select()
        }
      })
    }

    const onCancel = () => {
      editor.update(() => {
        const currentNode = this.getLatest()
        if (currentNode) {
          currentNode.remove()
        }
      })
    }

    return (
      <PopoverProvider animated defaultOpen>
        <PopoverDisclosure
          style={{
            backgroundColor: "rgba(88, 144, 255, 0.15)",
            padding: "4px 8px",
            borderRadius: "4px",
            fontStyle: "italic",
          }}
        >
          {this.getReferenceType()}
        </PopoverDisclosure>
        {createPortal(
          <Popover
            style={{ width: "500px" }}
            hideOnInteractOutside
            hideOnEscape
          >
            <ClinicalCodingForm
              referenceType={this.getReferenceType()}
              onSelectOption={onSelectOption}
              onCancel={onCancel}
              autoFocus={false}
            />
          </Popover>,
          document.body
        )}
      </PopoverProvider>
    )
  }
}

/**
 * Generates a CreateReferenceNode, which is a string following the format of a # followed by some text, eg. #lexical.
 * @param text - The text used inside the CreateReferenceNode.
 * @returns - The CreateReferenceNode with the embedded text.
 */
export function $createCreateReferenceNode(
  args: CreateReferenceNodeConstructorArguments
): CreateReferenceNode {
  return $applyNodeReplacement(new CreateReferenceNode(args))
}

/**
 * Determines if node is a CreateReferenceNode.
 * @param node - The node to be checked.
 * @returns true if node is a CreateReferenceNode, false otherwise.
 */
export function $isCreateReferenceNode(
  node: LexicalNode | null | undefined
): node is CreateReferenceNode {
  return node instanceof CreateReferenceNode
}
