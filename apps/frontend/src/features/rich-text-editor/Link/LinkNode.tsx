import { LinkNode as LexicalLinkNode } from "@lexical/link"

import { logException } from "lib/sentry/sentry"

export const LinkNode = LexicalLinkNode

const SUPPORTED_URL_PROTOCOLS = new Set([
  "http:",
  "https:",
  "mailto:",
  "sms:",
  "tel:",
  "lyfja:",
])

// Overwrite the default method to sanitize the URL in order
// to allow 'lyfja:' protocol. This will allow f<PERSON><PERSON><PERSON><PERSON><PERSON>
// to link to pages inside the Lyfja app.
LinkNode.prototype.sanitizeUrl = (url: string): string => {
  try {
    const parsedUrl = new URL(url)
    // eslint-disable-next-line no-script-url
    if (!SUPPORTED_URL_PROTOCOLS.has(parsedUrl.protocol)) {
      return "about:blank"
    }
  } catch (error) {
    logException(error)
    return url
  }
  return url
}
