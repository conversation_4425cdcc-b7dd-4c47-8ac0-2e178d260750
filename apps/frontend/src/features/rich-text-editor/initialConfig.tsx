import { CodeNode } from "@lexical/code"
import { AutoLinkNode } from "@lexical/link"
import { ListNode, ListItemNode } from "@lexical/list"
import { MarkNode } from "@lexical/mark"
import { OverflowNode } from "@lexical/overflow"
import { HorizontalRuleNode } from "@lexical/react/LexicalHorizontalRuleNode"
import { HeadingNode, QuoteNode } from "@lexical/rich-text"
import { TableNode, TableCellNode, TableRowNode } from "@lexical/table"
import { TextNode } from "lexical"

import { ExcalidrawNode } from "./Excalidraw/ExcalidrawNode"
import { LinkNode } from "./Link/LinkNode"
import { CreateReferenceNode } from "./ReferenceNode/CreateReferenceNode"
import { ReferenceNode } from "./ReferenceNode/ReferenceNode"
import baseTheme from "./baseTheme"
import { AutocompleteNode } from "./nodes/AutocompleteNode"

export const initialConfig = {
  theme: baseTheme,
  nodes: [
    CodeNode,
    HeadingNode,
    // this includes "check" item-list type
    ListNode,
    ListItemNode,
    QuoteNode,
    ReferenceNode, // for inline ClinicalCoding
    CreateReferenceNode,
    AutoLinkNode,
    LinkNode,
    OverflowNode,
    ExcalidrawNode,
    AutocompleteNode,
    HorizontalRuleNode,
    TextNode,
    // DataFieldNode,
    TableNode,
    TableCellNode,
    TableRowNode,
    MarkNode,
  ],
}
