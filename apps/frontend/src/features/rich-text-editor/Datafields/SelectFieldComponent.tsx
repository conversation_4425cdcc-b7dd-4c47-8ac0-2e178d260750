import { useEffect } from "react"

import { Select, SelectItem, SelectPopover } from "components/Ariakit"
import { useSelectStore } from "components/Ariakit/hooks"

import DataFieldStyles from "./DataFieldComponent.module.css"
import { SelectDataField } from "./DataFieldContext"
import SelectFieldComponentStyles from "./SelectFieldComponent.module.css"

type SelectFieldComponentProps = {
  handleSelectChange: (value: string) => void
  inputData?: SelectDataField
}

export const SelectFieldComponent = ({
  handleSelectChange,
  inputData,
}: SelectFieldComponentProps) => {
  const selectStore = useSelectStore({
    defaultValue: inputData?.value,
  })

  const value = selectStore.useState("value")

  useEffect(() => {
    return handleSelectChange(value as string)
  }, [value])

  return (
    <>
      <Select
        store={selectStore}
        className={`${SelectFieldComponentStyles.select} ${DataFieldStyles.input} `}
      />
      <SelectPopover
        portal={true}
        store={selectStore}
        className={SelectFieldComponentStyles.popover}
        sameWidth={false}
      >
        {inputData?.options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectPopover>
    </>
  )
}
