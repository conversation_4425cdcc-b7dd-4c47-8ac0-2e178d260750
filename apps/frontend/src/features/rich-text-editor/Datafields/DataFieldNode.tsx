import {
  DecoratorNode,
  Node<PERSON>ey,
  LexicalNode,
  LexicalEditor,
  $getNode<PERSON>y<PERSON>ey,
  DOMExportOutput,
  DOMConversionOutput,
  DOMConversionMap,
  $applyNodeReplacement,
  Spread,
  SerializedLexicalNode,
} from "lexical"
import { ReactNode } from "react"

import DataFieldComponent from "./DataFieldComponent"

export const elementAttributes = {
  id: "data-field-id",
  value: "data-field-value",
} as const

export type DataFieldAttributes = {
  id: string
  value: string | number | null
  readableValue: string | null
}

export type SerializedDataFieldNode = Spread<
  DataFieldAttributes & {
    version: 1
    type: "dataFieldNode"
  },
  SerializedLexicalNode
>

export class DataFieldNode extends DecoratorNode<ReactNode> {
  __attributes: DataFieldAttributes

  static getType(): string {
    return "dataFieldNode"
  }

  static clone(node: DataFieldNode): DataFieldNode {
    return new DataFieldNode(node.__attributes, node.__key)
  }

  static importDOM(): DOMConversionMap | null {
    return {
      span: (node: HTMLSpanElement) => {
        return "fieldId" in node.dataset && node.dataset.fieldId
          ? {
              conversion: convertDataFieldElement,
              priority: 2,
            }
          : null
      },
    }
  }

  exportDOM(): DOMExportOutput {
    const element = document.createElement("span")
    element.setAttribute(elementAttributes.id, this.getId())
    const value = this.getValue()
    if (value !== null) {
      element.setAttribute(elementAttributes.value, value.toString())
    }
    const readableValue = this.getReadableValue()
    if (readableValue !== null) {
      element.innerHTML = readableValue.toString()
    }
    return { element }
  }

  updateDOM(): false {
    return false
  }

  constructor(attributes: DataFieldAttributes, key?: NodeKey) {
    super(key)
    this.__attributes = attributes
  }

  createDOM(/* config: EditorConfig */): HTMLElement {
    const element = document.createElement("span")
    element.setAttribute(elementAttributes.id, this.getId())
    const value = this.getValue()
    // Set class to 'field' so that the field component can be found
    // element.classList.add(theme.DataFieldNode)
    if (value !== null) {
      element.setAttribute(elementAttributes.value, value.toString())
    }
    return element
  }

  exportJSON(): SerializedDataFieldNode {
    return {
      id: this.getId(),
      value: this.getValue(),
      readableValue: this.getReadableValue(),
      type: "dataFieldNode",
      version: 1,
    }
  }

  setValue(value: string): void {
    this.__attributes.value = value
    this.__attributes.readableValue = value
  }

  onValueChange = (value: string, editor: LexicalEditor) => {
    editor.update(() => {
      const node = $getNodeByKey(this.getKey()) as DataFieldNode
      if (node !== null && $isDataFieldNode(node)) {
        node.setValue(value)
      }
    })
  }

  getId() {
    return this.__attributes.id
  }

  getValue() {
    return this.__attributes.value
  }

  getReadableValue() {
    return this.__attributes.readableValue
  }

  decorate(): ReactNode {
    return (
      <DataFieldComponent
        {...this.__attributes}
        onChange={this.onValueChange}
      />
    )
  }
}

function convertDataFieldElement(domNode: Node): null | DOMConversionOutput {
  if (domNode instanceof HTMLSpanElement && "fieldId" in domNode.dataset) {
    const id = domNode.dataset.fieldId
    if (!id) {
      return null
    }

    let value: string | number | null = null
    const valueRaw = domNode.dataset.value
    if (valueRaw) {
      const valueNumber = Number(valueRaw)
      value = isNaN(valueNumber) ? valueRaw : valueNumber
    }

    const readableValue = domNode.textContent

    const node = $createDataFieldNode({ id, value, readableValue })
    return { node }
  } else if (domNode instanceof HTMLSpanElement) {
    console.error("no success", domNode)
  }
  return null
}

export function $createDataFieldNode(
  attributes: DataFieldAttributes
): DataFieldNode {
  return $applyNodeReplacement(new DataFieldNode(attributes))
}

export function $isDataFieldNode(
  node: LexicalNode | null | undefined
): node is DataFieldNode {
  return node instanceof DataFieldNode
}
