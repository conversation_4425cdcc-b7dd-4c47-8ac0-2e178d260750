s.input,
.measureSize {
  min-width: 35px;
  /* For backwards compatibility */
  height: calc(1.3em - 2px);
  /* Use new lh unit for browsers that support it */
  height: calc(1lh - 2px);
  padding-left: 5px;
  padding-right: 5px;
  padding-bottom: 1px;
  border: 1px solid transparent;
  border-radius: 4px;
  background-color: var(--color-lev-blue-10);
  font-weight: 500;
  color: inherit;
}

.input:focus {
  background-color: white;
  border-color: var(--color-lev-blue);
  box-shadow:
    0 0 4px rgba(13, 16, 57, 0.1),
    0 0 20px rgba(13, 16, 57, 0.2);
}

.dataFieldValue {
  composes: input;
  border: none;
  background-color: var(--color-lev-blue-200);
  min-width: none;
}

.input::-webkit-outer-spin-button,
.input::-webkit-inner-spin-button {
  /* display: none; <- Crashes Chrome on hover */
  -webkit-appearance: none;
  margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

.input[type="number"] {
  -moz-appearance: textfield; /* Firefox */
}

.measureSize {
  position: absolute;
  opacity: 0;
  left: 0;
  top: -2px;
  white-space: pre;
  pointer-events: none;
}
