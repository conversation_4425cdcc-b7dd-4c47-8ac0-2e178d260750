.shell {
  border: 1px solid transparent;
  border-radius: 16px; /* cannot use var b/c curve disrupts toolbar on top */
  --duration: 100ms;
  --easing: cubic-bezier(0.65, 0, 0.35, 1);
  transition:
    border-color var(--duration) var(--easing),
    background-color var(--duration) var(--easing),
    box-shadow var(--duration) var(--easing);
  border-color: var(--color-lev-blue);
  background-color: var(--color-white);
  box-shadow:
    0 0 4px rgba(0, 0, 0, 0.25),
    0 0 20px rgba(13, 16, 57, 0.2);
}

.container {
  background: transparent;
  position: relative;
  cursor: text;
  display: block;
  border-bottom-left-radius: 10px;
  border-bottom-right-radius: 10px;
}

.scroller {
  min-height: 50px;
  border: 0;
  cursor: text;
  display: flex;
  position: relative;
  outline: 0;
  max-width: 100%;
  z-index: 0;
  resize: vertical;
}

.placeholder {
  font-size: 15px;
  color: #999;
  overflow: hidden;
  position: absolute;
  text-overflow: ellipsis;
  top: 0;
  left: 0;
  right: 28px;
  user-select: none;
  white-space: nowrap;
  display: inline-block;
  pointer-events: none;
  padding: 4px 12px 8px;
}

.contentEditable {
  border: 0;
  display: block;
  position: relative;
  outline: 0;
  padding: 4px 12px 8px;
  tab-size: 1;
  min-height: 4em;
}
@media (max-width: 1025px) {
  .contentEditable {
    padding-left: 8px;
    padding-right: 8px;
  }
}
