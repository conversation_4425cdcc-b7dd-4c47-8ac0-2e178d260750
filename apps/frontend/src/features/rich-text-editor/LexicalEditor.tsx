import { LexicalComposer } from "@lexical/react/LexicalComposer"
import { FocusEventHandler } from "react"

import { ErrorBoundary } from "components/ErrorBoundary/ErrorBoundary"
import Panel from "components/Panel/Panel"
import { Heading } from "ui"

import {
  GlobalDataQuery,
  JournalTemplateFragmentFragment,
} from "generated/graphql"

import Editor, { EditorOnChange } from "./Editor"
import styles from "./Editor.module.css"
import { SharedHistoryContext } from "./SharedHistoryContext"
import { TableContext } from "./Table/TableContext"
import { initialConfig } from "./initialConfig"
import { LoadInitialContentPlugin } from "./plugins/LoadInitialContentPugin"

type LexicalEditorProps = {
  id: string
  isEditable?: boolean
  initialContent?: string
  onChange?: EditorOnChange
  onBlur?: FocusEventHandler<HTMLDivElement>
  className?: string
  journalEntryId: string
  sectionId: string
  journalTemplates: JournalTemplateFragmentFragment[]
  onAddBillingItem?: () => void
  onAddAttachment?: () => void
  snippets: NonNullable<GlobalDataQuery["actor"]>["snippets"]
}

export default function LexicalEditor({
  id,
  initialContent,
  onChange,
  onBlur,
  className = "",
  journalEntryId,
  sectionId,
  journalTemplates,
  onAddBillingItem,
  onAddAttachment,
  snippets,
}: LexicalEditorProps): JSX.Element {
  return (
    <ErrorBoundary
      fallback={
        <Panel status="error">
          <Heading>Something went wrong</Heading>
          <p>Try refreshing the page or selecting another text block.</p>
        </Panel>
      }
    >
      <LexicalComposer
        initialConfig={{
          ...initialConfig,
          namespace: id,
          onError: (error) => {
            throw error
          },
        }}
      >
        <TableContext>
          <SharedHistoryContext>
            <div className={`${styles.shell} ${className}`}>
              <LoadInitialContentPlugin initialContent={initialContent} />
              <Editor
                onChange={onChange}
                onBlur={onBlur}
                journalEntryId={journalEntryId}
                sectionId={sectionId}
                journalTemplates={journalTemplates}
                onAddBillingItem={onAddBillingItem}
                onAddAttachment={onAddAttachment}
                snippets={snippets}
              />
            </div>
          </SharedHistoryContext>
        </TableContext>
      </LexicalComposer>
    </ErrorBoundary>
  )
}
