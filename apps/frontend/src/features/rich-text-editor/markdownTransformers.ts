import {
  CHECK_LIST,
  ELEMENT_TRANSFORMERS,
  ElementTransformer,
  MULTILINE_ELEMENT_TRANSFORMERS,
  TEXT_FORMAT_TRANSFORMERS,
  Transformer,
} from "@lexical/markdown"
import {
  $createHorizontalRuleNode,
  $isHorizontalRuleNode,
  HorizontalRuleNode,
} from "@lexical/react/LexicalHorizontalRuleNode"
import { LexicalNode } from "lexical"

// not supported natively by Lexical
const HR: ElementTransformer = {
  dependencies: [HorizontalRuleNode],
  export: (node: LexicalNode) => {
    return $isHorizontalRuleNode(node) ? "***" : null
  },
  regExp: /^(---|\*\*\*|___)\s?$/,
  replace: (parentNode, _1, _2, isImport) => {
    const line = $createHorizontalRuleNode()

    // TODO: Get rid of isImport flag
    if (isImport || parentNode.getNextSibling() != null) {
      parentNode.replace(line)
    } else {
      parentNode.insertBefore(line)
    }

    line.selectNext()
  },
  type: "element",
}

// NOTE Order of text transformers matters:
export const markdownTransformers: Array<Transformer> = [
  HR,
  CHECK_LIST,

  // HEADING, QUOTE
  // UNORDERED_LIST, ORDERED_LIST
  ...ELEMENT_TRANSFORMERS,

  // CODE
  ...MULTILINE_ELEMENT_TRANSFORMERS,

  // INLINE_CODE, STRIKETHROUGH, HIGHLIGHT
  // BOLD_ITALIC_STAR, BOLD_ITALIC_UNDERSCORE, BOLD_STAR, BOLD_UNDERSCORE,
  // ITALIC_STAR, ITALIC_UNDERSCORE
  ...TEXT_FORMAT_TRANSFORMERS,

  // LINK; skipped b/c is handled by AutoLinkPlugin which is customisable
  // ...TEXT_MATCH_TRANSFORMERS,
]
