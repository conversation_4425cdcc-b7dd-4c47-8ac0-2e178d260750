import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuSeparator } from "@ariakit/react"
import { useLexicalComposerContext } from "@lexical/react/LexicalComposerContext"
import { useLexicalEditable } from "@lexical/react/useLexicalEditable"
import {
  $deleteTableColumn,
  $deleteTableRow__EXPERIMENTAL,
  $getTableCellNodeFromLexicalNode,
  $getTableNodeFromLexicalNodeOrThrow,
  $insertTableColumn__EXPERIMENTAL,
  $insertTableRow__EXPERIMENTAL,
  $isTableCellNode,
  $isTableRowNode,
  $isTableSelection,
  getTableElement,
  getTableObserverFromTableElement,
  TableCellHeaderStates,
  TableCellNode,
  TableObserver,
} from "@lexical/table"
import { mergeRegister } from "@lexical/utils"
import {
  $getRoot,
  $getSelection,
  $isRangeSelection,
  COMMAND_PRIORITY_CRITICAL,
  getDOMSelection,
  SELECTION_CHANGE_COMMAND,
} from "lexical"
import { ReactPortal, useCallback, useEffect, useRef, useState } from "react"
import { createPortal } from "react-dom"
import invariant from "tiny-invariant"

import { Icon } from "@leviosa/components"

import { Menu, MenuItem, MenuProvider } from "components/Ariakit"

import styles from "./index.module.css"

/* Menu strategy:
   - Nav is rendered only when Lexical has focus on table cell, its toggle li then becomes visible
   - Nav > menu is conditionally visible based on isMenuOpen
  */
export default function TableActionMenuPlugin(): null | ReactPortal {
  const isEditable = useLexicalEditable()
  const bodyElem = document.body

  const navRef = useRef<HTMLDivElement | null>(null)

  const [tableCellNode, setTableMenuCellNode] = useState<TableCellNode | null>(
    null
  )

  const [editor] = useLexicalComposerContext()
  const [selectionCounts] = useState({ columns: 1, rows: 1 })

  // Register mutation listener for table cell node
  useEffect(() => {
    if (!tableCellNode) return

    return editor.registerMutationListener(
      TableCellNode,
      (node) => {
        if (node.get(tableCellNode.getKey()) === "updated") {
          editor
            .getEditorState()
            // this maintains our reference to the current table cell node being edited
            .read(() => setTableMenuCellNode(tableCellNode.getLatest()))
        }
      },
      { skipInitialization: true }
    )
  }, [editor, tableCellNode])

  const insertTableRowAtSelection = (shouldInsertAfter: boolean) => {
    editor.update(() => {
      $insertTableRow__EXPERIMENTAL(shouldInsertAfter)
    })
  }

  const insertTableColumnAtSelection = (shouldInsertAfter: boolean) => {
    editor.update(() => {
      for (let i = 0; i < selectionCounts.columns; i++) {
        $insertTableColumn__EXPERIMENTAL(shouldInsertAfter)
      }
    })
  }

  const moveMenu = useCallback(() => {
    const navElement = navRef.current
    const selection = $getSelection()
    const nativeSelection = getDOMSelection(editor._window)
    const activeElement = document.activeElement

    function disable() {
      setTableMenuCellNode(null)
    }

    if (selection == null || navElement == null) {
      return disable()
    }

    const rootElement = editor.getRootElement()
    let tableCellParentNodeDOM: HTMLElement | null = null
    let tableObserver: TableObserver | null = null

    if (
      $isRangeSelection(selection) &&
      rootElement !== null &&
      nativeSelection !== null &&
      rootElement.contains(nativeSelection.anchorNode)
    ) {
      const tableCellNodeFromSelection = $getTableCellNodeFromLexicalNode(
        selection.anchor.getNode()
      )

      if (tableCellNodeFromSelection == null) {
        return disable()
      }

      tableCellParentNodeDOM = editor.getElementByKey(
        tableCellNodeFromSelection.getKey()
      )

      if (
        tableCellParentNodeDOM == null ||
        !tableCellNodeFromSelection.isAttached()
      ) {
        return disable()
      }

      const tableNode = $getTableNodeFromLexicalNodeOrThrow(
        tableCellNodeFromSelection
      )
      const tableElement = getTableElement(
        tableNode,
        editor.getElementByKey(tableNode.getKey())
      )
      invariant(tableElement !== null, "Expected to find tableElement in DOM")

      tableObserver = getTableObserverFromTableElement(tableElement)
      setTableMenuCellNode(tableCellNodeFromSelection)
    } else if ($isTableSelection(selection)) {
      const anchorNode = $getTableCellNodeFromLexicalNode(
        selection.anchor.getNode()
      )
      invariant(
        $isTableCellNode(anchorNode),
        "TableSelection anchorNode must be a TableCellNode"
      )
      const tableNode = $getTableNodeFromLexicalNodeOrThrow(anchorNode)
      const tableElement = getTableElement(
        tableNode,
        editor.getElementByKey(tableNode.getKey())
      )
      invariant(tableElement !== null, "Expected to find tableElement in DOM")
      tableObserver = getTableObserverFromTableElement(tableElement)
      tableCellParentNodeDOM = editor.getElementByKey(anchorNode.getKey())
    } else if (!activeElement) {
      return disable()
    }
    if (tableObserver === null || tableCellParentNodeDOM === null) {
      return disable()
    }

    if (!tableObserver || !tableObserver.isSelecting) {
      const { right, top: t1 } = tableCellParentNodeDOM.getBoundingClientRect()
      const { left, top: t2 } = bodyElem.getBoundingClientRect()
      navElement.style.transform = `translate(${right - left}px, ${t1 - t2}px)`
    }
  }, [editor, bodyElem, navRef])

  // Register listeners
  useEffect(() => {
    let timeoutId: ReturnType<typeof setTimeout> | undefined = undefined
    const callback = () => {
      timeoutId = undefined
      editor.getEditorState().read(moveMenu)
    }
    const delayedCallback = () => {
      if (timeoutId === undefined) {
        timeoutId = setTimeout(callback, 0)
      }
      return false
    }

    return mergeRegister(
      editor.registerUpdateListener(delayedCallback),
      editor.registerCommand(
        SELECTION_CHANGE_COMMAND,
        delayedCallback,
        COMMAND_PRIORITY_CRITICAL
      ),
      editor.registerRootListener((rootElement, prevRootElement) => {
        if (prevRootElement) {
          prevRootElement.removeEventListener("mouseup", delayedCallback)
        }
        if (rootElement) {
          rootElement.addEventListener("mouseup", delayedCallback)
          delayedCallback()
        }
      }),
      () => clearTimeout(timeoutId)
    )
  })

  // Close menu when user selects a different table-cell
  const prevTableCellDOM = useRef(tableCellNode)
  useEffect(() => {
    prevTableCellDOM.current = tableCellNode
  }, [prevTableCellDOM, tableCellNode])

  if (!isEditable) return null

  return createPortal(
    <MenuProvider>
      <div
        className={`${styles.nav} ${!tableCellNode && styles.navHide}`}
        ref={navRef}
      >
        <MenuButton aria-label="Table actions">
          <Icon name="arrow-down-s-line" />
        </MenuButton>

        {tableCellNode !== null && (
          <Menu unmountOnHide>
            <MenuItem onClick={() => insertTableRowAtSelection(false)}>
              Insert row above
            </MenuItem>
            <MenuItem onClick={() => insertTableRowAtSelection(true)}>
              Insert row below
            </MenuItem>
            <MenuItem
              onClick={() => {
                editor.update(() => {
                  $deleteTableRow__EXPERIMENTAL()
                })
              }}
            >
              Delete row
            </MenuItem>
            <MenuSeparator />
            <MenuItem onClick={() => insertTableColumnAtSelection(false)}>
              Insert column left
            </MenuItem>
            <MenuItem onClick={() => insertTableColumnAtSelection(true)}>
              Insert column right
            </MenuItem>
            <MenuItem
              onClick={() => {
                const tableNode =
                  $getTableNodeFromLexicalNodeOrThrow(tableCellNode)
                editor.update(() => {
                  $deleteTableColumn(tableNode, 0)
                })
              }}
            >
              Delete column
            </MenuItem>
            <MenuSeparator />
            <MenuItem
              onClick={() => {
                editor.update(() => {
                  // Toggle header state for all cells in the topmost table row
                  const tableTopRow =
                    $getTableNodeFromLexicalNodeOrThrow(
                      tableCellNode
                    ).getChildren()[0]

                  if ($isTableRowNode(tableTopRow)) {
                    const isCurrentlyHeader = !!(
                      tableCellNode.getHeaderStyles() &
                      TableCellHeaderStates.ROW
                    )

                    // Apply new state to all cells
                    tableTopRow.getChildren().forEach((cell) => {
                      if ($isTableCellNode(cell)) {
                        cell.setHeaderStyles(
                          isCurrentlyHeader
                            ? TableCellHeaderStates.NO_STATUS
                            : TableCellHeaderStates.ROW
                        )
                      }
                    })
                  }
                })
              }}
            >
              Toggle table header row
            </MenuItem>
            <MenuItem
              onClick={() => {
                editor.update(() => {
                  $getTableNodeFromLexicalNodeOrThrow(tableCellNode).remove()
                  // Clear table selection
                  editor.update(() => {
                    if (tableCellNode?.isAttached()) {
                      const tableNode =
                        $getTableNodeFromLexicalNodeOrThrow(tableCellNode)
                      const tableElement = getTableElement(
                        tableNode,
                        editor.getElementByKey(tableNode.getKey())
                      )
                      invariant(
                        tableElement !== null,
                        "Expected to find tableElement in DOM"
                      )
                      getTableObserverFromTableElement(
                        tableElement
                      )?.$clearHighlight()
                      tableNode.markDirty()
                      setTableMenuCellNode(tableCellNode.getLatest())
                    }
                    $getRoot().selectStart()
                  })
                })
              }}
            >
              Delete table
            </MenuItem>
          </Menu>
        )}
      </div>
    </MenuProvider>,
    document.body
  )
}
