.cellResizer {
  position: absolute;
}

/* button on the left, menu (ul) on the right. this does not account for menu near right window border but that would be a very rare case since Lexical is always in the center of layout */
.nav {
  position: absolute;
  /* position relative to right/bottom rim of td */
  top: 78px;
  left: -16px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  will-change: transform;
}

.navHide {
  display: none;
}

.nav > button {
  cursor: pointer;
}
