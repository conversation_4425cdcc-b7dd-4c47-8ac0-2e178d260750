import { Excalidraw } from "@excalidraw/excalidraw"
import {
  AppState,
  BinaryFiles,
  ExcalidrawImperativeAPI,
  ExcalidrawInitialDataState,
} from "@excalidraw/excalidraw/types/types"
import { useCallback, useState } from "react"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "ui"

import styles from "./ExcalidrawModal.module.css"

export const useCallbackRefState = () => {
  const [refValue, setRefValue] = useState<ExcalidrawImperativeAPI | null>(null)
  const refCallback = useCallback(
    (value: ExcalidrawImperativeAPI | null) => setRefValue(value),
    []
  )
  return [refValue, refCallback] as const
}

export type ExcalidrawInitialElements = ExcalidrawInitialDataState["elements"]

type ExcalidrawModalProps = {
  closeOnClickOutside?: boolean
  /**
   * The initial set of elements to draw into the scene
   */
  initialElements: ExcalidrawInitialElements
  /**
   * The initial set of elements to draw into the scene
   */
  initialAppState: AppState
  /**
   * The initial set of elements to draw into the scene
   */
  initialFiles: BinaryFiles
  /**
   * Controls the visibility of the modal
   */
  isOpen?: boolean
  /**
   * Callback when closing and discarding the new changes
   */
  onClose: () => void
  /**
   * Completely remove Excalidraw component
   */
  onDelete: () => void
  /**
   * Callback when the save button is clicked
   */
  onSave: (
    elements: ExcalidrawInitialElements,
    appState: Partial<AppState>,
    files: BinaryFiles
  ) => void
}

export default function ExcalidrawModal({
  initialElements,
  initialAppState,
  initialFiles,
  isOpen = false,
  onClose,
  onDelete,
  onSave,
}: ExcalidrawModalProps) {
  const [excalidrawAPI, excalidrawAPIRefCallback] = useCallbackRefState()

  const [elements, setElements] =
    useState<ExcalidrawInitialElements>(initialElements)
  const [files, setFiles] = useState<BinaryFiles>(initialFiles)
  const onChange = (
    els: ExcalidrawInitialElements,
    _: AppState,
    fls: BinaryFiles
  ) => {
    setElements(els)
    setFiles(fls)
  }

  const save = () => {
    if (elements && elements.filter((el) => !el.isDeleted).length > 0) {
      const appState = excalidrawAPI?.getAppState()
      // We only need a subset of the state
      const partialState: Partial<AppState> = {
        exportBackground: appState?.exportBackground,
        exportScale: appState?.exportScale,
        exportWithDarkMode: appState?.theme === "dark",
        isBindingEnabled: appState?.isBindingEnabled,
        isLoading: appState?.isLoading,
        name: appState?.name,
        theme: appState?.theme,
        viewBackgroundColor: appState?.viewBackgroundColor,
        viewModeEnabled: appState?.viewModeEnabled,
        zenModeEnabled: appState?.zenModeEnabled,
        zoom: appState?.zoom,
      }
      onSave(elements, partialState, files)
    } else {
      // delete node if the scene is clear
      onDelete()
    }
  }
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      contentClassName={styles.wrapper}
      onAnimationEnd={
        isOpen
          ? () => {
              // This is a to force Excalidraw to refresh its size
              // If we don't then the cursor will be off since it takes
              // the dimensions of the modal as soon as it renders
              // before the animation is done
              excalidrawAPI?.refresh()
            }
          : undefined
      }
      footer={
        <FormFooter>
          <Button onClick={onDelete}>Discard</Button>
          <Button onClick={save}>Save</Button>
        </FormFooter>
      }
    >
      <Excalidraw
        onChange={onChange}
        excalidrawAPI={excalidrawAPIRefCallback}
        initialData={{
          appState: initialAppState || { isLoading: false },
          elements: initialElements,
          files: initialFiles,
        }}
      />
    </Modal>
  )
}
