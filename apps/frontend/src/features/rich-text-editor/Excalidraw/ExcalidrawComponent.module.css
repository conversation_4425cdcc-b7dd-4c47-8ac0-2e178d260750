.button {
  border: 0;
  padding: 0;
  margin: 0;
  background-color: transparent;
  border-radius: 0px;
  position: relative;
}

.button.selected {
  outline: 2px solid var(--color-lev-blue);
  user-select: none;
}
.imageEditButton {
  border: 1px solid var(--color-neutral-200);
  border-radius: 8px;
  width: 34px;
  height: 28px;
  position: absolute;
  right: 4px;
  top: 4px;
  cursor: pointer;
  user-select: none;
  display: grid;
  place-content: center;
  font-size: 18px;
}

.imageEditButton:hover {
  background-color: var(--color-neutral-200);
}
