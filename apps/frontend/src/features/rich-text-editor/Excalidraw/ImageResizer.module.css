.imageCaptionButton {
  display: block;
  position: absolute;
  bottom: 20px;
  left: 0;
  right: 0;
  width: 30%;
  padding: 10px;
  margin: 0 auto;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  minwidth: 100px;
  color: #fff;
  cursor: pointer;
  user-select: none;
}

.imageResizer {
  display: block;
  width: 7px;
  height: 7px;
  position: absolute;
  background-color: var(--color-lev-blue);
  border: 1px solid #fff;
}

.imageResizer.imageResizerN {
  top: -6px;
  left: 48%;
  cursor: n-resize;
}

.imageResizer.imageResizerNe {
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.imageResizer.imageResizerE {
  bottom: 48%;
  right: -6px;
  cursor: e-resize;
}

.imageResizer.imageResizerSe {
  bottom: -6px;
  right: -6px;
  cursor: nwse-resize;
}

.imageResizer.imageResizerS {
  bottom: -6px;
  left: 48%;
  cursor: s-resize;
}

.imageResizer.imageResizerSw {
  bottom: -6px;
  left: -6px;
  cursor: sw-resize;
}

.imageResizer.imageResizerW {
  bottom: 48%;
  left: -6px;
  cursor: w-resize;
}

.imageResizer.imageResizerNw {
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}
