.editor {
  flex: auto;
  position: relative;
  line-height: 1.5;
  color: var(--color-text);
}

.editor p {
  margin: 0;
  position: relative;
}

.editor :is(p, ul, ol, blockquote, table) + :is(table, hr) {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.editor :is(p, ul, ol, blockquote, table) + :is(p, blockquote) {
  margin-top: 1rem;
}

.editor :is(p, ul, ol, blockquote, table) + :is(ul, ol) {
  margin-top: 0.5rem;
}

.editor :is(h1, h2, h3) + :is(p, ul, ol, blockquote, table) {
  margin-top: 0.5rem;
}

.editor :is(p, ul, ol, blockquote, table) + :is(h1, h2, h3) {
  margin-top: 1.5rem;
}

.editor :is(h1, h2) {
  font-size: 20px;
  font-family: var(--font-serif);
  font-weight: 500;
}

.editor h2 {
  font-size: 18px;
}

.editor h3 {
  font-size: 14px;
  text-transform: uppercase;
  line-height: 1.3;
}

/* remember there is a reset layer */
.editor ul li {
  list-style-type: disc;
}
.editor ul ul li {
  list-style-type: circle;
}
.editor ul ul ul li {
  list-style-type: square;
}
.editor ol li {
  list-style-type: decimal;
}
.editor ol ol li {
  list-style-type: lower-alpha;
}
.editor ol ol ol li {
  list-style-type: lower-roman;
}

.editor.editor li:has(ul, ol) {
  list-style-type: none;
}
.editor .nestedListItem:before,
.editor .nestedListItem:after {
  display: none;
}

.editor li {
  margin: 0 24px;
}

/* Check-list */
.editor ul li.liUnchecked,
.editor ul li.liChecked {
  position: relative;
  margin-left: 8px;
  margin-right: 8px;
  padding-left: 24px;
  padding-right: 24px;
  list-style-type: none;
  outline: none;
}
.editor ul li.liChecked {
  text-decoration: line-through;
}
.editor ul li.liUnchecked::before,
.editor ul li.liChecked::before {
  border-radius: 4px;
  border: 1px solid var(--color-lev-blue);
  background: var(--color-lev-blue-200);

  position: absolute;
  width: 16px;
  height: 16px;
  top: 4px;
  left: 0;
  cursor: pointer;
  display: block;
  border-radius: 4px;
  background-size: cover;
  content: " ";
  transition: border-width 0.2s;
}
.editor ul li.liUnchecked::before,
.editor ul li.liChecked::before {
  /* Focused Element */
  box-shadow: 0px 0px 20px 0px rgba(13, 16, 57, 0.2);
}

.editor ul li.liChecked::before {
  /* background-color: var(--color-lev-blue, #4e00dd); */

  border-width: 8px;
}
.editor ul li.liUnchecked::after,
.editor ul li.liChecked::after {
  content: "";
  cursor: pointer;
  border-color: #fff;
  border-style: solid;
  position: absolute;
  display: block;
  top: 6px;
  width: 5.5px;
  left: 6px;
  right: 7px;
  height: 9px;
  transform: rotate(45deg);
  border-width: 0 1.5px 1.5px 0;
  scale: 0.3;
  opacity: 0;
  transition:
    scale 0.2s,
    opacity 0.2s;
}

.editor ul li.liChecked::after {
  transition-delay: 0.2s;
  opacity: 1;
  scale: 1;
}

.editor blockquote {
  margin: 0;
  color: var(--color-text-secondary);
  border-left: 2px solid var(--color-lev-blue-200);
  padding-left: 1em;
  border-left: 4px solid #ddd;
}

/* We have 2x code blocks; the block type which supports text with line-breaks and inline one which is only that. */
.editor code {
  display: block; /* should span whole width of editor */
  color: var(--color-text-secondary);
  background-color: var(--color-neutral-200);
  padding: 4px 8px;
}

/* inline code  */
.editor p > code {
  display: inline;
  padding: 4px 2px; /* has thinner appearance */
  margin: auto 4px; /* needs space on sides  */
  border-radius: 5px; /* slight visual effect to denote it's inline not block */
}

.editor a {
  color: rgb(33, 111, 219);
  text-decoration: none;
}

.editor a:hover {
  text-decoration: underline;
  cursor: pointer;
}

.editor hr.selected {
  outline: 2px solid var(--color-lev-blue);
  outline-offset: 1px;
  user-select: none;
}

/* doesn't seem pre is used anywhere after cleanup? */
/* .editor pre {
  line-height: 1.1;
  background: #222;
  color: #fff;
  margin: 0;
  padding: 10px;
  font-size: 12px;
  overflow: auto;
  max-height: 400px;
} */

/* Modified styling, originally from Lexical playground */
.editor table {
  border-collapse: collapse;
  border-spacing: 0;
  overflow-y: scroll;
  overflow-x: scroll;
  table-layout: fixed;
  width: fit-content;
}

.editor table th,
.editor table td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

.editor table th {
  background-color: var(--color-neutral-200);
  font-weight: bold;
  vertical-align: top;
}

.editor table td {
  vertical-align: top;
  text-align: start;
  overflow: auto;
}

.editor .textStrikethrough {
  text-decoration: line-through;
}

.editor .textUnderline {
  text-decoration: underline;
}
.editor .textItalic {
  font-style: italic;
}

.editor .textUnderlineStrikethrough {
  text-decoration: underline line-through;
}

.editor mark {
  background-color: var(--color-warning-200);
}
