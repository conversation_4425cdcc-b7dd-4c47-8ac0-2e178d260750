import { MenuOption } from "@lexical/react/LexicalTypeaheadMenuPlugin"

import { IconName } from "@leviosa/assets"
import { Icon } from "@leviosa/components"

import { PermissionKey } from "generated/graphql"

// Extended class to support custom properties
export class InsertMenuOption extends MenuOption {
  title: string
  icon?: JSX.Element
  keywords: Array<string>
  onSelect: (queryString: string) => void
  onAddAttachment?: () => void
  className?: string
  permissions: PermissionKey[]

  constructor(
    title: string,
    options: {
      icon?: IconName
      keywords?: Array<string>
      onSelect: (queryString: string) => void
      /**
       * This Option will be shown if the user has any of the permissions. If empty if will be open to all */
      permissions?: PermissionKey[]
    }
  ) {
    super(title)
    this.title = title
    this.permissions = options.permissions || []
    this.keywords = options.keywords || []
    this.icon = options.icon ? <Icon name={options.icon} /> : undefined
    this.onSelect = options.onSelect.bind(this)
  }
}
