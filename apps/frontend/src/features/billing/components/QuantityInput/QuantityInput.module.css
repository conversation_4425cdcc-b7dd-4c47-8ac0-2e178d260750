.inputWrap.inputWrap {
  width: fit-content;
  margin-left: auto;
}
.inputWrap.inputWrap > input {
  border: 1px solid transparent;
  padding: 5px 8px;
  color: var(--Primary-Text, #181c75);
  width: 60px;
  justify-self: end;
}
.inputWrap.inputWrap > input:disabled {
  background-color: inherit;
  border: 1px solid transparent;
}

tr:hover .inputWrap.inputWrap > input,
.inputWrap.inputWrap > input:focus,
.inputWrap.inputWrap.loading > input {
  background-color: white;
  border-color: var(--color-neutral);
  padding-left: 20px;
}
.inputWrap.inputWrap > input:focus {
  border-color: var(--color-lev-blue);
}
