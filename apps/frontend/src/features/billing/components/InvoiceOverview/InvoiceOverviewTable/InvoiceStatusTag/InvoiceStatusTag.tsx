import { MenuGroup, MenuItemCheck } from "@ariakit/react"
import c from "classnames"
import { useTranslation } from "react-i18next"

import {
  Menu,
  MenuButton,
  MenuGroupLabel,
  MenuProvider,
} from "components/Ariakit"
import { MenuItemRadio } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { color } from "styles/colors"
import { Tag } from "ui"

import {
  PaymentStatus,
  useUpdateInvoicePaymentStatusMutation,
} from "generated/graphql"

import styles from "./InvoiceStatusTag.module.css"

const colorMap: Record<PaymentStatus, "orange" | "lightBlue" | "success"> = {
  UNPAID: "orange",
  CLAIM_CREATED: "lightBlue",
  PAID: "success",
}

type InvoiceStatusTagProps = {
  id: string
  paymentStatus: PaymentStatus
  issued: boolean
  size?: "default" | "small"
}

export const InvoiceStatusTag = ({
  id,
  paymentStatus,
  issued,
  size = "default",
}: InvoiceStatusTagProps) => {
  const { t } = useTranslation("enums")

  const menu = useMenuStore({
    placement: "bottom",
    animated: true,
    defaultValues: { status: paymentStatus },
  })

  const [updatePaymentStatus] = useUpdateInvoicePaymentStatusMutation()

  const handleSelect = (status: PaymentStatus) => {
    updatePaymentStatus({
      variables: {
        input: {
          id,
          paymentStatus: status,
        },
      },
    })

    menu.hide()
  }

  const paymentStatusValue = t(`PaymentStatus.${paymentStatus}`)

  if (!issued) {
    return (
      <Tag color="neutral" size={size}>
        {t("Draft")}
      </Tag>
    )
  }

  return (
    <MenuProvider store={menu}>
      <MenuButton
        className={c(
          styles.statusButton,
          color[colorMap[paymentStatus]].light,
          {
            [styles.small]: size === "small",
          }
        )}
        data-status={paymentStatus}
        store={menu}
        iconEnd={<Icon name="arrow-down-s-line" />}
      >
        {t(paymentStatusValue)}
      </MenuButton>
      <Menu className={styles.menu}>
        <MenuGroup>
          <MenuGroupLabel className={styles.titleInfo}>
            {t("Mark invoice as")}
          </MenuGroupLabel>

          <MenuItemRadio
            className={styles.menuItem}
            name="status"
            value="Unpaid"
            onClick={() => handleSelect(PaymentStatus.Unpaid)}
          >
            <MenuItemCheck />
            {t("Unpaid")}
          </MenuItemRadio>
          <MenuItemRadio
            className={styles.menuItem}
            name="status"
            value="Claim created"
            onClick={() => handleSelect(PaymentStatus.ClaimCreated)}
          >
            <MenuItemCheck />
            {t("Claim created")}
          </MenuItemRadio>
          <MenuItemRadio
            className={styles.menuItem}
            name="status"
            value="Paid"
            onClick={() => handleSelect(PaymentStatus.Paid)}
          >
            <MenuItemCheck />
            {t("Paid")}
          </MenuItemRadio>
        </MenuGroup>
      </Menu>
    </MenuProvider>
  )
}
