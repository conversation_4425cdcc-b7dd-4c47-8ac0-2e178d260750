import { useTranslation } from "react-i18next"

import { AttachmentsPreview } from "components/AttachmentsPreview/AttachmentsPreview"
import { invoicePdfUrl } from "features/billing/components/InvoiceOverview/invoicePdfUrl"
import { Table } from "ui"

import { GetInvoicesQuery } from "generated/graphql"

import styles from "./InvoiceOverviewTable.module.css"
import { TableBodyRow } from "./TableBodyRow"

type InvoiceData = {
  invoices?: GetInvoicesQuery["invoices"]
}

export const InvoiceOverviewTable = ({ invoices }: InvoiceData) => {
  const { t } = useTranslation()

  const attachments =
    invoices
      ?.filter(({ issued }) => issued)
      .map(({ id, invoiceNumber }) => ({
        name: `Invoice ${invoiceNumber}.pdf`,
        url: invoicePdfUrl(id),
      })) || []

  return (
    <AttachmentsPreview attachments={attachments}>
      <Table>
        <thead>
          <tr>
            <th>{t("Encounter")}</th>
            <th>{t("Issued")}</th>
            <th>{t("Invoice Number")}</th>
            <th>{t("Issuer")}</th>
            <th>{t("Provider")}</th>
            <th>{t("Subject")}</th>
            <th className={styles.numericValue}>{t("Total")}</th>
            <th>{t("Payment method")}</th>
            <th></th>
          </tr>
        </thead>

        <tbody>
          {(invoices || []).map((invoice) => (
            <TableBodyRow key={invoice.id} invoice={invoice} />
          ))}
        </tbody>
      </Table>
    </AttachmentsPreview>
  )
}
