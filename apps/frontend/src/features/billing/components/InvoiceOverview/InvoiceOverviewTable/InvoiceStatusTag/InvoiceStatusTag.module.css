.statusButton.statusButton {
  border-radius: var(--radius-button-half);
  border: none;
}

.small {
  font-size: 14px;
  padding: 4px 10px;
}

.menu {
  color: var(--color-text);
  min-width: 332px;
  padding: 0;
  padding-bottom: 8px;
}

.menuItem.menuItem {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
}

.titleInfo {
  margin-top: 14px;
  margin-bottom: 12px;
  padding-left: 16px;
}

.menuItem > svg {
  width: 24px;
  height: 24px;
  opacity: 0;
}

.menuItem[data-is-selected="true"] > svg {
  opacity: 1;
}
