import { MenuGroup, MenuItemCheck } from "@ariakit/react"
import c from "classnames"
import { useTranslation } from "react-i18next"

import {
  Menu,
  MenuButton,
  MenuGroupLabel,
  MenuProvider,
} from "components/Ariakit"
import { MenuItemRadio } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { notification } from "ui"

import {
  namedOperations,
  PaymentMethod,
  useUpdateInvoicePaymentMethodMutation,
} from "generated/graphql"

import { paymentMethods } from "../../InvoiceOverviewSidebar/InvoiceOverviewSidebar"
import statusTagStyles from "../InvoiceStatusTag/InvoiceStatusTag.module.css"
import styles from "./InvoicePaymentMethodTag.module.css"

type PaymentMethodTagProps = {
  id: string
  paymentMethod: string | null
  size?: "default" | "small"
}

export const InvoicePaymentMethodTag = ({
  id,
  paymentMethod,
  size = "default",
}: PaymentMethodTagProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "billing.paymentMethod" })

  const menu = useMenuStore({
    placement: "bottom",
    animated: true,
    defaultValues: { paymentMethod: paymentMethod || "" },
  })

  const [updatePaymentMethod] = useUpdateInvoicePaymentMethodMutation({
    onError: () => {
      notification.create({
        message: t("update.error"),
        status: "error",
      })
    },
    refetchQueries: [namedOperations.Query.GetInvoices],
  })

  const handleSelect = (newPaymentMethod: PaymentMethod) => {
    updatePaymentMethod({
      variables: {
        input: {
          id,
          paymentMethod: newPaymentMethod,
        },
      },
    })

    menu.hide()
  }

  const currentPaymentMethod = paymentMethods.find(
    (method) => method.value === paymentMethod
  )
  const paymentMethodLabel = currentPaymentMethod?.label || t("unselectedTag")

  return (
    <MenuProvider store={menu}>
      <MenuButton
        render={<MenuButton />}
        className={c(styles.paymentMethodButton, {
          [statusTagStyles.small]: size === "small",
        })}
        data-method={paymentMethod}
        iconEnd={<Icon name="arrow-down-s-line" />}
      >
        {paymentMethodLabel}
      </MenuButton>
      <Menu className={statusTagStyles.menu}>
        <MenuGroup>
          <MenuGroupLabel className={statusTagStyles.titleInfo}>
            {t("menuLabel")}
          </MenuGroupLabel>

          {paymentMethods.map((method) => (
            <MenuItemRadio
              key={method.value}
              className={statusTagStyles.menuItem}
              name="paymentMethod"
              value={method.value}
              onClick={() => handleSelect(method.value)}
            >
              <MenuItemCheck />
              {method.label}
            </MenuItemRadio>
          ))}
        </MenuGroup>
      </Menu>
    </MenuProvider>
  )
}
