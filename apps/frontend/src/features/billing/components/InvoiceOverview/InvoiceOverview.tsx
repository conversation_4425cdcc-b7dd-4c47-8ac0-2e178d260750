import { endOfDay } from "date-fns"
import { useTranslation } from "react-i18next"
import { useSearchParams } from "react-router-dom"

import Icon from "components/Icon/Icon"
import Restricted from "features/authentication/components/Restricted/Restricted"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { Button, Grid, Heading } from "ui"

import {
  InvoiceFilterInput,
  PaymentMethod,
  PaymentStatus,
  PermissionKey,
  useGetInvoicesQuery,
} from "generated/graphql"

import styles from "./InvoiceOverview.module.css"
import InvoiceOverviewSidebar from "./InvoiceOverviewSidebar/InvoiceOverviewSidebar"
import { InvoiceOverviewTable } from "./InvoiceOverviewTable/InvoiceOverviewTable"

export const INVOICES_LIMIT = 500

type RefreshButtonProps = {
  handleClick: () => void
  loading: boolean
}
const RefreshButton = ({ handleClick, loading }: RefreshButtonProps) => {
  const { t } = useTranslation("routes", {
    keyPrefix: "billing",
  })

  const handleOnClick = () => {
    // Prevent multiple clicks during loading
    if (!loading) {
      handleClick()
    }
  }

  return (
    <Button
      icon={<Icon name="refresh-line" spin={loading} />}
      onClick={handleOnClick}
    >
      {t("refreshButtonLabel")}
    </Button>
  )
}

const pollInterval1m = 60 * 1000

const InvoiceOverviewWithoutPermission = () => {
  const [searchParams] = useSearchParams()

  const { t } = useTranslation("routes", {
    keyPrefix: "billing",
  })

  const fromDate = searchParams.get("fromDate")
  const toDate = searchParams.get("toDate")
  const issuerId = searchParams.get("invoiceIssuer")
  const subjectId = searchParams.get("subject")
  const status = searchParams.get("status") as PaymentStatus | "DRAFT"
  const paymentMethod = searchParams.get("paymentMethod") as PaymentMethod
  const providerId = searchParams.get("provider")

  // if status = draft, it's not issued
  // if status has any other value it's issued
  // if status is null, its not set
  const issued = status === "DRAFT" ? false : status !== null ? true : undefined

  const filterInput: InvoiceFilterInput = {
    ...(providerId && { providerId }),
    ...(subjectId && { subjectId }),
    ...(issuerId && { issuerId }),
    ...(status && status !== "DRAFT" && { paymentStatus: status }),
    ...(issued !== undefined && { issued }),
    ...(paymentMethod && { paymentMethod: paymentMethod }),
    fromTreatmentDate: fromDate === null ? null : new Date(fromDate),
    toTreatmentDate: toDate === null ? null : endOfDay(new Date(toDate)),
    limit: INVOICES_LIMIT,
  }

  // by default we fetch max 50, capped by the backend
  const { data, loading, refetch } = useGetInvoicesQuery({
    variables: {
      filter: filterInput,
    },
    notifyOnNetworkStatusChange: true,
    pollInterval: pollInterval1m,
  })

  const invoices = data?.invoices || []

  const isPastLimit = invoices.length === INVOICES_LIMIT

  return (
    <Grid className={styles.container}>
      <aside className={styles.sidebarWrap}>
        <InvoiceOverviewSidebar isPastLimit={isPastLimit} />
      </aside>

      <div className={styles.invoiceWrap}>
        <div className={styles.invoiceHeaderWrap}>
          <Heading size="large">{t("invoiceOverviewHeading")}</Heading>
          <RefreshButton handleClick={refetch} loading={loading} />
        </div>

        <InvoiceOverviewTable invoices={invoices} />
      </div>
    </Grid>
  )
}

export const InvoiceOverview = () => {
  return (
    <Restricted
      to={PermissionKey.BillingBillingCodeView}
      fallback={<UnauthorizedPage />}
    >
      <InvoiceOverviewWithoutPermission />
    </Restricted>
  )
}

export default InvoiceOverview
