import { Combo<PERSON><PERSON>roup, <PERSON>u<PERSON>utton } from "@ariakit/react"
import c from "classnames"
import { matchSorter } from "match-sorter"
import { CSSProperties, ReactNode } from "react"
import { useTranslation } from "react-i18next"
import { useDebounce } from "use-debounce"

import {
  Combobox,
  ComboboxItem,
  ComboboxList,
  ComboboxProvider,
  Menu,
  MenuProvider,
} from "components/Ariakit"
import { ComboboxGroupLabel } from "components/Ariakit/Combobox/ComboboxGroup/ComboboxGroup"
import { useComboboxStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { ButtonText } from "ui"

import {
  BillingCodeType,
  GetBillingCodesQuery,
  GetMostUsedBillingCodesQuery,
  useGetBillingCodesQuery,
  useGetMostUsedBillingCodesQuery,
} from "generated/graphql"

import { PatientInvoiceSelectItemInfo } from "../PatientInvoiceSelectItemInfo/PatientInvoiceSelectItemInfo"
import styles from "./BillingCodeSelect.module.css"

type BillingCodeSelectItemProps = {
  option:
    | GetBillingCodesQuery["billingCodes"][0]
    | GetMostUsedBillingCodesQuery["mostUsedBillingCodes"][0]
  onSelect?: (value: string, billingCodeType: BillingCodeType) => void
}

const BillingCodeSelectItem = ({
  option,
  onSelect,
}: BillingCodeSelectItemProps) => {
  const { id, title, description, __typename } = option
  const isNHI = __typename === "BillingCodeNhi"

  return (
    <ComboboxItem
      value={title}
      className={styles.menuItem}
      focusOnHover
      onClick={() => {
        onSelect?.(
          id,
          isNHI ? BillingCodeType.Nhi : BillingCodeType.ClinicSpecific
        )
      }}
      setValueOnClick={false}
      render={(p) => (
        <div {...p}>
          <PatientInvoiceSelectItemInfo
            label={title}
            description={description || ""}
            hasStarred={false}
            hasNationalInsurance={isNHI}
            category={isNHI ? option.category : undefined}
          />
        </div>
      )}
    />
  )
}

type BillingCodeSelectProps = {
  className?: string
  style?: CSSProperties
  label?: ReactNode
  defaultValue?: string
  externalComboboxStore?: ReturnType<typeof useComboboxStore>
  isLoading?: boolean
  onSelect?: (value: string, billingCodeType: BillingCodeType) => void
}

export const BillingCodeSelect = ({
  className,
  style,
  label,
  onSelect,
  isLoading,
  externalComboboxStore,
}: BillingCodeSelectProps) => {
  const { t } = useTranslation("components", { keyPrefix: "combobox" })
  const tCommon = (key: string) => t(`common.${key}`)
  const tBCS = (key: string) => t(`billingCodeSelector.${key}`)

  const comboboxStore =
    externalComboboxStore ||
    useComboboxStore({
      defaultItems: [],
    })

  const { value: comboboxValue } = comboboxStore.useState()
  const [debouncedSearchValue] = useDebounce(comboboxValue, 500)

  const { data, loading, previousData } = useGetBillingCodesQuery({
    variables: {
      billingCodeType: null,
      filter: debouncedSearchValue,
      limit: 50,
    },
    skip: !debouncedSearchValue,
  })
  const optionsData = loading ? previousData : data

  const {
    data: mostUsedCodes,
    loading: loadingMostUsedCodes,
    previousData: mostUsedCodesPreviousData,
  } = useGetMostUsedBillingCodesQuery()

  const mostUsedCodesOptionsData = loadingMostUsedCodes
    ? mostUsedCodesPreviousData
    : mostUsedCodes

  const mostUsedCodesOptions =
    mostUsedCodesOptionsData?.mostUsedBillingCodes || []

  // Filter out duplicates from the most used codes
  const options =
    optionsData?.billingCodes?.filter((c) => {
      return !mostUsedCodesOptions?.some((mc) => {
        return mc.id === c.id.toString()
      })
    }) || []

  const filteredMostUsedCodes = debouncedSearchValue
    ? matchSorter(mostUsedCodesOptions, debouncedSearchValue, {
        keys: ["title", "description"],
      })
    : mostUsedCodesOptions

  return (
    <ComboboxProvider resetValueOnSelect store={comboboxStore}>
      <MenuProvider>
        <MenuButton className={c(styles.menuButton, className)} style={style}>
          <Icon
            name={isLoading ? "loader-4-line" : "add-box-line"}
            spin={isLoading}
          />
          {label && <ButtonText className={styles.label}>{label}</ButtonText>}
        </MenuButton>

        <Menu portal className={styles.menuPopover}>
          <Combobox
            autoSelect
            placeholder={tCommon("placeholder")}
            store={comboboxStore}
          />
          {loading && (
            <Icon name="loader-4-line" spin className={styles.menuSpinner} />
          )}

          <ComboboxList>
            {filteredMostUsedCodes.length > 0 && (
              <ComboboxGroup className={styles.comboboxGroup}>
                <ComboboxGroupLabel className={styles.comboboxGroupLabel}>
                  {tBCS("recentlyUsed")}
                </ComboboxGroupLabel>
                {filteredMostUsedCodes.map((option) => (
                  <BillingCodeSelectItem
                    key={option.id}
                    option={option}
                    onSelect={onSelect}
                  />
                ))}
              </ComboboxGroup>
            )}

            {options.length > 0 && (
              <ComboboxGroup className={styles.comboboxGroup}>
                <ComboboxGroupLabel className={styles.comboboxGroupLabel}>
                  {tCommon("searchResults")}
                </ComboboxGroupLabel>
                {options.map((option) => (
                  <BillingCodeSelectItem
                    key={option.id}
                    option={option}
                    onSelect={onSelect}
                  />
                ))}
              </ComboboxGroup>
            )}

            {!filteredMostUsedCodes.length && !options.length && (
              <ComboboxItem className={styles.emptyItem} disabled>
                {tCommon("noResults")}
              </ComboboxItem>
            )}
          </ComboboxList>
        </Menu>
      </MenuProvider>
    </ComboboxProvider>
  )
}
