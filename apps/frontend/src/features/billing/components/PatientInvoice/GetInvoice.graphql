query GetInvoice($invoiceId: UUID!) {
  invoice(id: $invoiceId) {
    id
    createdAt
    ...InvoiceTotalsFragment
    reference
    treatmentDate
    paymentMethod
    subject {
      id
      name
      address {
        addressLine1
        addressLine2
      }
      email
      personaId
    }
    containsNhiItems
    nhiPayableBySubject
    nhiPaysAll
    dueDate
    issuer {
      id
      title
      taxNumber
    }
    reference
    subjectDiscount
    totalWithoutVat
    comment
    payerId
    payerEmail
    printInvoice
    issuer {
      id
      title
    }
    provider {
      id
      name
    }
    payerName
    paymentDate
    sendInvoiceMail
    reference
    issued
    dueDate
    invoiceLines {
      id
      billingCode {
        id
        title
        ... on BillingCodeNhi {
          code
          description
        }
        ... on BillingCodeClinicSpecific {
          clinicCode: code
          description
        }
      }
      billingCodeType
      discount
      units
      unitPrice
      total
      quantity
      billableQuantity
    }
    encounter {
      id
    }
  }
}
