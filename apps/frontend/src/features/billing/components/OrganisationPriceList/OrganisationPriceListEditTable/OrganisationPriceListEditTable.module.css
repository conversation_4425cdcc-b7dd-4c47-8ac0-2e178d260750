.checkbox {
  cursor: pointer;
}
.checkbox:focus {
  box-shadow:
    0 0 4px rgba(13, 16, 57, 0.1),
    0 0 20px rgba(13, 16, 57, 0.2);
}

.table tbody tr:hover :where(.input, .selectInput) {
  background-color: var(--color-white);
}

.input {
  border: none;
  background-color: inherit;
  border: 1px solid transparent;
}

.textareaWrap {
  gap: 0px;
}

.input[type="text"] {
  margin-left: 0;
}

.input[type="number"] {
  padding-right: 16px;
  margin-right: -20px;
}

.inputNumber {
  padding-right: 16px;
  margin-right: -20px;
  text-align: right;
}

.inputWrap[data-key="code"] .input {
  width: 90px;
}

.inputWrap[data-key="title"] .input {
  width: 200px;
  line-height: 18px;
}

.inputWrap[data-key="units"] .input {
  width: 80px;
}

.inputWrap[data-key="unitPrice"] .input {
  width: 100px;
}

.inputWrap[data-key="vat"] .input {
  width: 80px;
}

.selectInput {
  width: 104px;
  border: 1px solid transparent;
  border-radius: 8px;
}

.selectInput > div > button {
  border: none;
  background-color: transparent;
}

.table tbody td:hover :where(.input:not(:focus), .selectInput:not(:focus)) {
  border: 1px solid #6e6f71;
  background-color: var(--color-white);
}

.table tbody tr[data-active-item] td {
  background: var(--lev-blue-lev-blue-0, #e6e6ff);
}

.table tbody tr[data-active-item] :where(td .input, td .selectInput) {
  border: 1px solid #6e6f71;
  background-color: var(--color-white);
}

.hiddenRow {
  visibility: hidden;
  position: absolute;
}
