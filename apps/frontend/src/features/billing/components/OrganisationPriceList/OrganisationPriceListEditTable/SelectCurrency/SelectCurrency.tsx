// COMEBACK enable when more currencies are added from backend
// import { useSelectStore } from "components/Ariakit/hooks"
// import Select from "components/Select/Select"
import { useTranslation } from "react-i18next"

// type SelectCurrencyProps = {
//   defaultValue: string
//   className?: string
//   onBlur?: () => void
// }

export const SelectCurrency = () => {
  // const selectStore = useSelectStore({
  //   defaultValue: defaultValue || "ISK",
  //   focusLoop: "vertical",
  // })

  // const options = [{ value: "ISK", label: "ISK" }]

  const { t } = useTranslation()

  return (
    // <Select
    //   options={options}
    //   selectStore={selectStore}
    //   hideLabel
    //   hideMessage
    //   className={className}
    //   name="currency"
    //   label={"Select Currency"}
    //   onBlur={onBlur}
    // />
    <div>{t("ISK")}</div>
  )
}
