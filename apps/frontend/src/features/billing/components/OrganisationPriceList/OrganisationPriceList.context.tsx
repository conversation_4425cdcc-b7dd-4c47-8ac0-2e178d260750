import { ReactNode, createContext, useContext, useState } from "react"
import { useSearchParams } from "react-router-dom"

import {
  BillingCodeType,
  GetBillingCodesForOrganisationQuery,
  useGetBillingCodesForOrganisationQuery,
} from "generated/graphql"

export type PriceListTypeValues = "VIEW" | "CREATE" | "EDIT"

export type PriceList =
  (GetBillingCodesForOrganisationQuery["billingCodes"][0] & {
    type?: PriceListTypeValues
  })[]

type OrganisationPriceListContextType = {
  priceList: PriceList
  loading: boolean
  currentPage?: number
  totalPages?: number

  resetPriceList: () => void
  refetchPriceList: () => void
}

const OrganisationPriceList = createContext<OrganisationPriceListContextType>({
  priceList: [],
  loading: false,

  currentPage: undefined,
  totalPages: undefined,

  resetPriceList: () => undefined,
  refetchPriceList: () => undefined,
})

export const useOrganisationPriceList = () => {
  return useContext(OrganisationPriceList)
}

export const OrganisationPriceListProvider = ({
  children,
}: {
  children: ReactNode
}) => {
  const [priceList, setPriceList] = useState<PriceList>([])

  const [searchParams] = useSearchParams()

  const searchParam = searchParams.get("search") || ""

  const { loading, refetch } = useGetBillingCodesForOrganisationQuery({
    variables: {
      limit: null,
      filter: searchParam,
      billingCodeType: BillingCodeType.ClinicSpecific,
    },
    onCompleted: (data) => {
      if (data.billingCodes) {
        setPriceList(data.billingCodes)
      }
    },
  })

  const resetPriceList = () => {
    setPriceList([])
  }

  const refetchPriceList = () => {
    resetPriceList()
    refetch()
  }

  return (
    <OrganisationPriceList.Provider
      value={{
        priceList,
        loading,
        resetPriceList,
        refetchPriceList,
      }}
    >
      {children}
    </OrganisationPriceList.Provider>
  )
}
