import { useInView } from "react-intersection-observer"

import { Loading } from "ui"

import styles from "./CustomInfiniteScroll.module.css"

type CustomInfiniteScrollProps = {
  onFetchMore: () => void
  loading: boolean
  shouldFetchMore: boolean
}

export const CustomInfiniteScroll = ({
  onFetchMore,
  loading,
  shouldFetchMore,
}: CustomInfiniteScrollProps) => {
  const { ref } = useInView({
    threshold: 0,
    triggerOnce: true,
    onChange: (inView) => {
      if (!shouldFetchMore) {
        return
      }

      if (inView) {
        onFetchMore()
      }
    },
  })

  return (
    <div className={styles.loading} ref={!loading ? ref : undefined}>
      {loading && <Loading />}
    </div>
  )
}
