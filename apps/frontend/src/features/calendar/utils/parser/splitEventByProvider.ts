import { isTypename } from "utils/isTypename"

import { ProviderSpecialty } from "generated/graphql"
import { CalendarColor } from "generated/graphql"
import { ParticipantRsvpStatus } from "generated/graphql"
import { ParticipantAttendanceRequest } from "generated/graphql"
import { ParticipantAttendanceState } from "generated/graphql"

// import { CalendarEvent } from "./calendarEvent"
type EventInstance = {
  __typename: "EventInstance"
  id: string
  title: string
  fromDate: string
  toDate: string
  description: string
  owner: { __typename: "Provider"; id: string }
  createdBy: { __typename: "Provider"; id: string }
  location:
    | { __typename: "Bed"; id: string; label: string }
    | { __typename: "Building"; id: string; label: string }
    | { __typename: "Corridor"; id: string; label: string }
    | { __typename: "Room"; id: string; label: string }
    | null
  serviceType: {
    __typename: "ExternalServiceType"
    id: string
    name: string
    color: CalendarColor
  } | null
  team: { __typename: "Team"; id: string; name: string } | null
  encounter: {
    __typename: "Encounter"
    id: string
    journalEntries: Array<{ __typename: "JournalEntry"; id: string }>
  } | null
  participants: Array<
    | {
        __typename: "ParticipantProvider"
        attendanceRequest: ParticipantAttendanceRequest
        objId: string
        participantId: string
        rsvpStatus: ParticipantRsvpStatus | null
        hasSchedulingConflict: boolean
        provider: {
          __typename: "Provider"
          id: string
          name: string
          specialty: ProviderSpecialty
        }
        attendanceState: {
          __typename: "ParticipantAttendanceRecord"
          id: string
          state: ParticipantAttendanceState
          date: string
        } | null
      }
    | {
        __typename: "ParticipantSubject"
        attendanceRequest: ParticipantAttendanceRequest
        objId: string
        participantId: string
        rsvpStatus: ParticipantRsvpStatus | null
        hasSchedulingConflict: boolean
        subject: {
          __typename: "Subject"
          id: string
          name: string
          personaId: string
          phoneNumber: string | null
        }
        attendanceState: {
          __typename: "ParticipantAttendanceRecord"
          id: string
          state: ParticipantAttendanceState
          date: string
        } | null
      }
  >
}
export type CalendarEvent = {
  start: Date
  end: Date
  title: string
  resource: EventInstance
  allDay: boolean
  subjects?: string[]
  resourceId?: string
}

/**
 * Splits calendar events by their associated providers.
 *
 * This function takes an array of calendar events and splits them into multiple events
 * based on the providers associated with each event's participants. Each resulting event
 * will have a `resourceId` corresponding to the provider's ID.
 *
 * @param events - An array of calendar events of type `CalendarEvent<"eventInstance">`.
 * @returns An array of calendar events, each associated with a specific provider.
 */

type MinimalCalendarEvent = {
  resource: {
    participants: (
      | {
          __typename: "ParticipantProvider"
          provider: { id: string }
        }
      | { __typename: "ParticipantSubject" }
    )[]
  }
}

export function splitEventByProvider<T extends MinimalCalendarEvent>(
  events: T[]
) {
  return events.flatMap((event) => {
    const { participants } = event.resource

    const participantProviders = participants.filter(
      isTypename("ParticipantProvider")
    )

    return participantProviders.map((participant) => ({
      ...event,
      resourceId: participant.provider.id,
    }))
  })
}
