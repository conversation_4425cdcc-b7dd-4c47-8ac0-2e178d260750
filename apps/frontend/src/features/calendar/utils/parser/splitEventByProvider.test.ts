import { splitEventByProvider } from "./splitEventByProvider"

type MinimalCalendarEvent = {
  resource: {
    participants: (
      | {
          __typename: "ParticipantProvider"
          provider: { id: string }
        }
      | { __typename: "ParticipantSubject" }
    )[]
  }
}

describe("splitEventByProvider", () => {
  it("should split events by provider", () => {
    const events: MinimalCalendarEvent[] = [
      {
        resource: {
          participants: [
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider1" },
            },
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider2" },
            },
            {
              __typename: "ParticipantSubject",
            },
          ],
        },
      },
    ]

    const result = splitEventByProvider(events)

    expect(result).toEqual([
      {
        resource: {
          participants: [
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider1" },
            },
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider2" },
            },
            {
              __typename: "ParticipantSubject",
            },
          ],
        },
        resourceId: "provider1",
      },
      {
        resource: {
          participants: [
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider1" },
            },
            {
              __typename: "ParticipantProvider",
              provider: { id: "provider2" },
            },
            {
              __typename: "ParticipantSubject",
            },
          ],
        },
        resourceId: "provider2",
      },
    ])
  })

  it("should return an empty array if no providers are present", () => {
    const events: MinimalCalendarEvent[] = [
      {
        resource: {
          participants: [
            {
              __typename: "ParticipantSubject",
            },
          ],
        },
      },
    ]

    const result = splitEventByProvider(events)

    expect(result).toEqual([])
  })

  it("should handle an empty events array", () => {
    const events: MinimalCalendarEvent[] = []

    const result = splitEventByProvider(events)

    expect(result).toEqual([])
  })
})
