import { addHours, isWithinInterval, roundToNearestMinutes } from "date-fns"
import { useMemo } from "react"
import { View } from "react-big-calendar"

import { getAvailabilityBlocksForProvider } from "features/calendar/components/Calendar/getAvailabilityBlocks"

import {
  EventInstancesFilterInput,
  useEventInstancesQuery,
  useGetEventsPollingQuery,
} from "generated/graphql"

import parseEventInstances from "../utils/parser/parseEventInstances"

const pollInterval15Mins = 15 * 60 * 1000
const pollInterval10s = 10 * 1000

const useEventPolling = (inputFilter: EventInstancesFilterInput) => {
  const now = roundToNearestMinutes(new Date()).toISOString()
  // we need to use useMemo since if we don't, the query will
  // be called every time the component is re-rendered (the date changes)
  const nowDate = useMemo(() => new Date(now), [now])
  const isTodayVisible = isWithinInterval(nowDate, {
    start: new Date(inputFilter.fromDate),
    end: new Date(inputFilter.toDate),
  })

  // Polling recent and upcoming events every 10 seconds. We don't
  // need the data from this query, since it will
  // update the cache and the data from any query that
  // uses the same events will be updated
  useGetEventsPollingQuery({
    variables: {
      inputFilter: {
        ...inputFilter,
        fromDate: addHours(nowDate, -1),
        toDate: addHours(nowDate, 1),
      },
    },
    skip: !isTodayVisible,
    pollInterval: pollInterval10s,
  })
}

export const useEventInstanceFetcher = (
  inputFilter: EventInstancesFilterInput,
  view: View
) => {
  const { data, previousData, ...restCalendarProps } = useEventInstancesQuery({
    variables: {
      inputFilter: inputFilter,
      providerInputFilter: {
        ids: inputFilter.participantProviderId,
      },
    },
    fetchPolicy: "cache-and-network",
    pollInterval: pollInterval15Mins,
    skip: !inputFilter.participantProviderId?.length,
  })
  useEventPolling(inputFilter)

  const eventInstances = useMemo(() => {
    return parseEventInstances(
      data?.eventInstances || previousData?.eventInstances
    )
  }, [data?.eventInstances, parseEventInstances, previousData?.eventInstances])

  const availabilityBlocks = useMemo(() => {
    return getAvailabilityBlocksForProvider(
      data?.providers || previousData?.providers || [],
      new Date(inputFilter.fromDate),
      view
    )
  }, [data?.providers, previousData?.providers, inputFilter.fromDate, view])

  return {
    eventInstances,
    availabilityBlocks,
    providers: data?.providers || previousData?.providers,
    ...restCalendarProps,
  }
}
