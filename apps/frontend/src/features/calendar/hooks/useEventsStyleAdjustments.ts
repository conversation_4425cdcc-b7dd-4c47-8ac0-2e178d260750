import { RefObject, useEffect } from "react"
import { View } from "react-big-calendar"

import type { CalendarEvent } from "../utils/parser/calendarEvent"

const getEventHourBoundaries = (weekEvents: CalendarEvent[]) => {
  if (weekEvents.length === 0) return null

  let earliestHour = 8
  let latestHour = 16

  weekEvents.forEach((element) => {
    const startHour = element.start.getHours()
    const endHour = element.end.getHours()
    if (startHour < earliestHour) earliestHour = startHour
    if (endHour > latestHour) latestHour = endHour
  })

  return { earliestHour, latestHour }
}

const showWeekend = (weekEvents: CalendarEvent[]) => {
  return weekEvents.some(
    (element) => element.start.getDay() === 6 || element.start.getDay() === 0
  )
}

const hideWeekendOnPrint = (
  element: HTMLElement,
  eventInstances: CalendarEvent[],
  currentView: string
) => {
  if (!showWeekend(eventInstances) && currentView !== "day") {
    element.classList.add("hideWeekendOnPrint")

    return
  }

  element.classList.remove("hideWeekendOnPrint")
}

export const useEventsStyleAdjustments = (
  eventInstances: CalendarEvent[],
  refContent: RefObject<HTMLDivElement>,
  currentView: View
) => {
  useEffect(() => {
    if (!refContent || !refContent.current || !eventInstances.length) return

    hideWeekendOnPrint(refContent.current, eventInstances, currentView)

    const hoursBoundaries = getEventHourBoundaries(eventInstances)
    if (!hoursBoundaries) return

    const { earliestHour, latestHour } = hoursBoundaries

    const start = earliestHour
    const end = latestHour

    const columns = refContent.current.querySelectorAll(".rbc-time-column")

    for (let i = 0; i < columns.length; i++) {
      const slots = columns[i].querySelectorAll(".rbc-timeslot-group")
      for (let j = 0; j < slots.length; j++) {
        if (j > end || j < start) {
          slots[j].classList.add("hide")
        }
      }
    }
  }, [refContent, eventInstances, currentView])
}
