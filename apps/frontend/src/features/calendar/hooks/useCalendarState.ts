import { useCallback, useEffect, useMemo } from "react"
import { View } from "react-big-calendar"
import { useMatch, useParams, useSearchParams } from "react-router-dom"
import { validate as uuidValidate } from "uuid"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { useLocalStorage } from "components/LocalStorageProvider/LocalStorageProvider"
import { RouteStrings } from "routes/RouteStrings"

export const validateUuidsString = (uuidsString: string) => {
  return uuidsString.split(",").filter(uuidValidate).join(",")
}

export type ProviderPreference = {
  selectedProviders: string[]
  visibilityMap: Record<string, boolean>
}

export default function useCalendarState() {
  const { view } = useParams()
  const [searchParams] = useSearchParams()
  useGlobalState()
  const isInCalendarRoot = useMatch(RouteStrings.calendar)
  const isInCalendarEvent = useMatch(RouteStrings.calendarViewEventInstance)
  const isInCalendar = isInCalendarRoot || isInCalendarEvent

  const [defaultView, setDefaultView] = useLocalStorage("calendarView")

  const currentView = (view ?? defaultView) as View
  useEffect(() => {
    if (currentView !== defaultView) setDefaultView(currentView)
  }, [currentView, defaultView, setDefaultView])

  const [visibleProvidersString] = useLocalStorage("visibleCalendarProviders")
  const [selectedProvidersString, setSelectedProviders] = useLocalStorage(
    "selectedCalendarProviders"
  )
  // Get provider from query parameter and properly decode it
  const rawUrlProvidersString = searchParams.get("provider") ?? ""
  const urlProviders = validateUuidsString(rawUrlProvidersString)

  const visibleProviders = validateUuidsString(visibleProvidersString)
  const selectedProviders = validateUuidsString(selectedProvidersString)
  // Parse provider IDs from URL parameter

  const removeSelectedProvider = useCallback(
    (providerId: string) => {
      console.log("Removeing selected provider", providerId)
      const newSelectedProviders = selectedProviders
        .split(",")
        .filter((id) => id !== providerId)
      setSelectedProviders(newSelectedProviders.join(","))
    },
    [selectedProviders]
  )

  // The list of visible providers we use should come from the URL
  const visibleProvidersArray = useMemo(
    () =>
      isInCalendar && urlProviders
        ? urlProviders.split(",")
        : visibleProviders.split(","),
    [isInCalendar, visibleProviders, urlProviders]
  )
  // There is a possibility that the visible providers are not in the selected providers
  // That happens if a provider is removed from the list in a different tab
  const selectedProvidersArray = useMemo(() => {
    return Array.from(
      new Set([...selectedProviders.split(","), ...urlProviders.split(",")])
    )
  }, [selectedProviders, urlProviders])

  return {
    view: currentView,
    visibleProviders: visibleProvidersArray,
    selectedProviders: selectedProvidersArray,
    removeSelectedProvider,
  }
}
