import c from "classnames"
import { useEffect } from "react"
import { ToolbarProps, View } from "react-big-calendar"
import { useHotkeys } from "react-hotkeys-hook"
import { useTranslation } from "react-i18next"

import { useSelectStore } from "components/Ariakit/hooks"
import Select from "components/Select/Select"
import { EventActions } from "features/calendar/components/EventActions/EventActions"
import ProviderEventsMenu from "features/calendar/components/ProviderEventsMenu/ProviderEventsMenu"
import useCalendarState from "features/calendar/hooks/useCalendarState"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { Heading } from "ui"
import useDateFormatter from "utils/useDateFormatter"

import styles from "./CalendarToolbar.module.css"
import { NavigationButtons } from "./NavigationButtons/NavigationButtons"

const keyViewMap: { [key: string]: View } = {
  D: "day",
  X: "work_week",
  W: "week",
  M: "month",
}

export const CalendarToolbar = ({
  onNavigate,
  date,
  onView,
  view,
}: ToolbarProps<CalendarEvent, object>) => {
  const { t } = useTranslation()
  const format = useDateFormatter()
  const { visibleProviders: providers } = useCalendarState()

  const periodStore = useSelectStore({
    defaultValue: view || "week",
  })

  useEffect(() => {
    if (view) {
      periodStore.setState("value", view)
    }
  }, [view])

  useHotkeys("d, x, w, m", (_event, handler) => {
    const selectedView = keyViewMap[handler.key.toUpperCase()]
    if (selectedView) {
      onView(selectedView)
      periodStore.setState("value", selectedView)
      periodStore.setOpen(false)
    }
  })

  const showProviderEventsMenu = providers.length === 1 && view === "day"

  const options: {
    label: string
    value: View
    subContent: string
    subContentClassName: string
    hideSubContentWhenSelected: boolean
  }[] = [
    {
      label: t("Day"),
      value: "day",
      subContent: "d",
      subContentClassName: styles.subContent,
      hideSubContentWhenSelected: true,
    },
    {
      label: t("Work week"),
      value: "work_week",
      subContent: "x",
      subContentClassName: styles.subContent,
      hideSubContentWhenSelected: true,
    },
    {
      label: t("Week"),
      value: "week",
      subContent: "w",
      subContentClassName: styles.subContent,
      hideSubContentWhenSelected: true,
    },
    {
      label: t("Month"),
      value: "month",
      subContent: "m",
      subContentClassName: styles.subContent,
      hideSubContentWhenSelected: true,
    },
  ]

  const label = format(date, {
    weekday: view === "day" ? "short" : undefined,
    day: view === "day" ? "numeric" : undefined,
    month: "short",
    year: view !== "day" ? "numeric" : undefined,
    dateStyle: undefined,
  })

  return (
    <div className={styles.wrap}>
      <Heading
        data-testid="view-header"
        size="large"
        className={c(styles.label, { [styles.day]: view === "day" })}
      >
        {label}
      </Heading>
      <NavigationButtons onNavigate={onNavigate} />
      <Select
        className={styles.viewSelect}
        options={options}
        selectStore={periodStore}
        onSelectChange={(value) =>
          typeof value === "string" && onView(value as View)
        }
        placeholder=""
        label="View"
        hideLabel
        data-testid="calendar-view-select"
        sameWidth={false}
        hideMessage
      />
      <EventActions />
      {showProviderEventsMenu && (
        <ProviderEventsMenu
          providerId={providers[0] || ""}
          date={date}
          className={styles.menuButton}
          size="large"
        />
      )}
    </div>
  )
}
