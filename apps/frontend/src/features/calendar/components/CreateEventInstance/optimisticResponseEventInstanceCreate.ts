export default ({
  id,
  description,
  start,
  end,
  title,
  ownerId,
}: {
  id: string
  description: string
  title: string
  start: Date
  end: Date
  creatorName: string
  creatorId: string
  ownerId: string
}): {
  createEventInstance: {
    owner: { __typename: string; id: string }
    fromDate: string
    toDate: string
    __typename: "EventInstance"
    description: string
    id: string
    title: string
  }
  __typename: "Mutation"
} => {
  return {
    createEventInstance: {
      id,
      owner: { id: ownerId, __typename: "Provider" },
      description,
      title,
      fromDate: start?.toISOString(),
      toDate: end?.toISOString(),
      __typename: "EventInstance",
    },
    __typename: "Mutation",
  }
}
