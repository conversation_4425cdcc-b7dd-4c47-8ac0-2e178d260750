mutation CreateEventInstance($input: EventInstanceCreateInput!, $id: UUID) {
  createEventInstance(input: $input, id: $id) {
    id
    title
    description
    fromDate
    toDate
  }
}

mutation CreateEventRecurrence($input: EventRecurrenceCreateInput!) {
  createEventRecurrence(input: $input) {
    id
    title
    description
    fromDate
    toDate
  }
}

fragment EventParticipantsSelection on EventParticipant {
  objId
  participantId
  rsvpStatus
  ... on ParticipantProvider {
    attendanceRequest
    provider {
      name
    }
    objId
  }
  ... on ParticipantSubject {
    attendanceRequest
    subject {
      name
    }
    objId
  }

  attendanceState {
    id
    state
  }
}

mutation SetEventParticipants(
  $eventRelationObject: ParticipantEventRelationObject!
  $participantInputs: [EventParticipantSetInput!]!
) {
  setEventParticipants(
    eventRelationObject: $eventRelationObject
    participantInputs: $participantInputs
  ) {
    id
    eventInstance {
      id
      title
      description
      fromDate
      toDate
      owner {
        id
      }

      participants {
        ...EventParticipantsSelection
      }
    }
  }
}
