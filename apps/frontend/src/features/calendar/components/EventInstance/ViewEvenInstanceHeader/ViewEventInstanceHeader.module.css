.header {
  display: grid;
  grid-template-columns: auto 1fr auto;
  border-bottom: 1px solid var(--color-gray-30);
  padding: 16px;
  padding-bottom: 8px;
  gap: 8px;
  position: sticky;
  top: 0;
  background-color: var(--color-white);
  z-index: 1;

  color: var(--color-text);
}

.noArrivalIndicator {
  grid-template-columns: 1fr auto auto;
}

.headingContainer {
  display: grid;
  gap: 4px;
}

.heading {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 8px;
}

.arrivalIndicatorWrapper {
  align-self: start;
  margin-top: -3px;
}

.arrivalIndicator {
  color: var(--color-lev-blue);
  padding: 8px;
  margin: 0;
  height: fit-content;
}

.cancelledIcon {
  margin-top: 4px;
  width: 24px;
  height: 24px;
  color: var(--color-critical-500);
}

.title {
  text-overflow: ellipsis;
  line-break: auto;
  line-clamp: 2;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  align-self: center;
}

.actions {
  display: flex;
  gap: 24px;
}
.actions > * {
  height: fit-content;
}

.actions > :not(:first-child) {
  margin-left: -8px;
}

.bookingInfoLoading {
  --colors-loading-gradient-light: var(--color-gray-20);
  --colors-loading-gradient-dark: var(--color-text-secondary);
  composes: loading from "@leviosa/assets/styles/LoadingGradient.module.css";
}
