import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { Button, ButtonGroup } from "ui"

import {
  namedOperations,
  ParticipantRsvpStatus,
  useSetParticipantRsvpMutation,
  EventInstanceQuery,
} from "generated/graphql"

import styles from "./SetRsvpStatus.module.css"

type SetRsvpStatusProps = {
  className?: string
  participants: EventInstanceQuery["eventInstance"]["participants"]
}

export const SetRsvpStatus = ({
  className = "",
  participants,
}: SetRsvpStatusProps) => {
  const { t } = useTranslation()
  const [setParticipantRsvp] = useSetParticipantRsvpMutation()

  const { globalData } = useGlobalState()

  const currentParticipant = participants?.find(
    (p) => p.objId === globalData.actor.id
  )

  if (!currentParticipant) return null

  const handleRsvp = (rsvpStatus: ParticipantRsvpStatus) => {
    setParticipantRsvp({
      variables: {
        setParticipantRsvpId: currentParticipant?.participantId,
        input: { rsvpStatus },
      },
      refetchQueries: [namedOperations.Query.EventInstance],
    })
  }

  const isDeclined = currentParticipant?.rsvpStatus === "DECLINED"
  const isAccepted = currentParticipant?.rsvpStatus === "ACCEPTED"

  return (
    <ButtonGroup className={`${styles.rsvpStatusWrapper} ${className}`}>
      <Button
        variant={isAccepted ? "filled" : "outline"}
        onClick={() => handleRsvp(ParticipantRsvpStatus.Accepted)}
      >
        {isAccepted ? t("Accepted") : t("Accept")}
      </Button>

      <Button
        variant={isDeclined ? "filled" : "outline"}
        status="error"
        onClick={() => handleRsvp(ParticipantRsvpStatus.Declined)}
      >
        {isDeclined ? t("Rejected") : t("Reject")}
      </Button>
    </ButtonGroup>
  )
}
