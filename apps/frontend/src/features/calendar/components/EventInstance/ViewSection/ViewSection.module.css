.wrap {
  display: grid;
  grid-template-columns: max-content auto;
  gap: 24px;
  color: var(--color-text);
}

.wrap:not(:has(> svg)) {
  grid-template-columns: 1fr;
  margin-left: 48px;
}

.wrap > :nth-child(2) {
  align-self: center;
}

.wrap:has(.viewSectionLabel) > svg {
  margin-top: 24px;
}

.sectionInfo {
  display: flex;
  align-items: center;
}

.wrap[data-has-label="true"] > svg {
  margin-top: 32px;
}

.conflict {
  background-color: var(--color-critical-200);
  border-radius: var(--radius-button-half);
  height: 24px;
  width: 24px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
  flex-shrink: 0;
}

.conflict:hover {
  background-color: var(--color-critical-300);
}

.conflictIcon {
  flex-shrink: 0;
  font-size: 14px;
  color: var(--color-critical-500);
  transform: rotate(180deg);
}

.tooltip {
  padding: 8px;
}
