import { PopoverStore } from "@ariakit/react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { timeViewFormat } from "features/calendar/utils/timeViewFormat"
import { RouteStrings } from "routes/RouteStrings"
import { Button, notification, Text } from "ui"
import { isTypename } from "utils/isTypename"

import { EventInstanceQuery, useCreateInvoiceMutation } from "generated/graphql"

import OpenInJournal from "../OpenInJournal/OpenInJournal"
import { RecurringEventText } from "../RecurringEventText/RecurringEventText"
import { RescheduleEventMenu } from "../RescheduleEventMenu/RescheduleEventMenu"
import { CancelledEventInfoPanel } from "./CancelledEventInfoPanel/CancelledEventInfoPanel"
import { DeleteEventInstance } from "./DeleteEventInstance/DeleteEventInstance"
import { EventInvoiceTable } from "./EventInvoiceTable/EventInvoiceTable"
import { EventParticipantList } from "./EventParticipantList"
import {
  EventProviderParticipantListItem,
  EventSubjectParticipantListItem,
} from "./EventParticipantListItem"
import styles from "./ViewEventInstance.module.css"
import { ViewLocation } from "./ViewLocation/ViewLocation"
import { ViewSection } from "./ViewSection/ViewSection"
import { ViewSectionLabel } from "./ViewSection/ViewSectionLabel"
import { ViewSectionMessage } from "./ViewSection/ViewSectionMessage"

type ViewEventInstanceProps = {
  event: CalendarEvent<"eventInstance">
  eventInstance?: EventInstanceQuery["eventInstance"]
  store: PopoverStore
}

export const ViewEventInstance = ({
  event,
  eventInstance,
  store,
}: ViewEventInstanceProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "calendar" })

  const navigate = useNavigate()
  const [createInvoice, { loading: loadingCreateInvoice }] =
    useCreateInvoiceMutation({})

  const {
    title,
    resource: {
      id,
      description,
      fromDate,
      toDate,
      serviceType,
      participants,
      team,
      canceledAt,
    },
  } = event

  const subjectsMap = Object.fromEntries(
    eventInstance?.participants
      ?.filter(isTypename("ParticipantSubject"))
      .map((participant) => [participant.subject.id, participant]) ?? []
  )

  const providersMap = Object.fromEntries(
    eventInstance?.participants
      ?.filter(isTypename("ParticipantProvider"))
      .map((participant) => [participant.provider.id, participant]) ?? []
  )

  const formattedTime = timeViewFormat(new Date(fromDate), new Date(toDate))

  const subjectParticipants = participants.filter(
    isTypename("ParticipantSubject")
  )
  const providerParticipants = participants.filter(
    isTypename("ParticipantProvider")
  )

  const providerId = providerParticipants[0]?.provider?.id
  const subjectIds = subjectParticipants.map(
    (participant) => participant.subject.id
  )

  const providerIds = providerParticipants.map(
    (participant) => participant.provider.id
  )

  const onCreateInvoice = (encounterId: string) => {
    createInvoice({
      variables: {
        input: {
          encounterId,
        },
      },
      onCompleted: (data) => {
        const invoiceId = data.createInvoice.id

        const basePath = generatePath(RouteStrings.patientInvoice, {
          invoiceId: invoiceId,
        })

        const searchParams = new URLSearchParams({
          returnTo: encodeURIComponent(location.pathname + location.search),
        })

        navigate({
          pathname: basePath,
          search: searchParams.toString(),
        })
      },
      onError: () => {
        notification.create({
          message: tRoutes("failedToCreateInvoice"),
          status: "error",
        })
      },
    })
  }

  const eventTitle = serviceType?.name || title

  const isEventCancelled = !!canceledAt
  const encounter = eventInstance?.encounter

  return (
    <>
      {isEventCancelled && eventInstance && (
        <CancelledEventInfoPanel
          canceledAt={canceledAt}
          canceledBy={eventInstance.canceledBy?.name}
          cancellationReason={eventInstance.cancellationReason}
          eventStatus={eventInstance.eventStatus}
        />
      )}
      <ViewSection iconName="group-line" hasContent={participants.length > 0}>
        <div className={styles.participants}>
          <ViewSectionLabel>{tRoutes("subjects")}</ViewSectionLabel>
          <EventParticipantList>
            {subjectParticipants.map((participant) => {
              const subjectId = participant.subject.id
              const subjectMap = subjectsMap[subjectId]
              const { hasSchedulingConflict, subject } = subjectMap || {}
              const { age, gender, personaId, phoneNumber } = subject || {}

              return (
                <EventSubjectParticipantListItem
                  key={subjectId}
                  id={subjectId}
                  name={participant.subject.name}
                  hasSchedulingConflict={
                    hasSchedulingConflict && !isEventCancelled
                  }
                  {...(personaId && {
                    subjectData: {
                      age,
                      gender,
                      personaId,
                      phoneNumber,
                    },
                  })}
                />
              )
            })}
            {subjectParticipants.length === 0 && (
              <Text className={styles.noInfo}>{tRoutes("noSubjects")}</Text>
            )}
          </EventParticipantList>
          <ViewSectionLabel>{tRoutes("providers")}</ViewSectionLabel>
          <EventParticipantList>
            {providerParticipants.map((participant) => (
              <EventProviderParticipantListItem
                name={participant.provider.name}
                hasSchedulingConflict={
                  providersMap[participant.provider.id]
                    ?.hasSchedulingConflict && !isEventCancelled
                }
                key={participant.provider.id}
                type={participant.provider.__typename}
              />
            ))}
            {providerParticipants.length === 0 && (
              <Text className={styles.noInfo}>{tRoutes("noProviders")}</Text>
            )}
          </EventParticipantList>
        </div>
      </ViewSection>
      <ViewSection iconName="time-line" hasContent={!!formattedTime}>
        <div>
          {serviceType &&
          providerId &&
          subjectParticipants.length === 1 &&
          !isEventCancelled ? (
            <RescheduleEventMenu
              eventDate={fromDate}
              serviceTypeId={serviceType?.id}
              providerId={providerId}
              subjectId={subjectParticipants[0].subject.id}
              formattedTime={formattedTime}
              eventInstanceId={id}
            />
          ) : (
            <Text>{formattedTime}</Text>
          )}
          {eventInstance?.eventRecurrence && !isEventCancelled && (
            <ViewSectionMessage>
              <RecurringEventText
                eventRecurrence={eventInstance.eventRecurrence}
              />
            </ViewSectionMessage>
          )}
        </div>
      </ViewSection>
      <ViewLocation
        fromTime={fromDate}
        toTime={toDate}
        isEventCancelled={isEventCancelled}
      />
      <ViewSection iconName="file-list-line" hasContent={!!description}>
        {description}
      </ViewSection>
      <ViewSection
        iconName="money-dollar-circle-line"
        hasContent={!!encounter?.id}
      >
        {encounter?.invoices?.length ? (
          <EventInvoiceTable
            key={id}
            invoices={encounter.invoices}
            createInvoice={() => onCreateInvoice(encounter.id)}
            loadingCreateInvoice={loadingCreateInvoice}
          />
        ) : (
          encounter?.id && (
            <Button
              variant="clear"
              onClick={() => {
                !loadingCreateInvoice && onCreateInvoice(encounter.id)
              }}
              className={styles.createInvoiceButton}
              icon={loadingCreateInvoice && <Icon name="loader-4-line" spin />}
            >
              {loadingCreateInvoice
                ? tRoutes("creatingInvoice")
                : tRoutes("createInvoice")}
            </Button>
          )
        )}
      </ViewSection>
      <div className={styles.footer}>
        {!isEventCancelled && (
          <DeleteEventInstance
            eventId={id}
            eventTitle={eventTitle}
            subject={subjectParticipants
              ?.map((participant) => participant.subject.name)
              .join(", ")}
            provider={providerParticipants[0]?.provider?.name || ""}
            date={fromDate}
            eventRecurrence={eventInstance?.eventRecurrence}
            store={store}
          />
        )}
        <OpenInJournal
          start={new Date(fromDate)}
          end={new Date(toDate)}
          subjectIds={subjectIds}
          providerIds={providerIds}
          teamId={team?.id}
          eventId={id}
          encounterReason={eventTitle}
          encounterId={encounter?.id}
          variant="filled"
          className={styles.openJournalButton}
          isEventCancelled={isEventCancelled}
        />
      </div>
    </>
  )
}
