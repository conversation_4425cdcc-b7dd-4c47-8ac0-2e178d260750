mutation EditEventsForRecurrence(
  $recurrenceId: UUID!
  $input: EventInstancesInRecurrenceEditInput!
) {
  editEventsForRecurrence(recurrenceId: $recurrenceId, input: $input) {
    ...EventInstanceFragment
    eventStatus
    createdBy {
      id
    }
    eventRecurrence {
      id
      repeatInterval
      durationMins
      fromDate
      toDate
    }
    participants {
      objId
      participantId
      rsvpStatus
      hasSchedulingConflict

      attendanceState {
        id
        state
      }

      ... on ParticipantProvider {
        provider {
          id
          name
        }
      }
      ... on ParticipantSubject {
        subject {
          id
          name
        }
      }
    }
  }
}
