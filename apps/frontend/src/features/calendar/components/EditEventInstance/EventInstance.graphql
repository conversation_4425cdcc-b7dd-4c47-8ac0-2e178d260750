query EventInstance($eventInstanceId: UUID!) {
  eventInstance(id: $eventInstanceId) {
    ...EventInstanceFragment
    eventStatus
    createdBy {
      id
      name
      userType
    }
    createdAt
    canceledAt
    cancellationReason
    canceledBy {
      id
      name
    }
    eventRecurrence {
      id
      repeatInterval
      durationMins
      fromDate
      toDate
    }
    participants {
      ...EventParticipantsFragment
    }
    encounter {
      id
      journalEntries {
        id
      }
      invoices {
        id
        invoiceNumber
        subject {
          id
          name
          email
        }
        totalPayableBySubject
        paymentMethod
        paymentStatus
        issued
        payerEmail
      }
    }
  }
}
