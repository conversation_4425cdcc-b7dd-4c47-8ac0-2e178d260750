import {
  EditEventInstanceMutation,
  EventInstanceQuery,
  EventInstanceStatus,
  EventType,
} from "generated/graphql"

type EditEventInstanceOptimisticResponseType = {
  id: string
  title: string
  fromDate: Date
  toDate: Date
  ownerId: string
  serviceType: EventInstanceQuery["eventInstance"]["serviceType"]
  participants: EventInstanceQuery["eventInstance"]["participants"]
  team: EventInstanceQuery["eventInstance"]["team"]
}

export default ({
  id,
  fromDate,
  toDate,
  title,
  ownerId,
  serviceType,
  participants,
  team,
}: EditEventInstanceOptimisticResponseType): EditEventInstanceMutation => {
  return {
    editEventInstance: {
      id,
      owner: { id: ownerId, __typename: "Provider" as const },
      location: null,
      description: "",
      eventStatus: EventInstanceStatus.Ready,
      eventType: EventType.Meeting,
      fromDate: fromDate.toISOString(),
      toDate: toDate.toISOString(),
      title,
      serviceType: serviceType,
      team,
      participants,
      __typename: "EventInstance" as const,
    },
    __typename: "Mutation" as const,
  }
}
