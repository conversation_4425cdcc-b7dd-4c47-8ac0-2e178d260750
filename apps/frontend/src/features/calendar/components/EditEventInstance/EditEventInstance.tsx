import { usePopoverContext } from "@ariakit/react"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { z } from "zod"

import { Radio, RadioGroup } from "components/Ariakit"
import useRadioStore from "components/Ariakit/hooks/useRadioStore/useRadioStore"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import optimisticResponseEventInstance from "features/calendar/components/EditEventInstance/optimisticResponseEventInstanceEdit"
import { Participant } from "features/calendar/components/Participants/Participants"
import dateLocalReadableFormat from "features/calendar/utils/dateLocalReadableFormat"
import participantInputs from "features/calendar/utils/participantInputs"
import { Button, notification } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"

import {
  EventInstanceQuery,
  namedOperations,
  ParticipantAttendeeSource,
  ParticipantEventRelationObject,
  ParticipantEventRelationSource,
  useEditEventInstanceMutation,
  useEditEventsForRecurrenceMutation,
  useSetEventParticipantsMutation,
} from "generated/graphql"

import {
  EventInstanceForm,
  EventInstanceSchema,
} from "../forms/EventInstanceForm/EventInstanceForm"
import styles from "./EditEventInstance.module.css"

enum RecurrenceType {
  instance = "instance",
  recurrence = "recurrence",
}

type EditEventInstanceProps = {
  eventInstance: EventInstanceQuery["eventInstance"]
}

const EditEventInstance = ({ eventInstance }: EditEventInstanceProps) => {
  const [eventInstanceForEdit, setEventInstanceForEdit] = useState<z.infer<
    typeof EventInstanceSchema
  > | null>(null)

  const popoverStore = usePopoverContext()

  const { t } = useTranslation()

  const navigateToCalendar = () => {
    if (popoverStore) popoverStore.hide()
  }

  const radioStoreForUpdate = useRadioStore()
  const radioStoreValueForUpdate = radioStoreForUpdate.useState("value")

  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const [
    setEventParticipant,
    { loading: setEventParticipantLoading, error: setEventParticipantError },
  ] = useSetEventParticipantsMutation({
    onCompleted: (data) => {
      if (data) {
        navigateToCalendar()
      }
    },
    // COMEBACK remove when backend returns data
    refetchQueries: [
      namedOperations.Query.EventInstance,
      namedOperations.Query.EventInstances,
      namedOperations.Query.ProviderBox,
    ],
  })

  const [editEventInstance, { loading: updateLoading, error: updateError }] =
    useEditEventInstanceMutation({
      onCompleted: () => {
        notification.create({
          message: t("Event has been updated"),
          status: "success",
          maxWidth: "500px",
        })

        setEventInstanceForEdit(null)

        if (eventRecurrence?.id) {
          navigateToCalendar()

          return
        }
      },
    })

  const [editEventsForRecurrence] = useEditEventsForRecurrenceMutation({
    onCompleted: () => {
      notification.create({
        message: t("Recurrent Events has been updated"),
        status: "success",
        maxWidth: "500px",
      })

      setEventInstanceForEdit(null)

      navigateToCalendar()
    },
    onError: (error) => {
      notification.create({
        message: error.message,
        status: "error",
        maxWidth: "500px",
      })
      setEventInstanceForEdit(null)

      navigateToCalendar()
    },
  })

  if (!eventInstance) return null

  const {
    fromDate,
    toDate,
    participants,
    eventRecurrence,
    owner: { id: ownerId },
    location,
    team,
    serviceType,
    ...eventProperties
  } = eventInstance

  const eventParticipants: Participant[] = participants.map((participant) => {
    const {
      objId,
      attendanceRequest,
      rsvpStatus,
      participantId,
      attendanceState,
      hasSchedulingConflict,
      __typename,
    } = participant

    return {
      attendanceRequest: attendanceRequest,
      name:
        __typename === "ParticipantProvider"
          ? participant.provider.name
          : participant.subject.name,
      participantType:
        __typename === "ParticipantProvider"
          ? ParticipantAttendeeSource.Provider
          : ParticipantAttendeeSource.Subject,
      userId: objId,
      participantId,
      rsvpStatus,
      attendance: attendanceState?.state ?? null,
      isAvailable: !hasSchedulingConflict,
      personaId:
        __typename === "ParticipantSubject"
          ? participant.subject.personaId
          : null,
      specialty:
        __typename === "ParticipantProvider"
          ? participant.provider.specialty
          : null,
      phoneNumber:
        __typename === "ParticipantSubject"
          ? participant.subject.phoneNumber
          : null,
    }
  })

  const onUpdate = () => {
    if (!eventInstanceForEdit) return

    // NOTE: we are not updating providers for recurring event

    const {
      //participant update data
      attendanceRequest: _attendanceRequest,
      participantType: _participantType,
      userId: _userId,
      eventParticipantId: _eventParticipantId,
      locationId,
      serviceTypeId,
      teamId,
      ...restData
    } = eventInstanceForEdit

    if (radioStoreValueForUpdate === RecurrenceType.instance) {
      editEventInstance({
        variables: {
          editEventInstanceId: eventInstance.id,
          input: {
            ...restData,
            locationId: {
              set: locationId || null,
            },
            serviceTypeId: {
              set:
                serviceTypeId === "none" || !serviceTypeId
                  ? null
                  : serviceTypeId,
            },
            teamId: {
              set: teamId || null,
            },
          },
        },
      })

      return
    }

    const { fromDate, toDate, ...restRecurrenceData } = restData

    editEventsForRecurrence({
      variables: {
        recurrenceId: eventRecurrence?.id || "",
        input: {
          ...restRecurrenceData,
          fromTime: fromDate.toTimeString().split(" ")[0],
          toTime: toDate.toTimeString().split(" ")[0],
          locationId: {
            set: locationId || null,
          },
          serviceTypeId: {
            set:
              serviceTypeId === "none" || !serviceTypeId ? null : serviceTypeId,
          },
        },
      },
    })
  }

  return (
    <>
      <EventInstanceForm
        formType="Edit"
        isLoading={
          updateLoading ||
          setEventParticipantLoading ||
          setEventParticipantLoading
        }
        error={updateError || setEventParticipantError}
        formData={{
          fromDate: dateLocalReadableFormat(fromDate),
          toDate: dateLocalReadableFormat(toDate),
          untilDate: eventRecurrence?.toDate,
          participants: eventParticipants,
          repeatInterval: eventRecurrence?.repeatInterval,
          ...eventProperties,
          ownerId: ownerId,
          locationId: location?.id,
          teamId: team?.id,
          serviceTypeId: serviceType?.id,
        }}
        onSubmit={async (data) => {
          const {
            participantType,
            userId,
            eventParticipantId,
            locationId,
            serviceTypeId,
            teamId,
            attendanceRequest: _,
            ...restData
          } = data

          if (eventRecurrence?.id) {
            setEventInstanceForEdit(data)
            return
          }

          editEventInstance({
            variables: {
              editEventInstanceId: eventInstance.id,
              input: {
                ...restData,
                locationId: {
                  set: locationId || null,
                },
                serviceTypeId: {
                  set:
                    serviceTypeId === "none" || !serviceTypeId
                      ? null
                      : serviceTypeId,
                },
                teamId: {
                  set: teamId || null,
                },
              },
            },
            optimisticResponse: optimisticResponseEventInstance({
              ...restData,
              id: eventInstance.id,
              fromDate: restData.fromDate,
              toDate: restData.toDate,
              ownerId: actor.id,
              serviceType: serviceType,
              participants: eventInstance.participants,
              team: team,
            }),
          })

          const eventRelationObject: ParticipantEventRelationObject = {
            objId: eventInstance.id,
            source: ParticipantEventRelationSource.EventInstance,
          }

          const participants = participantInputs({
            participantType,
            userId,
            eventParticipantId,
          })

          setEventParticipant({
            variables: {
              eventRelationObject: eventRelationObject,
              participantInputs: participants,
            },
          })
        }}
        onCancel={navigateToCalendar}
      />
      <Dialog
        title={t("Edit recurring event")}
        isOpen={eventInstanceForEdit !== null}
        onClose={() => setEventInstanceForEdit(null)}
        contentClassName={styles.dialogContent}
        actions={
          <>
            <Button
              variant="clear"
              onClick={() => setEventInstanceForEdit(null)}
            >
              {t("Cancel")}
            </Button>
            <Button onClick={onUpdate} variant="filled">
              {t("Update")}
            </Button>
          </>
        }
      >
        <div>
          <RadioGroup store={radioStoreForUpdate}>
            <Radio
              label={t("This event only")}
              value={RecurrenceType.instance}
            />
            <Radio label={t("All events")} value={RecurrenceType.recurrence} />
          </RadioGroup>
        </div>
      </Dialog>
    </>
  )
}

export default EditEventInstance
