fragment EventParticipantsFragment on EventParticipant {
  objId
  participantId
  rsvpStatus
  hasSchedulingConflict
  ... on ParticipantProvider {
    attendanceRequest
    provider {
      id
      name
      specialty
    }
    objId
  }
  ... on ParticipantSubject {
    attendanceRequest
    subject {
      id
      age
      gender
      name
      personaId
      phoneNumber
    }
    objId
  }

  attendanceState {
    id
    state
    date
  }
}
