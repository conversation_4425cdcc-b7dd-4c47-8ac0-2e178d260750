import { endOfDay, startOfDay } from "date-fns"
import { t } from "i18next"
import { useCallback, useState } from "react"
import { Trans, useTranslation } from "react-i18next"

import { Icon } from "@leviosa/components"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Panel, { PanelWithIcon } from "components/Panel/Panel"
import { Button, Heading, Modal, notification, Text, TextWithIcon } from "ui"
import Checkbox from "ui/components/Checkbox/Checkbox"
import { isTypename } from "utils/isTypename"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  EventInstanceStatus,
  useCancelEventInstancesMutation,
  namedOperations,
  useGetCancellableProviderEventsQuery,
  CancellableProviderEventInfoFragmentFragment,
} from "generated/graphql"

import styles from "./CancelProviderEventsModal.module.css"

// Component for displaying event information
interface EventInfoProps {
  event: CancellableProviderEventInfoFragmentFragment
  providerId: string
}

const EventInfo = ({ event, providerId }: EventInfoProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })

  const timeFormat = useTimeFormatter()
  const startTime = event.fromDate ? timeFormat(new Date(event.fromDate)) : ""
  const endTime = event.toDate ? timeFormat(new Date(event.toDate)) : ""

  const subjects = event.participants
    ?.filter(isTypename("ParticipantSubject"))
    .map((participant) => participant.subject)

  const affectedProviders = event.participants
    ?.filter(isTypename("ParticipantProvider"))
    .map((participant) => participant.provider)
    .filter((provider) => provider.id !== providerId)

  const eventTitle =
    event.serviceType?.name || (event.title?.trim() ? event.title : undefined)
  const eventInfo = [startTime, endTime, eventTitle].filter(Boolean).join(" - ")

  return (
    <div className={styles.eventInfo}>
      <div className={styles.subjectsContainer}>
        {subjects.map((subject, index) => {
          const noPhoneNumber = !subject.phoneNumber
          return (
            <span key={subject.id}>
              <Heading size="small" as="span">
                {subject.name}
              </Heading>
              {noPhoneNumber && (
                <Tooltip
                  tooltipContent={tRoutes("phoneNumberNotSet")}
                  tooltipClassName={styles.tooltip}
                  className={styles.tooltipIcon}
                  portal={false}
                  status="warning"
                >
                  <Icon name="notification-off-line" />
                </Tooltip>
              )}
              {index < subjects.length - 1 && <span>, </span>}
            </span>
          )
        })}
      </div>
      <Text secondary>{eventInfo}</Text>
      {affectedProviders.length > 0 && (
        <PanelWithIcon status="info" className={styles.panel}>
          {tRoutes("cancellingWillAlsoAffectOthers", {
            providers: affectedProviders.map((p) => p.name).join(", "),
          })}
        </PanelWithIcon>
      )}
    </div>
  )
}

interface CancelEventsModalProps {
  isOpen: boolean
  providerId: string
  onClose: () => void
  date: Date
}

const CancelProviderEventsModal = ({
  isOpen,
  providerId,
  onClose,
  date,
}: CancelEventsModalProps) => {
  const { t: tRoutes } = useTranslation("routes", {
    keyPrefix: "calendar",
  })

  const [selectedEvents, setSelectedEvents] = useState<string[]>([])

  const format = useDateFormatter()
  const formattedDate = format(date)

  const [
    cancelEventInstances,
    { loading: loadingCancellingEvents, error: errorCancellingEvents },
  ] = useCancelEventInstancesMutation()

  const { data, loading } = useGetCancellableProviderEventsQuery({
    variables: {
      inputFilter: {
        fromDate: startOfDay(date),
        toDate: endOfDay(date),
        participantProviderId: [providerId],
        includeCanceled: false,
      },
      providerInputFilter: {
        ids: [providerId],
      },
    },
    skip: !isOpen, // Only run query when modal is open
    onCompleted: (data) => {
      const eventInstances = data?.eventInstances || []
      setSelectedEvents(eventInstances.map((event) => event.id))
    },
  })

  const handleCancelEvents = useCallback(() => {
    cancelEventInstances({
      variables: {
        input: {
          ids: selectedEvents,
          status: EventInstanceStatus.CanceledByProvider,
        },
      },
      refetchQueries: [namedOperations.Query.EventInstances],
      onCompleted: () => {
        notification.create({
          message: tRoutes("eventsSuccessfullyCancelled"),
          status: "success",
        })
      },
    })
    onClose()
  }, [cancelEventInstances, selectedEvents, onClose])

  const toggleEventSelection = useCallback((eventId: string) => {
    setSelectedEvents((prev) =>
      prev.includes(eventId)
        ? prev.filter((id) => id !== eventId)
        : [...prev, eventId]
    )
  }, [])

  const eventInstances = data?.eventInstances || []
  const provider = data?.providers?.[0] || null

  const renderModalContent = () => {
    if (!provider) {
      return <Panel>{tRoutes("somethingWentWrong")}</Panel>
    }

    if (eventInstances.length === 0) {
      return (
        <Panel>
          <Trans
            i18nKey={tRoutes("noEventsToCancel", {
              name: provider.name,
              date: formattedDate,
            })}
            components={{ bold: <b /> }}
          />
        </Panel>
      )
    }

    return (
      <div className={styles.modalContent}>
        <Panel variant="no-border">
          <Trans
            i18nKey={tRoutes("scheduleForDate", {
              name: provider?.name,
              date: formattedDate,
            })}
            components={{ bold: <b /> }}
          />
        </Panel>
        <div className={styles.eventsHeader}>
          <Heading size="xsmall">{tRoutes("events")}</Heading>
          <Text>
            <Trans
              i18nKey={tRoutes("eventsSelected", {
                number: selectedEvents.length,
              })}
              components={{
                bold: <b className={styles.selectedEventsLabel} />,
              }}
            />
          </Text>
        </div>
        <ul className={styles.events}>
          {eventInstances.map((event) => (
            <li key={event.id}>
              <Checkbox
                id={event.id}
                className={styles.event}
                label={<EventInfo event={event} providerId={providerId} />}
                checked={selectedEvents.includes(event.id)}
                onChange={() => {
                  toggleEventSelection(event.id)
                }}
              />
            </li>
          ))}
        </ul>
        {errorCancellingEvents && (
          <Panel status="error" className={styles.panel}>
            <TextWithIcon status="error">
              {tRoutes("somethingWentWrong")}
            </TextWithIcon>
          </Panel>
        )}
        <div className={styles.footer}>
          <Button variant="clear" onClick={onClose}>
            {t("close")}
          </Button>
          <Button
            variant="filled"
            onClick={handleCancelEvents}
            disabled={loadingCancellingEvents || selectedEvents.length === 0}
            iconEnd={
              loadingCancellingEvents && <Icon name={"loader-4-line"} spin />
            }
          >
            {loadingCancellingEvents
              ? tRoutes("cancellingEvents")
              : tRoutes("cancelEvents")}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <Modal
      isOpen={!loading && isOpen}
      onClose={onClose}
      title={tRoutes("cancelEvents")}
      aria-describedby="cancel-events-description"
      contentClassName={styles.modal}
    >
      {renderModalContent()}
    </Modal>
  )
}

export default CancelProviderEventsModal
