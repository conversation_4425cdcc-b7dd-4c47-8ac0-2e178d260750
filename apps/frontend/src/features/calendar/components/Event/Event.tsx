import { EventProps } from "react-big-calendar"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { ButtonText, Text } from "ui"
import { getEventTitle as _getEventTitle } from "utils/getEventTitle"
import { isTypename } from "utils/isTypename"
import useTimeFormatter from "utils/useTimeFormatter"

import { JournalEntryStatus } from "generated/graphql"

import ArrivalIndicator from "../ArrivalIndicator/ArrivalIndicator"
import { EventInvoiceIndicator } from "../EventInvoiceIndicator/EventInvoiceIndicator"
import OpenInJournal from "../OpenInJournal/OpenInJournal"
import styles from "./Event.module.css"

export const Event = ({ event }: EventProps<CalendarEvent>) => {
  const { view } = useParams()
  const formatTime = useTimeFormatter()
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "calendar" })
  const { resource, start, end } = event

  const time = (() => {
    return (
      <>
        <time dateTime={start.toISOString()}>{formatTime(start)}</time>
        <time dateTime={end.toISOString()} className={styles.endTime}>
          {" - "}
          {formatTime(end)}
        </time>
      </>
    )
  })()

  const title = (() => {
    if (resource.type === "holiday") {
      return ""
    }

    if (event.subjects && event.subjects.length > 0) {
      return <PiiSensitive>{event.subjects.join(", ")}</PiiSensitive>
    }

    return _getEventTitle(event.title, resource.serviceType?.name)
  })()

  const description =
    resource.type === "eventInstance" ? resource.description : ""

  const subjects =
    resource.type === "eventInstance"
      ? resource.participants.filter(isTypename("ParticipantSubject"))
      : []

  const hasJournalEntries =
    resource.type === "eventInstance" &&
    resource.encounter &&
    resource.encounter.journalEntries.length > 0

  const hasAllJournalEntriesCompleted =
    hasJournalEntries &&
    resource.encounter?.journalEntries.every(
      (journalEntry) => journalEntry.status === JournalEntryStatus.Completed
    )

  const hasNonCompletedJournalEntries =
    hasJournalEntries &&
    resource.encounter?.journalEntries.some(
      (journalEntry) => journalEntry.status !== JournalEntryStatus.Completed
    )

  const providerIds =
    resource.type === "eventInstance"
      ? resource.participants
          ?.filter(isTypename("ParticipantProvider"))
          .map((provider) => provider.provider.id)
      : []

  const subjectIds =
    resource.type === "eventInstance"
      ? resource.participants
          ?.filter(isTypename("ParticipantSubject"))
          .map((subject) => subject.subject.id)
      : []
  const hasInvoices =
    resource.type === "eventInstance" &&
    resource.encounter &&
    resource.encounter.invoices?.length > 0

  const isCancelled = resource.type === "eventInstance" && !!resource.canceledAt

  return (
    <div
      className={styles.wrap}
      data-day-view={view === "day"}
      data-all-day-event={event.allDay}
    >
      <Text size="small" className={styles.eventTime}>
        {time}
      </Text>
      <div className={styles.eventActions}>
        {hasInvoices && (
          <EventInvoiceIndicator invoices={resource.encounter?.invoices} />
        )}
        {(hasAllJournalEntriesCompleted || hasNonCompletedJournalEntries) && (
          <Tooltip
            placement="top"
            tooltipContent={
              <div style={{ maxWidth: 250 }}>
                {hasAllJournalEntriesCompleted
                  ? tRoutes("allJournalEntriesCompleted")
                  : tRoutes("journalEntryNotCompleted")}
              </div>
            }
          >
            <Icon
              name={
                hasAllJournalEntriesCompleted
                  ? "file-list-2-fill"
                  : "file-list-2-line"
              }
              className={styles.encounterIcon}
            />
          </Tooltip>
        )}
        {isCancelled ? (
          <Tooltip
            placement="top"
            tooltipContent={
              <div style={{ maxWidth: 250 }}>
                {tRoutes("eventHasBeenCancelled")}
              </div>
            }
          >
            <Icon name="prohibited-2-line" className={styles.cancelledIcon} />
          </Tooltip>
        ) : (
          <ArrivalIndicator
            subjectParticipants={subjects}
            wrapperClassName={styles.arrivalIndicator}
            placement={view === "day" ? "bottom-start" : "bottom-end"}
          />
        )}
      </div>
      <ButtonText size="small" className={styles.eventTitle}>
        {title}
      </ButtonText>
      <Text size="small" className={styles.eventDescription}>
        {description}
      </Text>
      {resource.type === "eventInstance" && (
        <OpenInJournal
          className={styles.openJournalButton}
          start={new Date(resource.fromDate)}
          end={new Date(resource.toDate)}
          providerIds={providerIds}
          subjectIds={subjectIds}
          encounterReason={resource.serviceType?.name || resource.title}
          teamId={resource.team?.id}
          eventId={resource.id}
          encounterId={resource.encounter?.id}
          isEventCancelled={!!resource.canceledAt}
          variant="clear"
          size="small"
        />
      )}
    </div>
  )
}
