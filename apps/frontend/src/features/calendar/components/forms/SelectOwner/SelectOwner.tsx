import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"

import { useGetParticipantsQuery } from "generated/graphql"

type SelectOwnerProps = {
  ownerId?: string
  readOnly?: boolean
  className?: string
}

export const SelectOwner = ({
  ownerId,
  readOnly,
  className,
}: SelectOwnerProps) => {
  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const { data: dataProviders } = useGetParticipantsQuery({
    variables: {
      inputSubjectFilter: {
        name: null,
      },
      inputProviderFilter: {
        name: null,
      },
    },
  })

  const comboboxProvidersStore = useComboboxStore({})
  const { value: providerValue } = comboboxProvidersStore.useState()

  const providers =
    dataProviders?.providers.map((provider) => ({
      label: provider.name,
      value: provider.id,
    })) ?? []

  const { filteredList: filteredProviders } = useFilter({
    defaultItems: providers || [],
    value: providerValue,
  })

  const selectStoreProviders = useSelectStore({
    combobox: comboboxProvidersStore,
    defaultValue: ownerId || actor.id,
    focusLoop: "vertical",
  })

  return (
    <FiltrableSelect
      placeholder={"Select Owner/Organizer"}
      label={"Owner/Organizer"}
      options={providers}
      selectStore={selectStoreProviders}
      filteredOptions={filteredProviders}
      comboboxStore={comboboxProvidersStore}
      name="ownerId"
      readOnly={readOnly}
      className={className}
      sameWidth
    />
  )
}
