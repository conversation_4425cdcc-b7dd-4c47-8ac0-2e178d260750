.formWrap {
  max-width: 730px;
}

.span7 {
  grid-column: span 7;
}

.span12 {
  grid-column: span 12;
}

.grid {
  width: 100%;
}

.inputPlaceholder {
  color: var(--color-blue-gray-violet-dark);
  padding: 4px 8px;
  margin: 4px -8px;
  cursor: pointer;
}

.inputPlaceholder:hover {
  background: var(--color-lev-blue-on-white-hover);
  border-radius: var(--radius-button-half);
}

.footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  position: sticky;
  bottom: 0;
  background-color: var(--color-white);
  margin-top: 16px;

  padding: 6px 24px 16px 24px;

  border-top: 1px solid var(--color-gray-30);
  width: calc(100% + 48px);
  margin-left: -24px;
  margin-right: -24px;
}

.createEventFooter {
  padding-bottom: 24px;
  margin-bottom: -24px;
}

.visuallyHidden.visuallyHidden {
  display: none;
}

.repeatIntervalText {
  margin-bottom: 16px;
}
