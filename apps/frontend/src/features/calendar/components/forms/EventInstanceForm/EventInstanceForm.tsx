import { ApolloError } from "@apollo/client"
import c from "classnames"
import { t } from "i18next"
import { FormEvent, useRef, useState } from "react"
import { useTranslation } from "react-i18next"
import z from "zod"

import Panel from "components/Panel/Panel"
import { EventInstanceFormData } from "features/calendar/types/EventInstanceFormData"
import { DO_NOT_REPEAT } from "features/calendar/utils/eventRepeatIntervalOptions"
import { Button, FormGrid, Grid, Input, Textarea } from "ui"

import { EventRepeatInterval } from "generated/graphql"

import { CalendarTimePicker } from "../../CalendarTimePicker/CalendarTimePicker"
import { ViewSection } from "../../EventInstance/ViewSection/ViewSection"
import { Participants } from "../../Participants/Participants"
import { SelectLocation } from "../../SelectLocation/SelectLocation"
import { SelectServiceType } from "../../SelectServiceType/SelectServiceType"
import { SelectTeam } from "../../SelectTeam/SelectTeam"
import { EventInstanceProvider } from "./EventInstance.context"
import styles from "./EventInstanceForm.module.css"

export const EventInstanceSchema = z
  .object({
    title: z.string().default(""),
    description: z.string(),

    untilDate: z
      .string()
      .transform((d) => new Date(d))
      .optional(),

    fromDate: z.string().transform((d) => new Date(d)),

    toDate: z.string().transform((d) => new Date(d)),
    repeatInterval: z
      .enum([
        DO_NOT_REPEAT,
        EventRepeatInterval.EveryWeekday,
        EventRepeatInterval.Weekly,
        EventRepeatInterval.Monthly,
      ])
      .optional(),

    attendanceRequest: z.array(z.string()),
    participantType: z.array(z.string()),
    userId: z.array(z.string()),
    eventParticipantId: z.array(z.string()),
    teamId: z.string().optional().or(z.null()),
    serviceTypeId: z.string().optional().or(z.null()),
    locationId: z.string().optional().or(z.null()),
  })
  .refine(
    (data) => {
      if (data.serviceTypeId === "none" || !data.serviceTypeId) {
        return typeof data.title === "string" && data.title.length >= 3
      }

      return true
    },
    {
      message: t(
        "Title must be at least 3 characters long if no service is selected"
      ),
      path: ["title"],
    }
  )

type EventInstanceFormProps = {
  formType: "Create" | "Edit"
  formData: Partial<EventInstanceFormData>
  error?: ApolloError
  readOnly?: boolean
  isLoading?: boolean
  onSubmit?: (data: z.infer<typeof EventInstanceSchema>) => void
  onCancel?: () => void
}

export const EventInstanceForm = ({
  formType,
  formData,
  onSubmit,
  error,
  isLoading = false,
  onCancel,
  readOnly = false,
}: EventInstanceFormProps) => {
  const { t } = useTranslation()

  const {
    title,
    description,
    fromDate,
    toDate,
    participants,
    repeatInterval,
    locationId,
    teamId,
    serviceTypeId,
    untilDate,
  } = formData

  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )

  const submitButtonRef = useRef<HTMLButtonElement>(null)
  const titleInputRef = useRef<HTMLInputElement>(null)
  const descriptionInputRef = useRef<HTMLTextAreaElement>(null)

  const [showTitleInput, setShowTitleInput] = useState(false)
  const [showDescriptionInput, setShowDescriptionInput] = useState(false)
  const [showLocationInput, setShowLocationInput] = useState(false)

  const handleSubmitEventInstance = (event: FormEvent<HTMLFormElement>) => {
    event.preventDefault()

    const formData = new FormData(event.currentTarget)
    const data = Object.fromEntries(formData.entries())
    const attendanceRequest = formData.getAll("attendanceRequest") ?? []
    const attendance = formData.getAll("attendance") ?? []
    const participantType = formData.getAll("participantType")
    const userId = formData.getAll("userId")
    const eventParticipantId = formData.getAll("eventParticipantId")

    const validatedInput = EventInstanceSchema.safeParse({
      ...data,
      attendanceRequest,
      attendance,
      participantType,
      userId,
      eventParticipantId,
      locationId: data.locationId || null,
    })

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)
      console.error(validatedInput.error)

      return
    }

    onSubmit?.(validatedInput.data)
  }

  return (
    <EventInstanceProvider fromDate={fromDate} toDate={toDate}>
      <FormGrid
        id="event-instance-form"
        onSubmit={handleSubmitEventInstance}
        className={styles.formWrap}
      >
        <ViewSection iconName={"time-line"} hasLabel>
          <CalendarTimePicker
            readOnly={readOnly}
            formType={formType}
            repeatInterval={repeatInterval}
            untilDate={untilDate}
          />
        </ViewSection>
        <Participants
          formType={formType}
          initialParticipants={participants}
          eventToDate={toDate}
        />
        <ViewSection iconName={"list-check-3"} hasLabel>
          <Grid className={styles.grid}>
            <SelectServiceType
              serviceTypeId={serviceTypeId}
              useDefaultServiceType={formType === "Create"}
            />
            <SelectTeam teamId={teamId} />
          </Grid>
        </ViewSection>

        <ViewSection>
          <Grid className={styles.grid}>
            <Input
              ref={titleInputRef}
              label={t("Title")}
              name="title"
              defaultValue={title}
              readOnly={readOnly}
              className={c(styles.span12, {
                [styles.visuallyHidden]: !showTitleInput && !title,
              })}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault()
                  submitButtonRef.current?.focus()
                }
              }}
            />
            {!showTitleInput && !title && (
              <button
                onClick={() => {
                  setShowTitleInput(true)
                  setTimeout(() => {
                    if (titleInputRef.current) {
                      titleInputRef.current.focus()
                    }
                  }, 0)
                }}
                onFocus={() => {
                  setShowTitleInput(true)
                  setTimeout(() => {
                    if (titleInputRef.current) {
                      titleInputRef.current.focus()
                    }
                  }, 0)
                }}
                className={c(styles.span12, styles.inputPlaceholder)}
              >
                {t("Add title")}
              </button>
            )}
          </Grid>
        </ViewSection>

        <ViewSection>
          <Grid className={styles.grid}>
            <Textarea
              ref={descriptionInputRef}
              label={t("Description")}
              name="description"
              defaultValue={description}
              readOnly={readOnly}
              className={c(styles.span12, {
                [styles.visuallyHidden]: !showDescriptionInput && !description,
              })}
              hideMessage
            />
            {!showDescriptionInput && !description && (
              <button
                onClick={() => {
                  setShowDescriptionInput(true)
                  setTimeout(() => {
                    if (descriptionInputRef.current) {
                      descriptionInputRef.current.focus()
                    }
                  }, 0)
                }}
                onFocus={() => {
                  setShowDescriptionInput(true)
                  setTimeout(() => {
                    if (descriptionInputRef.current) {
                      descriptionInputRef.current.focus()
                    }
                  }, 0)
                }}
                className={c(styles.span12, styles.inputPlaceholder)}
              >
                {t("Add description")}
              </button>
            )}
          </Grid>
        </ViewSection>
        <ViewSection>
          <Grid className={styles.grid}>
            <SelectLocation
              locationId={locationId}
              readOnly={readOnly}
              className={c(styles.span7, {
                [styles.visuallyHidden]: !showLocationInput && !locationId,
              })}
              visuallyHidden={!showLocationInput && !locationId}
            />
            {!showLocationInput && !locationId && (
              <button
                onClick={() => {
                  setShowLocationInput(true)
                }}
                onFocus={() => {
                  setShowLocationInput(true)
                }}
                className={c(styles.span12, styles.inputPlaceholder)}
              >
                {t("Add location")}
              </button>
            )}
          </Grid>
        </ViewSection>
        {(error || validationError) && (
          <Panel status="error">
            {validationError?.message}
            {error?.message}
          </Panel>
        )}
        <div
          className={c(styles.footer, {
            [styles.createEventFooter]: formType === "Create",
          })}
        >
          <Button onClick={onCancel} variant="clear">
            {t("cancel")}
          </Button>
          <Button
            disabled={isLoading}
            type="submit"
            variant="filled"
            form="event-instance-form"
            ref={submitButtonRef}
          >
            {formType === "Create" ? t("Create") : t("doSave")}
          </Button>
        </div>
      </FormGrid>
    </EventInstanceProvider>
  )
}
