import { differenceInMinutes, parse } from "date-fns"

import { isValid24HourFormat } from "./isValid24HourFormat"

export const getDiffInMinutes = (
  fromTime: string,
  toTime: string,
  initialDiffMinutes: number
): number => {
  if (!isValid24HourFormat(fromTime) || !isValid24HourFormat(toTime)) {
    return initialDiffMinutes
  }

  const fromDate = parse(fromTime, "HH:mm", new Date())
  const toDate = parse(toTime, "HH:mm", new Date())

  return differenceInMinutes(toDate, fromDate)
}
