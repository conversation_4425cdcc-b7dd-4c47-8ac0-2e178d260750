import { addMinutes, parse, startOfDay } from "date-fns"

export const roundUpToNearestQuarterMinutes = (date: string): string => {
  const dateParts = date.split(":")
  const hours = parseInt(dateParts[0])
  const minutes = dateParts.length > 1 ? parseInt(dateParts[1]) : 0

  if (isNaN(hours) || isNaN(minutes)) return "00:00"

  if (hours >= 24 || hours < 0 || minutes >= 60 || minutes < 0) return "00:00"

  const dateTime = parse(`${hours}:${minutes}`, "HH:mm", new Date())
  const minutesFromStartOfDay =
    (dateTime.getTime() - startOfDay(dateTime).getTime()) / 60000
  const roundedMinutes = Math.ceil(minutesFromStartOfDay / 15) * 15
  const roundedTime = addMinutes(startOfDay(dateTime), roundedMinutes)

  const formattedHours = String(roundedTime.getHours()).padStart(2, "0")
  const formattedMinutes = String(roundedTime.getMinutes()).padStart(2, "0")

  return `${formattedHours}:${formattedMinutes}`
}
