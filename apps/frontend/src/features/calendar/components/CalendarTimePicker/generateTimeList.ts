import {
  addHours,
  addMinutes,
  differenceInMinutes,
  Duration,
  format,
  formatDuration,
} from "date-fns"

export const generateTimeListFrom = () => {
  const timeList = []

  for (let hour = 0; hour <= 23; hour++) {
    for (let minute = 0; minute <= 45; minute += 15) {
      const formattedTime = format(new Date().setHours(hour, minute), "HH:mm")
      timeList.push(formattedTime)
    }
  }

  return timeList
}

export type TimeList = { time: string; duration: string }

export const generateTimeListTo = (fromDateTime: string) => {
  const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/

  const timeList: TimeList[] = []

  if (!timeRegex.test(fromDateTime)) {
    return timeList
  }

  const [hours, minutes] = fromDateTime.split(":")
  const parsedDateTime = new Date()
  parsedDateTime.setHours(Number(hours))
  parsedDateTime.setMinutes(Number(minutes))

  for (let hour = 0; hour <= 23; hour++) {
    for (let minute = 0; minute <= 45; minute += 15) {
      const dateTime = addMinutes(addHours(parsedDateTime, hour), minute)
      const formattedTime = format(dateTime, "HH:mm")
      const timeInMinutes = differenceInMinutes(dateTime, parsedDateTime)

      const duration: Duration = {
        hours: Math.floor(timeInMinutes / 60),
        minutes: timeInMinutes % 60,
      }
      const formattedDuration = formatDuration(duration, {
        format: ["hours", "minutes"],
      })

      timeList.push({
        time: formattedTime,
        duration: formattedDuration,
      })
    }
  }

  return timeList
}
