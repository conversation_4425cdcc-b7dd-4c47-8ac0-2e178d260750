import { MenuButton as MenuButtonAriakit } from "@ariakit/react"
import { addDays, addMonths } from "date-fns"
import { useCallback, useState } from "react"
import { useHotkeys } from "react-hotkeys-hook"
import { useTranslation } from "react-i18next"

import { MenuGroupLabel, MenuProvider } from "components/Ariakit"
import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { useMenuStore } from "components/Ariakit/hooks"
import useNavigateCalendar from "features/calendar/utils/navigateCalendar"
import { replaceHotKeyChar } from "features/power-menu/components/PowerMenuItem/replaceHotkeyChar"
import modifier from "features/power-menu/lib/hotkeys/hotkeyModifier"
import { RouteStrings } from "routes/RouteStrings"
import { IconButton } from "ui"

import {
  namedOperations,
  useCreateEventInSlotMutation,
} from "generated/graphql"

import OptionIntervalMenu from "../OptionIntervalSubMenu/OptionIntervalMenu"

type DuplicateEventMenuProps = {
  eventDate: string
  serviceTypeId: string
  providerId: string
  subjectId: string
}

export default function DuplicateEventMenu({
  eventDate,
  serviceTypeId,
  providerId,
  subjectId,
}: DuplicateEventMenuProps) {
  const { t } = useTranslation()
  const menuStore = useMenuStore()
  const navigateCalendar = useNavigateCalendar()
  const isOpen = menuStore.useState().open
  const [selectedFromTime, setSelectedFromTime] = useState("")

  const [createEvent, mutationResult] = useCreateEventInSlotMutation({
    refetchQueries: [namedOperations.Query.EventInstances],
  })

  useHotkeys(
    modifier + "+d",
    (event) => {
      event.preventDefault()
      menuStore.setOpen(true)
    },
    [menuStore]
  )

  const optionIntervals = [
    {
      label: t("In a month"),
      // Available slots from just under a month before the event date
      // to 6 months after the event date
      // The -4 is to give flexibility in booking an event some days before the month
      fromDate: addMonths(addDays(new Date(eventDate), -4), 1).toISOString(),
      toDate: addMonths(new Date(eventDate), 6).toISOString(),
    },
    {
      label: t("In six months"),
      // Same here, 5 days before 6 months to allow more flexibility
      fromDate: addMonths(addDays(new Date(eventDate), -5), 6).toISOString(),
      toDate: addMonths(new Date(eventDate), 12).toISOString(),
    },
    {
      label: t("In a year"),
      // Same here, 5 days before the year to allow more flexibility
      fromDate: addMonths(addDays(new Date(eventDate), -5), 12).toISOString(),
      toDate: addMonths(new Date(eventDate), 24).toISOString(),
    },
  ]

  const handleCreateEvent = async (fromTime: string, toTime: string) => {
    setSelectedFromTime(fromTime)

    const { data } = await createEvent({
      variables: {
        input: {
          fromDate: new Date(fromTime),
          toDate: new Date(toTime),
          providerId,
          subjectId,
          serviceTypeId,
        },
      },
    })

    if (data?.createEventInstanceInSlot.id)
      setTimeout(() => {
        navigateCalendar(RouteStrings.calendarViewEventInstance, {
          eventId: data.createEventInstanceInSlot.id,
          search: {
            provider: providerId,
            date: fromTime.split("T")[0],
          },
        })
      }, 600)
  }

  const getMutationResult = useCallback(
    (fromTime: string) => {
      if (selectedFromTime === fromTime)
        return {
          loading: mutationResult.loading,
          called: mutationResult.called,
          error: mutationResult.error,
        }

      return {
        loading: false,
        called: false,
        error: undefined,
      }
    },
    [selectedFromTime, mutationResult]
  )

  return (
    <MenuProvider store={menuStore}>
      <Tooltip
        tooltipContent={`Duplicate event (${replaceHotKeyChar(
          `${modifier}+D`
        )})`}
      >
        <MenuButtonAriakit
          as={IconButton}
          size="xlarge"
          iconName="file-copy-line"
        />
      </Tooltip>
      <OptionIntervalMenu
        serviceTypeId={serviceTypeId}
        providerId={providerId}
        subjectId={subjectId}
        isOpen={isOpen}
        handleSelectSlot={handleCreateEvent}
        getMutationResult={getMutationResult}
        optionIntervals={optionIntervals}
      >
        <MenuGroupLabel>{t("Duplicate Event")}</MenuGroupLabel>
      </OptionIntervalMenu>
    </MenuProvider>
  )
}
