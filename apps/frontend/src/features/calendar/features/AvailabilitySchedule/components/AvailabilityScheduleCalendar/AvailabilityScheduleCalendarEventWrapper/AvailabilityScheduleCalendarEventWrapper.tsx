import { EventWrapperProps } from "react-big-calendar"

import { EventWrapperAvailability } from "features/calendar/components/EventWrapperAvalibility/EventWrapperAvailability"
import type { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"

export const AvailabilityScheduleCalendarEventWrapper = ({
  event,
  children,
  ...rest
}: EventWrapperProps<CalendarEvent> & {
  children?: React.ReactNode
}) => {
  return (
    <div
      className="eventWrapper"
      data-resource-type={event.resource.type}
      data-is-background-event="true"
    >
      {event.resource.type === "availability" ? (
        <EventWrapperAvailability
          event={event}
          eventInfo={{
            event,
            ...rest,
          }}
        />
      ) : (
        children
      )}
    </div>
  )
}
