.details {
  padding: 0 16px;
  border-radius: 16px;
}

details > summary:first-of-type {
  list-style-type: none;
}

.summaryButton {
  display: grid;
  grid-template-columns: 1fr auto auto;
  grid-gap: 8px;
  cursor: pointer;
  margin: 0 -16px;
  padding: 16px;
  border-radius: 16px;

  > div {
    margin: auto 0;
  }
}

.summaryButtons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editButton {
  height: fit-content;
}

.content {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-bottom: 24px;
}

.description {
  display: -webkit-box;
  -webkit-line-clamp: 10;
  -webkit-box-orient: vertical;
  overflow: hidden;
  white-space: pre-line;
}

.input {
  width: auto;
}

.days {
  width: 100%;
  table-layout: fixed;
}

.days td {
}
th.labelCol {
  width: 100px;
  padding: 0 17px;
  text-align: left;
}

th.dayCol {
  width: 50px;
  vertical-align: top;
  padding: 18px 6px 0 0;
  /* height = Input height + padding-top + padding-bottom */
  height: calc(42px + 8px + 8px);
  text-align: right;
  font-weight: normal;
}
td.unavailable {
  padding-left: 16px;
  color: var(--color-text-secondary);
}

.switch {
  display: flex;
  align-items: center;
  gap: 16px;
}

.removeButton {
  width: fit-content;
}

.serviceHeader {
  display: flex;
  position: relative;
  align-items: center;
  justify-content: space-between;
}

.icons {
  display: flex;
  align-items: center;
  gap: 16px;
}
