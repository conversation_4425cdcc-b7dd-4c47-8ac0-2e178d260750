.container {
  width: 400px;
  display: grid;
}

.periodContainer {
  display: grid;
  grid-gap: 8px;
  grid-template-columns: 160px 6px 160px 36px;
  align-items: center;
  margin-bottom: 10px;
  color: var(--color-lev-blue);
}

.datePicker {
  width: 160px;
  color: var(--color-lev-blue);
  border-color: var(--color-lev-blue);
  cursor: pointer;
}

.datePicker > button {
  top: -7px;
  pointer-events: none;
}

.dateInput {
  height: 30px;
  color: var(--color-lev-blue);
  border: 0;
  cursor: pointer;
}

.dateInput:hover,
.dateInput:focus {
  color: var(--color-lev-blue);
  background-color: var(--color-lev-blue-10);
}

.deleteButton {
  height: 100%;
}

.addNewPeriodButton {
  width: fit-content;
}

.showPastSchedulesButton {
  width: fit-content;
  justify-self: flex-end;
}
