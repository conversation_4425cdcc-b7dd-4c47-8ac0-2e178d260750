import c from "classnames"
import { useEffect, useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link, useNavigate, useParams } from "react-router-dom"

import { useSelectStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import Select from "components/Select/Select"
import ServiceTypeModal from "features/organisation-management/components/ServiceTypeModal/ServiceTypeModal"
import { RouteStrings } from "routes/RouteStrings"
import { calendarColorMap } from "styles/colors"
import { Button, ButtonText, Input, Label, notification, Text } from "ui"
import { IconButton } from "ui"
import { Dialog } from "ui/components/Dialog/Dialog"
import Switch from "ui/components/Switch/Switch"
import { compareTimes, naiveTimeToSimpleTime } from "utils/timeUtils"

import {
  ServiceTypeAvailabilityFragmentFragment,
  useDeleteServiceTypeAvailabilityMutation,
  useSetServiceTypeAvailabilityBookableOnlineMutation,
  useUpdateServiceTypeAvailabilityMutation,
  Weekday,
} from "generated/graphql"

import AvailabilityBlock, {
  AddAvailabilityBlockButton,
} from "../AvailabilityBlock/AvailabilityBlock"
import styles from "./AvailabilityServiceType.module.css"

const weekdays = [
  Weekday.Monday,
  Weekday.Tuesday,
  Weekday.Wednesday,
  Weekday.Thursday,
  Weekday.Friday,
  Weekday.Saturday,
  Weekday.Sunday,
]

type AvailabilityServiceTypeProps = {
  serviceTypeAvailability: ServiceTypeAvailabilityFragmentFragment
  canEdit: boolean
  expanded?: boolean
  provider: {
    id: string
    name: string
    teams: { id: string; name: string }[]
  }
}

const AvailabilityServiceType = ({
  serviceTypeAvailability,
  canEdit,
  expanded = false,
  provider,
}: AvailabilityServiceTypeProps) => {
  const { t } = useTranslation()
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "Weekday_short",
  })

  const { scheduleId } = useParams<{
    scheduleId: string
  }>()

  const { id } = serviceTypeAvailability

  const navigate = useNavigate()

  const [updateServiceTypeAvailability] =
    useUpdateServiceTypeAvailabilityMutation()

  const [deleteServiceTypeAvailability] =
    useDeleteServiceTypeAvailabilityMutation()

  const [setServiceTypeAvailabilityBookableOnline] =
    useSetServiceTypeAvailabilityBookableOnlineMutation()

  const [showContent, setShowContent] = useState(expanded)
  const [showServiceTypeModal, setShowServiceTypeModal] = useState(false)
  const [isDialogForRemoveOpen, setIsDialogForRemoveOpen] = useState(false)

  useEffect(() => {
    setShowContent(expanded)
  }, [expanded])

  const teamsOptions =
    provider.teams.map((team) => ({
      value: team.id,
      label: team.name,
    })) || []

  const selectTeamStore = useSelectStore({
    defaultValue: serviceTypeAvailability.team?.id || "",
  })

  const serviceType = serviceTypeAvailability.serviceType

  // If there is no scheduleId, we won't have any service type availabilities
  if (!scheduleId) {
    return null
  }

  return (
    <div
      id={id}
      className={c(styles.details, calendarColorMap[serviceType.color].light)}
    >
      <div className={styles.serviceHeader}>
        <Link
          className={styles.summaryButton}
          to={generatePath(RouteStrings.calendarSchedule, {
            providerId: provider.id,
            scheduleId,
            serviceId: !expanded ? id : undefined,
          })}
          replace
          onClick={() => setShowContent(!showContent)}
        >
          <ButtonText>{serviceTypeAvailability.serviceType.name}</ButtonText>
        </Link>

        <div className={styles.icons}>
          {showContent && (
            <IconButton
              variant="clear"
              size="large"
              iconName="edit-line"
              className={styles.editButton}
              onClick={() => {
                setShowServiceTypeModal(true)
              }}
            />
          )}
          <IconButton
            iconName={showContent ? "arrow-up-s-line" : "arrow-down-s-line"}
            size="large"
            onClick={() => setShowContent(!showContent)}
          />
        </div>
      </div>

      {showContent && (
        <div className={styles.content}>
          <Text
            size="small"
            title={serviceTypeAvailability.serviceType.description}
            className={styles.description}
          >
            {serviceTypeAvailability.serviceType.description}
          </Text>
          <div className={styles.switch}>
            <Switch
              id={`${serviceTypeAvailability.id}-online-bookable-toggle`}
              checked={serviceTypeAvailability.isBookableOnline}
              onToggle={() => {
                setServiceTypeAvailabilityBookableOnline({
                  variables: {
                    input: {
                      id: serviceTypeAvailability.id,
                      isBookableOnline:
                        !serviceTypeAvailability.isBookableOnline,
                    },
                  },
                  onError: (error) => {
                    notification.create({
                      status: "error",
                      message: error.message,
                    })
                  },
                })
              }}
            />
            <Text
              as="label"
              htmlFor={`${serviceTypeAvailability.id}-online-bookable-toggle`}
            >
              {t("Available for online booking")}
            </Text>
          </div>
          <Input
            label="Duration (minutes)"
            placeholder={t("Enter duration")}
            inputProps={{
              className: styles.input,
            }}
            disabled={!canEdit}
            hideMessage
            defaultValue={
              serviceTypeAvailability.appointmentDurationMinutes || undefined
            }
            onBlur={(e) => {
              updateServiceTypeAvailability({
                variables: {
                  input: {
                    id: id,
                    appointmentDurationMinutes: parseInt(e.target.value),
                  },
                },
              })
            }}
          />
          <Select
            label={t("Team")}
            name="teamId"
            placeholder={t("Select team")}
            selectStore={selectTeamStore}
            options={teamsOptions}
            disabled={!canEdit}
            hideMessage
            onSelectChange={(value) => {
              updateServiceTypeAvailability({
                variables: {
                  input: {
                    id: id,
                    teamId: value as string,
                  },
                },
              })
            }}
          />
          <table className={styles.days}>
            <thead>
              <tr>
                <td />
                <Label as="th" className={styles.labelCol}>
                  {t("from")} *
                </Label>
                <Label as="th" className={styles.labelCol}>
                  {t("to")} *
                </Label>
                <td />
              </tr>
            </thead>
            <tbody>
              {weekdays.map((day) => {
                const blockRules = serviceTypeAvailability.blockRules
                  .filter((br) => br.weekday === day && !br.isDeleted)
                  .sort((a, b) => compareTimes(a.fromTime, b.fromTime))
                  .map((br) => ({
                    ...br,
                    fromTime: br.fromTime
                      ? naiveTimeToSimpleTime(br.fromTime)
                      : null,
                    toTime: br.toTime ? naiveTimeToSimpleTime(br.toTime) : null,
                  }))

                if (!blockRules.length) {
                  return (
                    <tr key={day}>
                      <Text
                        size="default"
                        as="th"
                        rowSpan={blockRules.length || 1}
                        className={styles.dayCol}
                      >
                        {tEnum(day)}
                      </Text>
                      <td colSpan={2} className={styles.unavailable}>
                        {t("Unavailable")}
                      </td>
                      <td></td>
                      <td>
                        {canEdit && (
                          <AddAvailabilityBlockButton
                            scheduleId={scheduleId}
                            serviceTypeAvailabilityId={id}
                            weekday={day}
                          />
                        )}
                      </td>
                    </tr>
                  )
                }

                return blockRules.map((blockRule, i) => {
                  const prevItem = i > 0 ? blockRules[i - 1] : null
                  const prevEndTime = prevItem ? prevItem.toTime : null
                  const nextItem =
                    i < blockRules.length - 1 ? blockRules[i + 1] : null
                  const nextStartTime = nextItem ? nextItem.fromTime : null

                  return (
                    <tr key={blockRule.id}>
                      {i === 0 && (
                        <Text
                          as="th"
                          rowSpan={blockRules.length || 1}
                          className={styles.dayCol}
                        >
                          {tEnum(day)}
                        </Text>
                      )}
                      <AvailabilityBlock
                        key={blockRule.id}
                        scheduleId={scheduleId}
                        serviceTypeAvailabilityId={id}
                        minuteStep={
                          serviceTypeAvailability.appointmentDurationMinutes ||
                          undefined
                        }
                        disabled={!canEdit}
                        allowedStartTime={prevEndTime || "00:00"}
                        allowedEndTime={nextStartTime || "23:59:59"}
                        blockRule={blockRule}
                        showAddBlockButton={i === blockRules.length - 1}
                      />
                    </tr>
                  )
                })
              })}
            </tbody>
          </table>
          {canEdit && (
            <Button
              variant="clear"
              status="error"
              icon={<Icon name="delete-bin-line" />}
              onClick={() => setIsDialogForRemoveOpen(true)}
              className={styles.removeButton}
            >
              {t("Remove Service")}
            </Button>
          )}
        </div>
      )}
      <ServiceTypeModal
        serviceType={serviceType}
        showModal={showServiceTypeModal}
        closeModal={() => {
          setShowServiceTypeModal(false)
          setShowContent(true)
        }}
      />

      <Dialog
        title={t("Are you sure?")}
        isOpen={isDialogForRemoveOpen}
        onClose={() => {
          setIsDialogForRemoveOpen(false)
        }}
        actions={
          <>
            <Button onClick={() => setIsDialogForRemoveOpen(false)}>
              {t("Cancel")}
            </Button>
            <Button
              onClick={() => {
                deleteServiceTypeAvailability({
                  variables: {
                    input: {
                      id: id,
                    },
                  },
                  onCompleted: () => {
                    setIsDialogForRemoveOpen(false)
                    navigate(
                      generatePath(RouteStrings.calendarSchedule, {
                        providerId: provider.id,
                        scheduleId,
                      })
                    )
                  },
                })
              }}
              variant="filled"
              status="error"
            >
              {t("Remove")}
            </Button>
          </>
        }
      >
        {t(
          "The service will be removed from the availability schedule. This action cannot be undone."
        )}
      </Dialog>
    </div>
  )
}

export default AvailabilityServiceType
