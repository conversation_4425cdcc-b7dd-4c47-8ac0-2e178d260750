import { ToolbarProps } from "react-big-calendar"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate, useParams } from "react-router-dom"

import {
  useComboboxStore,
  useFilter,
  useSelectStore,
} from "components/Ariakit/hooks"
import FiltrableSelect from "components/FiltrableSelect/FiltrableSelect"
import { NavigationButtons } from "features/calendar/components/CalendarToolbar/NavigationButtons/NavigationButtons"
import { CalendarEvent } from "features/calendar/utils/parser/calendarEvent"
import { RouteStrings } from "routes/RouteStrings"
import { Heading } from "ui"

import { useGetProvidersQuery } from "generated/graphql"

import styles from "./AvailabilityScheduleCalendarToolbar.module.css"

export const AvailabilityScheduleCalendarToolbar = ({
  onNavigate,
  label,
}: ToolbarProps<CalendarEvent, object>) => {
  const navigate = useNavigate()

  const { providerId } = useParams()

  const { data } = useGetProvidersQuery({
    variables: {
      inputFilter: {
        name: null,
      },
    },
  })

  const { t } = useTranslation()

  const comboboxStore = useComboboxStore()

  const { value: comboboxValue } = comboboxStore.useState()

  const providerOptions =
    data?.providers.map((provider) => ({
      label: provider?.name || "",
      value: provider?.id || "",
    })) || []

  const selectStore = useSelectStore({
    combobox: comboboxStore,
    defaultValue: providerId || "",
    focusLoop: "vertical",
  })

  const { filteredList } = useFilter({
    defaultItems: providerOptions,
    value: comboboxValue,
  })

  return (
    <div className={styles.wrap}>
      <FiltrableSelect
        className={styles.select}
        sameWidth
        hideLabel
        label={t("Select provider")}
        selectStore={selectStore}
        hideMessage
        comboboxStore={comboboxStore}
        filteredOptions={filteredList}
        options={providerOptions}
        onSelectChange={(value) => {
          if (typeof value === "string") {
            navigate(
              generatePath(RouteStrings.calendarSchedule, {
                providerId: value,
              })
            )
          }
        }}
      />
      <Heading>{label}</Heading>
      <div>
        <NavigationButtons onNavigate={onNavigate} />
      </div>
    </div>
  )
}
