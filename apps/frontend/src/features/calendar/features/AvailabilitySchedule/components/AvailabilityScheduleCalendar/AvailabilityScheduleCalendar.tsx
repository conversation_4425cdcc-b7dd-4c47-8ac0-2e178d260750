import { useEffect, useState } from "react"
import { useMemo } from "react"
import { Calendar as ReactBigCalendar } from "react-big-calendar"
import { generatePath, useNavigate, useParams } from "react-router-dom"

import { getAvailabilityBlocks } from "features/calendar/components/Calendar/getAvailabilityBlocks"
import { Event } from "features/calendar/components/Event/Event"
import RBCHeader from "features/calendar/components/RBCHeader/RBCHeader"
import { formats } from "features/calendar/utils/formats"
import { getScrollTimeAt7 } from "features/calendar/utils/getScrollTime"
import { localizer } from "features/calendar/utils/localizer"
import calendarVariables from "features/calendar/utils/variables/calendarVariables"
import { RouteStrings } from "routes/RouteStrings"

import { AvailabilityScheduleFragmentFragment } from "generated/graphql"

import "../../../../assets/Calendar.css"
import "../../../../assets/CalendarDragAndDrop.css"
import "../../../../assets/CalendarPrintView.css"
import "../../../../assets/EventColors.css"
import styles from "./AvailabilityScheduleCalendar.module.css"
import { AvailabilityScheduleCalendarEventWrapper } from "./AvailabilityScheduleCalendarEventWrapper/AvailabilityScheduleCalendarEventWrapper"
import { AvailabilityScheduleCalendarToolbar } from "./AvailabilityScheduleCalendarToolbar/AvailabilityScheduleCalendarToolbar"

const availabilityCalendarMap = {
  event: Event,
  toolbar: AvailabilityScheduleCalendarToolbar,
  eventWrapper: AvailabilityScheduleCalendarEventWrapper,
  header: RBCHeader,
}

type AvailabilityScheduleCalendarProps = {
  schedules: AvailabilityScheduleFragmentFragment[]
  providerId: string
}

export const AvailabilityScheduleCalendar = ({
  schedules,
  providerId,
}: AvailabilityScheduleCalendarProps) => {
  const [date, setDate] = useState<Date>(new Date())

  const { scheduleId } = useParams<{
    scheduleId: string
    serviceId: string
  }>()

  const navigate = useNavigate()

  const schedule = schedules.find((s) => s.id === scheduleId)

  const availabilitySchedules = useMemo(
    () =>
      getAvailabilityBlocks(schedule ? [schedule] : [], date, "week").map(
        (el) => ({
          ...el,
          resourceId: providerId,
        })
      ),
    [schedule, date, providerId]
  )

  useEffect(() => {
    if (schedule && schedule.fromDate) {
      setDate(new Date(schedule.fromDate))
    }
  }, [schedule])

  return (
    <ReactBigCalendar
      formats={formats}
      localizer={localizer}
      date={date}
      events={availabilitySchedules}
      components={availabilityCalendarMap}
      className={styles.availabilityScheduleCalendar}
      onNavigate={(date) => setDate(date)}
      dayLayoutAlgorithm="no-overlap"
      defaultView={"week"}
      views={["week"]}
      onSelectEvent={(e) => {
        navigate(
          generatePath(RouteStrings.calendarSchedule, {
            providerId: providerId,
            scheduleId,
            serviceId: e.resource.id,
          })
        )
      }}
      scrollToTime={getScrollTimeAt7()}
      {...calendarVariables}
      selectable={false}
    />
  )
}
