import { useEffect } from "react"
import { generatePath, useNavigate, useParams } from "react-router-dom"

import { Redirect } from "routes/PrivateRoutes"
import { RouteStrings } from "routes/RouteStrings"
import { Loading } from "ui"

import {
  AvailabilityScheduleFragmentFragment,
  useGetAvailabilitySchedulesQuery,
} from "generated/graphql"

import styles from "./AvailabilitySchedules.module.css"
import { AvailabilityScheduleCalendar } from "./components/AvailabilityScheduleCalendar/AvailabilityScheduleCalendar"
import AvailabilityScheduleSidebar from "./components/AvailabilityScheduleSidebar/AvailabilityScheduleSidebar"
import { sortSchedules } from "./sortSchedules"

const AvailabilitySchedules = ({
  schedules,
  provider,
}: {
  schedules: AvailabilityScheduleFragmentFragment[]
  provider: {
    id: string
    name: string
    teams: { id: string; name: string }[]
  }
}) => {
  const { scheduleId } = useParams<{
    scheduleId: string
  }>()

  const navigate = useNavigate()

  // Schedules should always have fromdate and todate
  const filteredSchedules = schedules.filter(
    (s) => s.fromDate !== null || s.toDate !== null
  )

  // Sort schedules so the first schedule is the one that starts nearest to the current date (in the future)
  const sortedSchedules = filteredSchedules.sort(sortSchedules)

  useEffect(() => {
    if (!scheduleId && sortedSchedules.length > 0) {
      // if there is no active schedule, redirect to the first schedule
      navigate(
        generatePath(RouteStrings.calendarSchedule, {
          providerId: provider.id,
          scheduleId: sortedSchedules[0].id,
        })
      ),
        { replace: true }
    }
  }, [scheduleId, sortedSchedules])

  return (
    <div className={styles.container}>
      <AvailabilityScheduleSidebar
        provider={provider}
        schedules={sortedSchedules}
      />
      <AvailabilityScheduleCalendar
        providerId={provider.id}
        schedules={schedules}
      />
    </div>
  )
}

const AvailabilitySchedulesWrapper = () => {
  const { providerId } = useParams<{ providerId: string }>()

  // Provider ID should always be present but redirect to calendar if not
  if (!providerId) return <Redirect to={generatePath(RouteStrings.calendar)} />

  const { data, loading, error } = useGetAvailabilitySchedulesQuery({
    variables: {
      providerId,
      status: null,
    },
  })

  const schedules = data?.availabilitySchedules || []

  if (error) {
    return <div>Error: {error.message}</div>
  }

  if (loading) {
    return <Loading className={styles.loading} />
  }

  return (
    <AvailabilitySchedules
      schedules={schedules}
      provider={{
        id: providerId || "",
        name: data?.provider?.name || "",
        teams: data?.provider?.teams || [],
      }}
    />
  )
}

export default AvailabilitySchedulesWrapper
