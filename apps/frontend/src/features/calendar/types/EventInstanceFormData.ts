import { Participant } from "features/calendar/components/Participants/Participants"

import { EventInstanceStatus, EventType, Scalars } from "generated/graphql"

export type EventInstanceFormData = {
  eventType: EventType
  eventStatus: EventInstanceStatus
  title: string
  description: string
  fromDate: string
  toDate: string
  participants: Participant[]
  repeatInterval: string
  ownerId: string
  teamId?: string
  serviceTypeId?: string
  locationId?: string
  untilDate?: string
  providerId?: string | null
}
