import { describe, it, expect } from "vitest"

import { ListItemType } from "generated/graphql"

import { WL_JOURNAL_ENTRY } from "../components/WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "../components/WorklistToolbar/WorklistStatusFilter"
import {
  getTypeOptions,
  getValidTypesForStatus,
  getWorklistFilterTypesFromParams,
} from "./worklistTypeUtils"

describe("worklistTypeUtils", () => {
  describe("getTypeOptions", () => {
    const baseTypes = [
      ListItemType.AppointmentRequest,
      ListItemType.Referral,
      ListItemType.DoctorLetter,
    ]

    it("should return correct types by status", () => {
      expect(
        getTypeOptions(WorklistFilterStatus.TO_DO, false, false).map(
          (o) => o.value
        )
      ).toEqual(baseTypes)
      expect(
        getTypeOptions(WorklistFilterStatus.RESOLVED, false, false).map(
          (o) => o.value
        )
      ).toEqual(baseTypes)

      expect(
        getTypeOptions(WorklistFilterStatus.IN_PROGRESS, false, false).map(
          (o) => o.value
        )
      ).toEqual([ListItemType.AppointmentRequest, ListItemType.Referral])
    })

    it("should only show Journal Entries when the status is To Do, the user has permission, and the provider is selected", () => {
      expect(
        getTypeOptions(WorklistFilterStatus.TO_DO, true, false).map(
          (o) => o.value
        )
      ).toContain(WL_JOURNAL_ENTRY)

      const exclusionCases: [WorklistFilterStatus, boolean, boolean][] = [
        [WorklistFilterStatus.TO_DO, false, false], // no permission
        [WorklistFilterStatus.TO_DO, true, true], // org view
        [WorklistFilterStatus.IN_PROGRESS, true, false], // wrong status
        [WorklistFilterStatus.RESOLVED, true, false], // wrong status
      ]

      exclusionCases.forEach(([status, hasPermission, isOrg]) => {
        expect(
          getTypeOptions(status, hasPermission, isOrg).map((o) => o.value)
        ).not.toContain(WL_JOURNAL_ENTRY)
      })
    })
  })

  describe("getValidTypesForStatus", () => {
    it("should return empty array when no current types", () => {
      const result = getValidTypesForStatus(
        [],
        WorklistFilterStatus.TO_DO,
        false,
        false
      )
      expect(result).toEqual([])
    })

    it("should return current types when all are valid for the new status", () => {
      const validTypes = [
        ListItemType.AppointmentRequest,
        ListItemType.Referral,
      ]

      expect(
        getValidTypesForStatus(
          validTypes,
          WorklistFilterStatus.TO_DO,
          false,
          false
        )
      ).toEqual(validTypes)
      expect(
        getValidTypesForStatus(
          validTypes,
          WorklistFilterStatus.IN_PROGRESS,
          false,
          false
        )
      ).toEqual(validTypes)
    })

    it("should return empty array when any current type is invalid for the new status", () => {
      const typesWithDoctorLetter = [
        ListItemType.DoctorLetter,
        ListItemType.AppointmentRequest,
      ]
      expect(
        getValidTypesForStatus(
          typesWithDoctorLetter,
          WorklistFilterStatus.IN_PROGRESS,
          false,
          false
        )
      ).toEqual([])
    })
  })

  describe("getWorklistFilterTypesFromParams", () => {
    it("should return empty array for null input or empty string", () => {
      expect(getWorklistFilterTypesFromParams(null)).toEqual([])
      expect(getWorklistFilterTypesFromParams("")).toEqual([])
    })

    it("should filter out invalid types", () => {
      const result = getWorklistFilterTypesFromParams(
        "APPOINTMENT_REQUEST,INVALID_TYPE,REFERRAL"
      )
      expect(result).toEqual([
        ListItemType.AppointmentRequest,
        ListItemType.Referral,
      ])
    })

    it("should remove duplicates", () => {
      const result = getWorklistFilterTypesFromParams(
        "APPOINTMENT_REQUEST,APPOINTMENT_REQUEST,REFERRAL"
      )
      expect(result).toEqual([
        ListItemType.AppointmentRequest,
        ListItemType.Referral,
      ])
    })

    it("should handle all valid enum values", () => {
      const allTypes =
        "APPOINTMENT_REQUEST,DOCTOR_LETTER,REFERRAL,JOURNAL_ENTRY"
      const result = getWorklistFilterTypesFromParams(allTypes)
      expect(result).toEqual([
        ListItemType.AppointmentRequest,
        ListItemType.DoctorLetter,
        ListItemType.Referral,
        WL_JOURNAL_ENTRY,
      ])
    })

    it("should handle whitespace and empty segments", () => {
      const result = getWorklistFilterTypesFromParams(
        "APPOINTMENT_REQUEST,,REFERRAL,"
      )
      expect(result).toEqual([
        ListItemType.AppointmentRequest,
        ListItemType.Referral,
      ])
    })
  })
})
