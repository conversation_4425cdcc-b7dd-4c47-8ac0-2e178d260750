import { i18nInstance } from "i18n"

import { ListItemType } from "generated/graphql"

import {
  WL_JOURNAL_ENTRY,
  WorklistItemType,
} from "../components/WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "../components/WorklistToolbar/WorklistStatusFilter"

export const getTypeLabel = (typeValue: WorklistItemType): string => {
  if (typeValue === WL_JOURNAL_ENTRY) {
    return i18nInstance.t("features:worklist.typeJournalEntry")
  }

  return i18nInstance.t(`enums:ListItemType.${typeValue}`)
}

export const getTypeOptions = (
  currentStatus: WorklistFilterStatus,
  canViewSubjectJournal: boolean,
  isOrg: boolean
): Array<{ label: string; value: WorklistItemType }> => {
  const options: Array<{
    label: string
    value: WorklistItemType
  }> = [
    {
      label: getTypeLabel(ListItemType.AppointmentRequest),
      value: ListItemType.AppointmentRequest,
    },
    {
      label: getTypeLabel(ListItemType.Referral),
      value: ListItemType.Referral,
    },
  ]

  if (currentStatus !== WorklistFilterStatus.IN_PROGRESS) {
    options.push({
      label: getTypeLabel(ListItemType.DoctorLetter),
      value: ListItemType.DoctorLetter,
    })
  }

  const showJournalEntryOption =
    canViewSubjectJournal &&
    !isOrg &&
    currentStatus === WorklistFilterStatus.TO_DO
  if (showJournalEntryOption) {
    options.push({
      label: getTypeLabel(WL_JOURNAL_ENTRY),
      value: WL_JOURNAL_ENTRY,
    })
  }

  return options
}

/**
 * Helper function to get valid types based on status, permissions, and org context.
 * Returns an empty array (showing "Everything") if ANY of the current types are not available in the new status.
 * This ensures a clear filter state when the status changes and makes some types unavailable.
 */
export const getValidTypesForStatus = (
  currentTypes: WorklistItemType[],
  newStatus: WorklistFilterStatus,
  canViewSubjectJournal: boolean,
  isOrg: boolean
): WorklistItemType[] => {
  if (currentTypes.length === 0) {
    return []
  }

  const typeOptions = getTypeOptions(newStatus, canViewSubjectJournal, isOrg)
  const availableTypes = typeOptions.map(({ value }) => value)

  const allTypesAreAvailable = currentTypes.every((type) =>
    availableTypes.includes(type)
  )

  return allTypesAreAvailable ? currentTypes : []
}

export const getWorklistFilterTypesFromParams = (
  typeParam: string | null
): WorklistItemType[] => {
  if (!typeParam) {
    return []
  }

  const allTypes = new Set<WorklistItemType>([
    ...Object.values(ListItemType),
    WL_JOURNAL_ENTRY,
  ])

  const types = typeParam
    .split(",")
    .filter((type) => allTypes.has(type as WorklistItemType))

  return Array.from(new Set(types)) as WorklistItemType[]
}
