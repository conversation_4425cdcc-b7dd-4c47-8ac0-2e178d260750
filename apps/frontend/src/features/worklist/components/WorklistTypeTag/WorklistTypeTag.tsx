import c from "classnames"
import { ElementType } from "react"
import { useTranslation } from "react-i18next"

import { IconName } from "@leviosa/assets"
import { Icon } from "@leviosa/components"

import { Color } from "styles/colors"
import { Tag, TagProps } from "ui"

import { ListItemType } from "generated/graphql"

import {
  WL_JOURNAL_ENTRY,
  WorklistItemType,
} from "../WorklistItem/WorklistItem"
import styles from "./WorklistTypeTag.module.css"

const typeIconMap = {
  [ListItemType.AppointmentRequest]: "calendar-2-line",
  [ListItemType.DoctorLetter]: "mail-line",
  [ListItemType.Referral]: "file-add-line",
  JOURNAL_ENTRY: "draft-line",
} as Record<WorklistItemType, IconName>

const typeColorMap = {
  [ListItemType.AppointmentRequest]: "levGreen",
  [ListItemType.DoctorLetter]: "orange",
  [ListItemType.Referral]: "pink",
  JOURNAL_ENTRY: "blue",
} as Record<WorklistItemType, Color>

type WorklistTypeTagProps<E extends ElementType> = {
  type: WorklistItemType
} & Omit<TagProps<E>, "children">

export default function WorklistTypeTag<E extends ElementType = "h3">({
  type,
  className,
  ...rest
}: WorklistTypeTagProps<E>) {
  const { t: tEnum } = useTranslation("enums", {
    keyPrefix: "ListItemType",
  })
  const { t } = useTranslation("features", { keyPrefix: "worklist" })

  return (
    <Tag
      as="h3"
      size="small"
      color={typeColorMap[type]}
      className={c(className, styles.tag)}
      {...rest}
    >
      <Icon name={typeIconMap[type]} className={styles.icon} />
      {type === WL_JOURNAL_ENTRY ? t("journalEntryTagLabel") : tEnum(type)}
    </Tag>
  )
}
