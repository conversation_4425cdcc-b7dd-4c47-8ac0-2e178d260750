fragment WorklistItemFragment on ListItem {
  id
  priority
  comment
  createdAt
  itemType
  assignee {
    id
    name
    specialty
  }
  ... on AppointmentRequestItem {
    subject {
      ...WorklistSubjectFragment
    }
  }
  ... on DoctorLetterItem {
    subject {
      ...WorklistSubjectFragment
    }
    inboundData {
      ...WorklistInboundDataFragment
    }
  }
  ... on ReferralItem {
    subject {
      ...WorklistSubjectFragment
    }
    inboundData {
      ...WorklistInboundDataFragment
    }
  }
}
