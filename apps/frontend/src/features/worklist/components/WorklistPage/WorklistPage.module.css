.wrap {
  display: grid;
  grid-template-columns: minmax(400px, 1fr) minmax(400px, 1fr);
  grid-template-rows: min-content 1fr;
  grid-template-areas:
    "header itemDetails"
    "items itemDetails";
  height: calc(100vh - var(--header-height));
  overflow: hidden;
  margin-top: -24px;
  --worklist-border-separator: 1px solid var(--color-neutral-200);
}

.listSection {
  display: flex;
  flex-direction: column;
  padding-top: 0;
  min-height: 0; /* required for flex children to shrink and allowing the list to scroll */
  grid-area: items;
  border-right: var(--worklist-border-separator);
}

.statusSelect {
  margin-left: -8px;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  grid-area: header;
  gap: 8px;
  border-right: var(--worklist-border-separator);
}

.header h1 {
  margin-right: auto;
}

.listContainer {
  flex: 1 1 0%;
  min-height: 0;
  overflow-y: auto;
  max-height: calc(100vh - var(--header-height) - 130px);
}

.list li {
  animation: enterDown 0.2s ease-out;
  animation-fill-mode: both;
  --stagger: 20ms;
  animation-delay: calc(9 * var(--stagger));

  &:first-child {
    animation-delay: 0ms;
  }
  &:nth-child(2) {
    animation-delay: var(--stagger);
  }
  &:nth-child(2) {
    animation-delay: calc(var(--stagger) * 3);
  }
  &:nth-child(3) {
    animation-delay: calc(var(--stagger) * 4);
  }
  &:nth-child(4) {
    animation-delay: calc(var(--stagger) * 5);
  }
  &:nth-child(5) {
    animation-delay: calc(var(--stagger) * 6);
  }
  &:nth-child(6) {
    animation-delay: calc(var(--stagger) * 7);
  }
  &:nth-child(7) {
    animation-delay: calc(var(--stagger) * 8);
  }
}

.footer {
  margin: 40px 0;
}

@keyframes enterDown {
  from {
    opacity: 0;
    transform: translateY(-1rem);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.itemDetails {
  grid-area: itemDetails;
}

.noItemWrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  padding: 40px;
  gap: 8px;
}

.noItemWrapper > svg {
  width: 200px;
  height: auto;
  margin-bottom: 16px;
}
