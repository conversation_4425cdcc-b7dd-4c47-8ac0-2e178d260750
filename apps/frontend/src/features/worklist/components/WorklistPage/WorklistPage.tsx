import { Suspense, useCallback, useEffect, useMemo } from "react"
import { useTranslation } from "react-i18next"
import { Link, Outlet, useMatch, useSearchParams } from "react-router-dom"

import ComputerIllustration from "@leviosa/assets/illustrations/computer.svg?react"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import { Pagination } from "components/Pagination/Pagination"
import usePermissions from "features/authentication/hooks/usePermissions"
import WorklistItem, {
  WorklistItemType,
} from "features/worklist/components/WorklistItem/WorklistItem"
import { PrivateRoutes, RouteStrings } from "routes/RouteStrings"
import { Button, Heading, Loading, Text } from "ui"

import { PermissionKey } from "generated/graphql"

import { useWorklistData } from "../../hooks/useWorklistData"
import {
  getValidTypesForStatus,
  getWorklistFilterTypesFromParams,
} from "../../utils/worklistTypeUtils"
import {
  getWorklistFilterStatus,
  WorklistFilterStatus,
} from "../WorklistToolbar/WorklistStatusFilter"
import { WorklistToolbar } from "../WorklistToolbar/WorklistToolbar"
import styles from "./WorklistPage.module.css"

export default function WorklistPage() {
  const isOrg = !!useMatch(RouteStrings.worklistOrg + "/*")
  const { t } = useTranslation("features", { keyPrefix: "worklist" })
  const { globalData } = useGlobalState()
  const { hasPermission } = usePermissions()
  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const [searchParams, setSearchParams] = useSearchParams()

  const statusSearchParam = searchParams.get("status")
  const selectedStatus: WorklistFilterStatus =
    getWorklistFilterStatus(statusSearchParam)

  const typeSearchParam = searchParams.get("type")
  const selectedTypes = useMemo(
    (): WorklistItemType[] => getWorklistFilterTypesFromParams(typeSearchParam),
    [typeSearchParam]
  )

  const pageSearchParam = searchParams.get("page")
  const currentPage = parseInt(pageSearchParam ?? "1", 10) || 1

  const updateTypeSearchParams = useCallback(
    (newTypes: WorklistItemType[]) => {
      setSearchParams((prev) => {
        const newParams = new URLSearchParams(prev)
        if (newTypes.length > 0) {
          newParams.set("type", newTypes.join(","))
        } else {
          newParams.delete("type")
        }
        newParams.delete("page")
        return newParams
      })
    },
    [setSearchParams]
  )

  const handleStatusChange = (newStatus: WorklistFilterStatus) => {
    setSearchParams((prev) => {
      const newParams = new URLSearchParams(prev)
      newParams.set("status", newStatus)

      const currentTypeParam = newParams.get("type")
      const currentTypes = getWorklistFilterTypesFromParams(currentTypeParam)

      const validTypes = getValidTypesForStatus(
        currentTypes,
        newStatus,
        canViewSubjectJournal,
        isOrg
      )

      if (validTypes.length > 0) {
        newParams.set("type", validTypes.join(","))
      } else {
        newParams.delete("type")
      }

      newParams.delete("page")
      return newParams
    })
  }

  const handleTypeChange = useCallback(
    (newType: WorklistItemType) => {
      const updatedTypes = selectedTypes.includes(newType)
        ? selectedTypes.filter((type) => type !== newType)
        : [...selectedTypes, newType]

      updateTypeSearchParams(updatedTypes)
    },
    [selectedTypes, updateTypeSearchParams]
  )

  useEffect(() => {
    const validTypes = getValidTypesForStatus(
      selectedTypes,
      selectedStatus,
      canViewSubjectJournal,
      isOrg
    )

    if (
      validTypes.length !== selectedTypes.length ||
      !validTypes.every((type) => selectedTypes.includes(type))
    ) {
      updateTypeSearchParams(validTypes)
    }
  }, [
    isOrg,
    selectedTypes,
    selectedStatus,
    canViewSubjectJournal,
    updateTypeSearchParams,
  ])

  const searchParamsWithoutPage = useMemo(() => {
    const params = new URLSearchParams(searchParams)
    params.delete("page")
    return params.toString()
  }, [searchParams])

  return (
    <div className={styles.wrap}>
      <header className={styles.header}>
        <Heading as="h1" size="large">
          {t("mainHeading")}
        </Heading>
        <Button
          as={Link}
          to={{
            pathname: PrivateRoutes.worklist,
            search: searchParamsWithoutPage,
          }}
          size="small"
          variant={isOrg ? "clear" : "filled-light"}
        >
          {globalData.actor.name}
        </Button>
        <Button
          as={Link}
          to={{
            pathname: PrivateRoutes.worklistOrg,
            search: searchParamsWithoutPage,
          }}
          size="small"
          variant={!isOrg ? "clear" : "filled-light"}
        >
          {globalData.actor.organisation.name || "Organisation"}
        </Button>
      </header>
      <section className={styles.listSection}>
        <WorklistToolbar
          isOrg={isOrg}
          status={selectedStatus}
          types={selectedTypes}
          onStatusChange={handleStatusChange}
          onTypeChange={handleTypeChange}
        />

        <Suspense fallback={<Loading />}>
          <Worklist
            isOrg={isOrg}
            selectedStatus={selectedStatus}
            selectedTypes={selectedTypes}
            currentPage={currentPage}
          />
        </Suspense>
      </section>
      <section className={styles.itemDetails}>
        <Outlet />
      </section>
    </div>
  )
}

type WorklistProps = {
  isOrg?: boolean
  selectedStatus: WorklistFilterStatus
  selectedTypes: WorklistItemType[]
  currentPage: number
}
const Worklist = ({
  isOrg = false,
  selectedStatus,
  selectedTypes,
  currentPage,
}: WorklistProps) => {
  const { globalData } = useGlobalState()
  const providerId = globalData?.actor.id

  const { items, totalCount, totalPages } = useWorklistData({
    isOrg,
    selectedStatus,
    providerId,
    selectedTypes,
    currentPage,
  })

  if (items.length === 0 && totalCount === 0) {
    // If To do is selected and all types (none) then show all caught up msg
    // otherwise show no items match your current filters
    const allCaughtUp =
      selectedStatus === WorklistFilterStatus.TO_DO &&
      (selectedTypes.length === 0 || selectedTypes.length === 4)

    return <NoWorklistItems allCaughtUp={allCaughtUp} />
  }

  return (
    <div className={styles.listContainer}>
      <ul className={styles.list}>
        {items.map((item) => (
          <li key={`${item.id}-${isOrg ? "org" : ""}`}>
            <WorklistItem
              {...item}
              isOrg={isOrg}
              selectedStatus={selectedStatus}
            />
          </li>
        ))}
      </ul>

      <div className={styles.footer}>
        {totalPages > 1 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            pageAsQueryParam={true}
          />
        )}
      </div>
    </div>
  )
}

const NoWorklistItems = ({ allCaughtUp }: { allCaughtUp: boolean }) => {
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })

  return (
    <div className={styles.noItemWrapper}>
      <ComputerIllustration />
      {allCaughtUp ? (
        <>
          <Heading>{t("allCaughtUp")}</Heading>
          <Text>{t("noIncomingTasksAtm")}</Text>
        </>
      ) : (
        <>
          <Heading>{t("noItemsMatchFilters")}</Heading>
          <Text>{t("broadenYourSearchToSeeMore")}</Text>
        </>
      )}
    </div>
  )
}
