import {
  generate<PERSON><PERSON>,
  <PERSON>,
  useResolvedPath,
  usePara<PERSON>,
  useLocation,
} from "react-router-dom"

import { Toolt<PERSON> } from "components/Ariakit/Tooltip/Tooltip"
import { ListItemAssigneeSelect } from "components/ListItemAssigneeSelect/ListItemAssigneeSelect"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { SubjectInfoSection } from "components/SubjectDetailsCard/SubjectDetailsCard"
import { PriorityIcon } from "features/waiting-list/components/PrioritySelect/PriorityIcon"
import WorklistTypeTag from "features/worklist/components/WorklistTypeTag/WorklistTypeTag"
import { RouteStrings } from "routes/RouteStrings"
import { Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  ListItemPriority,
  ListItemType,
  namedOperations,
  ProviderSpecialty,
  WorklistSubjectFragmentFragment,
} from "generated/graphql"

import { WorklistFilterStatus } from "../WorklistToolbar/WorklistStatusFilter"
import styles from "./WorklistItem.module.css"

export const WL_JOURNAL_ENTRY = "JOURNAL_ENTRY" as const

export type WorklistItemType = ListItemType | typeof WL_JOURNAL_ENTRY

function getPath(type: WorklistItemType, id: string) {
  if (type === WL_JOURNAL_ENTRY) {
    return generatePath(`./${RouteStrings.worklistEntry}`, {
      journalEntryId: id,
    })
  }

  return generatePath(`./${RouteStrings.worklistItem}`, {
    listItemId: id,
  })
}

type ItemLinkProps = {
  type: WorklistItemType
  className: string
  children: React.ReactNode
  id: string
}

const ItemLink = ({
  type,
  className,
  children,
  id,
  ...rest
}: ItemLinkProps) => {
  const { pathname } = useResolvedPath(getPath(type, id))
  const { search } = useLocation()

  const to = {
    pathname,
    search,
  }

  return (
    <Link to={to} className={className} {...rest}>
      {children}
    </Link>
  )
}

type WorklistItemProps = {
  id: string
  subject: WorklistSubjectFragmentFragment
  content: string | null
  createdAt: string
  priority: ListItemPriority | "medium-high" | null
  type: WorklistItemType
  assignee: { id: string; name: string; specialty: ProviderSpecialty } | null
  isOrg: boolean
  selectedStatus: WorklistFilterStatus
}
export default function WorklistItem({
  id,
  subject,
  content,
  createdAt,
  priority,
  type,
  assignee,
  isOrg,
  selectedStatus,
}: WorklistItemProps) {
  const formatDate = useDateFormatter()
  const formatTime = useTimeFormatter()
  const { listItemId, journalEntryId } = useParams()

  const date = new Date(createdAt)

  const isActive = listItemId === id || journalEntryId === id

  return (
    <div className={styles.wrapper}>
      <ItemLink
        id={id}
        type={type}
        className={styles.link}
        aria-current={isActive ? "page" : undefined}
      >
        <Text as="h2" size="large" weight="bold" className={styles.name}>
          <PiiSensitive>{subject.name}</PiiSensitive>
          <SubjectInfoSection {...subject} />
        </Text>

        <div className={styles.typeAndDate}>
          <WorklistTypeTag type={type} />

          <Tooltip tooltipContent={formatTime(date)} tabIndex={-1}>
            <Text as="time" size="small" secondary dateTime={createdAt}>
              {formatDate(date)}
            </Text>
          </Tooltip>
        </div>

        {priority !== "medium-high" && (
          <PriorityIcon priority={priority} className={styles.priority} />
        )}
        {content && <p className={styles.content}>{content}</p>}
      </ItemLink>

      <div className={styles.assignee}>
        {type !== WL_JOURNAL_ENTRY && isOrg && (
          <ListItemAssigneeSelect
            isEditable={selectedStatus !== WorklistFilterStatus.RESOLVED}
            id={id}
            value={assignee?.name}
            portal
            placement="bottom-end"
            refetchQueries={
              !isOrg
                ? [
                    namedOperations.Query.GetProviderWorklist,
                    namedOperations.Query.ProviderBox,
                  ]
                : [namedOperations.Query.ProviderBox]
            }
          />
        )}
      </div>
    </div>
  )
}
