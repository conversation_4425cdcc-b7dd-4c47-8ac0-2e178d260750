import { ReactNode, useState } from "react"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  Link,
  useResolvedPath,
  useParams,
  useLocation,
} from "react-router-dom"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { ListItemAssigneeSelect } from "components/ListItemAssigneeSelect/ListItemAssigneeSelect"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { SubjectInfoSection } from "components/SubjectDetailsCard/SubjectDetailsCard"
import { PriorityIcon } from "features/waiting-list/components/PrioritySelect/PriorityIcon"
import { WorklistFilterStatus } from "features/worklist/components/WorklistToolbar/WorklistStatusFilter"
import WorklistTypeTag from "features/worklist/components/WorklistTypeTag/WorklistTypeTag"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Text } from "ui"
import useDateFormatter from "utils/useDateFormatter"
import useTimeFormatter from "utils/useTimeFormatter"

import {
  ListItemPriority,
  ListItemType,
  namedOperations,
  ProviderSpecialty,
  WorklistSubjectFragmentFragment,
} from "generated/graphql"

import styles from "./WorklistItem.module.css"

export const WL_JOURNAL_ENTRY = "JOURNAL_ENTRY" as const

export type WorklistItemType = ListItemType | typeof WL_JOURNAL_ENTRY

function getPath(type: WorklistItemType, id: string) {
  if (type === WL_JOURNAL_ENTRY) {
    return generatePath(`./${RouteStrings.worklistEntry}`, {
      journalEntryId: id,
    })
  }

  return generatePath(`./${RouteStrings.worklistItem}`, {
    listItemId: id,
  })
}

type ItemLinkProps = {
  type: WorklistItemType
  className: string
  children: React.ReactNode
  id: string
}

const ItemLink = ({
  type,
  className,
  children,
  id,
  ...rest
}: ItemLinkProps) => {
  const { pathname } = useResolvedPath(getPath(type, id))
  const { search } = useLocation()

  const to = {
    pathname,
    search,
  }

  return (
    <Link to={to} className={className} {...rest}>
      {children}
    </Link>
  )
}

const AssigneeSelect = ({
  isOrg,
  allowReassign,
  id,
  assigneeName,
}: {
  isOrg: boolean
  allowReassign: boolean
  id: string
  assigneeName: string | undefined
}) => {
  const { t } = useTranslation("features", { keyPrefix: "worklist" })
  const [showReassignSelect, setShowReassignSelect] = useState(isOrg)
  let content: ReactNode = null
  if ((allowReassign && showReassignSelect) || (!allowReassign && isOrg)) {
    content = (
      <ListItemAssigneeSelect
        isEditable={allowReassign}
        id={id}
        value={assigneeName}
        portal
        placement="bottom-end"
        open={!isOrg ? true : undefined}
        onClose={() => !isOrg && setShowReassignSelect(false)}
        refetchQueries={
          !isOrg
            ? [
                namedOperations.Query.GetProviderWorklist,
                namedOperations.Query.ProviderBox,
              ]
            : [namedOperations.Query.ProviderBox]
        }
      />
    )
  }
  if (allowReassign && !showReassignSelect) {
    content = (
      <Button
        variant="clear"
        className={styles.reassignButton}
        onClick={() => setShowReassignSelect(true)}
      >
        <Text secondary>{t("reassign")}</Text>
      </Button>
    )
  }

  return <div className={styles.assignee}>{content}</div>
}

type WorklistItemProps = {
  id: string
  subject: WorklistSubjectFragmentFragment
  content: string | null
  createdAt: string
  priority: ListItemPriority | "medium-high" | null
  type: WorklistItemType
  assignee: { id: string; name: string; specialty: ProviderSpecialty } | null
  isOrg: boolean
  selectedStatus: WorklistFilterStatus
}
export default function WorklistItem({
  id,
  subject,
  content,
  createdAt,
  priority,
  type,
  assignee,
  isOrg,
  selectedStatus,
}: WorklistItemProps) {
  const formatDate = useDateFormatter()
  const formatTime = useTimeFormatter()
  const { listItemId, journalEntryId } = useParams()

  const date = new Date(createdAt)

  const isActive = listItemId === id || journalEntryId === id

  return (
    <div className={styles.wrapper}>
      <ItemLink
        id={id}
        type={type}
        className={styles.link}
        aria-current={isActive ? "page" : undefined}
      >
        <Text as="h2" size="large" weight="bold" className={styles.name}>
          <PiiSensitive>{subject.name}</PiiSensitive>
          <SubjectInfoSection {...subject} />
        </Text>

        <div className={styles.typeAndDate}>
          <WorklistTypeTag type={type} />

          <Tooltip tooltipContent={formatTime(date)} tabIndex={-1}>
            <Text as="time" size="small" secondary dateTime={createdAt}>
              {formatDate(date)}
            </Text>
          </Tooltip>
        </div>

        {priority !== "medium-high" && (
          <PriorityIcon priority={priority} className={styles.priority} />
        )}
        {content && <p className={styles.content}>{content}</p>}
      </ItemLink>

      <AssigneeSelect
        isOrg={isOrg}
        allowReassign={
          type !== WL_JOURNAL_ENTRY &&
          selectedStatus !== WorklistFilterStatus.RESOLVED
        }
        id={id}
        assigneeName={assignee?.name}
      />
    </div>
  )
}
