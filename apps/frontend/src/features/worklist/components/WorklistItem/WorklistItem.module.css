.wrapper {
  position: relative;
  display: grid;
  grid-template-columns: min-content 1fr auto;
  grid-auto-rows: auto;
  grid-template-areas:
    "typeAndDate typeAndDate assignee"
    "name name name"
    "content content content";
  align-items: center;
  gap: 8px 12px;
  border-bottom: 1px solid var(--color-neutral-200);
  color: inherit;
  transition: background-color 0.1s linear;
}

.wrapper:has(.priority) {
  grid-template-areas:
    "priority typeAndDate assignee"
    "name name name"
    "content content content";
}

.link {
  grid-column: 1 / -1;
  grid-row: 1 / -1;
  display: grid;
  grid-template-columns: subgrid;
  grid-template-rows: subgrid;
  grid-template-areas: inherit;
  color: inherit;
  text-decoration: none;
  padding: 24px;
}

.link[aria-current="page"] {
  background-color: var(--color-background-hover);
}

.name {
  grid-area: name;
  display: flex;
  align-items: baseline;
  gap: 8px;
}

.typeAndDate {
  grid-area: typeAndDate;
  display: flex;
  gap: 16px;
}

.priority {
  grid-area: priority;
  font-size: 24px;
}

.assignee {
  grid-area: assignee;
  padding-right: 40px;
  padding-top: 24px;
  --color-text: var(--color-initial-text);

  &:not(:has(:is(button, div))) {
    /* Let the pointer event flow through to the link
     * when reassign is disabled */
    pointer-events: none;
  }
}

/* Prevent hover state on card when assignee select is open and hovered */
.wrapper:has(.assignee:hover) .link {
  background-color: transparent;
}

.content {
  grid-area: content;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: pre-line;
}
.reassignButton {
  opacity: 0;
  transition: opacity 100ms linear;
  margin: -4px 0;
}
.wrapper:hover .reassignButton,
.wrapper:has(a[aria-current="page"]) .reassignButton {
  opacity: 1;
  transition: opacity 300ms linear;
  transition-delay: 300ms;
}
