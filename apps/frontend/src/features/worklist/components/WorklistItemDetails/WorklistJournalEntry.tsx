import c from "classnames"
import { useTranslation } from "react-i18next"

import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { EntryDate } from "features/subject-journal/components/EntryDate/EntryDate"
import JournalAttachments from "features/subject-journal/components/JournalAttachments/JournalAttachments"
import JournalBlockLink from "features/subject-journal/components/JournalBlockLink/JournalBlockLink"
import JournalEntryBlockDrugPrescription from "features/subject-journal/components/JournalEntryBlockDrugPrescription/JournalEntryBlockDrugPrescription"
import { JournalEntryFreeText } from "features/subject-journal/components/JournalEntryFreeText/JournalEntryFreeText"
import MedicalCertificateSupplement from "features/subject-journal/components/MedicalCertificateSupplement/MedicalCertificateSupplement"
import { OutboundMessage } from "features/subject-journal/components/OutboundMessage/OutboundMessage"
import { color } from "styles/colors"
import { Heading, Tag, Text } from "ui"
import ClinicalCodingCard from "ui/components/ClinicalCodingCard/ClinicalCodingCard"
import { ProviderHoverInfoLink } from "ui/components/ProviderBadgeMenu/ProviderHoverInfoLink/ProviderHoverInfoLink"
import { isTypename } from "utils/isTypename"

import {
  ExternalMessage,
  GetJournalEntryQuery,
  JournalEntryFragmentFragment,
  LanguageId,
} from "generated/graphql"

import styles from "./WorklistItemDetails.module.css"

type SupplementsProps = {
  supplements: JournalEntryFragmentFragment["blocks"]
  journalEntryId: string
}
const Supplements = ({ supplements, journalEntryId }: SupplementsProps) => {
  if (supplements.length === 0) return

  const attachments = supplements.filter(isTypename("JournalEntryAttachment"))

  const hasAttachments = attachments.length > 0

  return (
    <div className={styles.supplements}>
      {supplements.map((block) => {
        switch (block.__typename) {
          case "ClinicalCoding":
            return (
              <ClinicalCodingCard
                key={block.id}
                {...block}
                closedAt={block.closedAt}
                editableDescription={false}
                closeableCode={false}
              />
            )
          case "DrugPrescription":
            return (
              <JournalEntryBlockDrugPrescription
                key={block.id}
                {...block}
                languageId={LanguageId.Is}
                canEdit={false}
              />
            )
          case "MedicalCertificate":
            return block.certType === "Absence" ? (
              <MedicalCertificateSupplement
                key={block.id}
                {...block}
                languageId={LanguageId.Is}
                canEdit={false}
              />
            ) : (
              <JournalEntryFreeText
                key={block.id}
                {...block}
                languageId={LanguageId.Is}
                canEdit={false}
              />
            )
          case "OutboundDoctorsLetter":
            return (
              <OutboundMessage
                key={block.id}
                {...block}
                messageType={ExternalMessage.DoctorLetter}
                canEdit={false}
              />
            )
          case "OutboundReferral":
            return (
              <OutboundMessage
                key={block.id}
                {...block}
                messageType={ExternalMessage.Referral}
                canEdit={false}
              />
            )
          default:
            return null
        }
      })}
      {hasAttachments && (
        <div className={styles.attachments}>
          <JournalAttachments
            attachments={attachments}
            journalEntryId={journalEntryId}
            canUpload={false}
          />
        </div>
      )}
    </div>
  )
}

type WorklistJournalEntryProps = {
  journalEntry: GetJournalEntryQuery["journalEntry"]
}

export const WorklistJournalEntry = ({
  journalEntry,
}: WorklistJournalEntryProps) => {
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })
  const { t: tEnum } = useTranslation("enums")

  const { sections, blocks, subject, documentType } = journalEntry

  const journalEntrySupplements = blocks.filter((b) => !isTypename("Note")(b))

  return (
    <div className={c(styles.journalEntry, styles.journalEntryContent)}>
      <div className={styles.journalEntryHeading}>
        <Heading>{journalEntry.title}</Heading>
        <div className={c(styles.journalEntryMeta, color.light)}>
          {documentType && (
            <Tag size="small" color="blue" weight="bold">
              {tEnum(`DocumentType.${documentType}.label`)}
            </Tag>
          )}
          <ProviderHoverInfoLink {...journalEntry.createdBy} />
          <EntryDate date={journalEntry.createdAt} />
        </div>
      </div>
      <div className={styles.journalEntryContent}>
        {sections?.map((section) => {
          return (
            <JournalBlockLink
              key={section.id}
              subjectId={subject.id}
              sectionId={section.id}
              encounterId={null}
              className={styles.journalEntrySection}
            >
              <Heading size="small">{section.title}</Heading>
              {section.content ? (
                <PiiSensitive
                  as="div"
                  className={"editor"}
                  dangerouslySetInnerHTML={{
                    __html: section.content,
                  }}
                />
              ) : (
                <Text secondary>{t("emptyNote")}</Text>
              )}
            </JournalBlockLink>
          )
        })}
        <Supplements
          supplements={journalEntrySupplements}
          journalEntryId={journalEntry.id}
        />
      </div>
    </div>
  )
}
