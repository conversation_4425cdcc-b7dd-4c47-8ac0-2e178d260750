.wrap {
  height: 100%;
  grid-template-rows: min-content auto;
  width: 100%;
  overflow-x: hidden;
}

.itemActions {
  width: 100%;
  display: flex;
  justify-content: end;
  padding: 8px 16px;
}

.detailsHeader {
  width: 100%;
  border-bottom: 1px solid var(--color-neutral-200);
  padding: 16px 32px;
  display: flex;
  justify-content: space-between;
  align-items: center;

  padding: 24px 32px 16px;
  align-items: baseline;
  margin: 0 auto;
  position: sticky;
  top: 0;
  z-index: var(--z-index-sticky-header);
  background: white;
}

.subjectInformation {
  display: grid;
  grid-template-columns: 1fr max-content;
  align-items: start;
}

.openJournalButton {
  margin-top: -6px;
}

.content {
  display: grid;
  grid-template-columns: minmax(0, 1fr);
  padding: 24px 32px;
  gap: 24px;
  overflow-y: auto;
  overflow-x: hidden;
  align-content: start;

  > * {
    width: 100%;
    max-width: 700px;
    margin: 0 auto;
  }
}

.noItemWrapper {
  display: grid;
  justify-content: center;
  height: 100%;
  grid-template-rows: 2fr auto 3fr;
  grid-template-areas:
    ".   "
    "text"
    ".   ";

  > * {
    grid-row: 2;
  }
}

.noItemWrapper > svg {
  width: 200px;
  height: auto;
}

/* Journal entry styles */
.journalEntry {
  background-color: var(--color-lev-blue-200);
  border-radius: var(--radius-button-half);
  padding: 16px 24px;
}

.journalEntryHeading {
  display: grid;
  gap: 8px;
}

.journalEntryMeta {
  display: flex;
  gap: 8px;
}

.journalEntryContent {
  display: grid;
  gap: 32px;
}

.journalEntrySection {
  display: grid;
  gap: 12px;

  --color-text: var(--color-initial-text);
  color: var(--color-text);
  margin: -12px;
  padding: 12px;
  border-radius: 12px;
  transition:
    background-color 100ms,
    scale 100ms;

  &:hover {
    background-color: white;
    cursor: pointer;
  }
  &:active {
    scale: 0.99;
  }
}

/* Reusable supplement layout */
.supplements {
  margin: 0 -12px;
  display: flex;
  gap: 16px;
  flex-flow: wrap;
  align-items: center;
}

.attachments {
  width: 100%;
}
