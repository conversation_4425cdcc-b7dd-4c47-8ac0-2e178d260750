import { ReactNode } from "react"
import { useTranslation } from "react-i18next"
import {
  generatePath,
  Link,
  useLocation,
  useMatch,
  useNavigate,
  useParams,
  useResolvedPath,
} from "react-router-dom"

import StethoscopeIllustration from "@leviosa/assets/illustrations/stethoscope.svg?react"
import { Icon } from "@leviosa/components"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import { SubjectDetailsCard } from "components/SubjectDetailsCard/SubjectDetailsCard"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { InboundData } from "features/subject-journal/components/InboundData/InboundData"
import { WaitingListEntry } from "features/subject-journal/components/InboundData/ReferralWaitingListEntry/ReferralWaitingListEntry"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading } from "ui"

import {
  PermissionKey,
  useGetJournalEntrySuspenseQuery,
  useGetWorklistItemSuspenseQuery,
  WaitingListItemSubjectFragmentFragment,
} from "generated/graphql"

import styles from "./WorklistItemDetails.module.css"
import { WorklistJournalEntry } from "./WorklistJournalEntry"

type DetailsLayoutProps = {
  subject?: WaitingListItemSubjectFragmentFragment | null
  actions?: ReactNode
  children: ReactNode
}

const DetailsLayout = ({ subject, children }: DetailsLayoutProps) => {
  return (
    <div className={styles.wrap}>
      <DetailsHeader subject={subject} />
      <div className={styles.content}>{children}</div>
    </div>
  )
}

export const NoAccess = () => {
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })

  return (
    <div className={styles.noItemWrapper}>
      <Heading secondary>{t("noAccess")}</Heading>
    </div>
  )
}

type DetailsHeaderProps = {
  subject?: WaitingListItemSubjectFragmentFragment | null
}

const DetailsHeader = ({ subject }: DetailsHeaderProps) => {
  const { t } = useTranslation()
  const { t: tFeatures } = useTranslation("features", {
    keyPrefix: "worklist",
  })

  const navigate = useNavigate()
  const { search } = useLocation()

  const { pathname } = useResolvedPath("..") // Navigate one level up

  const handleClose = () => {
    // Navigate to parent route (worklist) without the item ID
    // Preserve any existing query parameters
    navigate({ pathname, search })
  }

  return (
    <div className={styles.detailsHeader}>
      {subject && (
        <div className={styles.subjectInformation}>
          <SubjectDetailsCard {...subject} />
          <Restricted to={PermissionKey.SubjectJournalView}>
            <Button
              as={Link}
              to={generatePath(RouteStrings.subjectJournal, {
                subjectId: subject.id,
              })}
              variant="clear"
              className={styles.openJournalButton}
            >
              {t("openJournal")}
            </Button>
          </Restricted>
        </div>
      )}
      <Tooltip tooltipContent={tFeatures("closeItem")}>
        <Button
          variant="clear"
          size="large"
          icon={<Icon name="close-line" />}
          onClick={handleClose}
        />
      </Tooltip>
    </div>
  )
}

export const ListItemDetails = () => {
  const isOrg = !!useMatch(RouteStrings.worklistOrg + "/*")
  const { t: tEnum } = useTranslation("enums")
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })
  const { listItemId } = useParams()

  const { data, error } = useGetWorklistItemSuspenseQuery({
    variables: {
      filter: {
        // listItemId is always present due to route structure
        ids: [listItemId as string],
      },
    },
  })

  const listItem = data?.listItems?.items[0]

  if (error)
    return (
      <div className={styles.noItemWrapper}>
        <StethoscopeIllustration />
        <Heading>{t("somethingWentWrong")}</Heading>
      </div>
    )

  if (!listItem) return null

  if (listItem.__typename === "AppointmentRequestItem") {
    const waitingListPath = !isOrg
      ? generatePath(RouteStrings.waitingList, {
          pageNumber: 1,
          view: "me",
        })
      : null
    return (
      <DetailsLayout subject={listItem.subject}>
        <Heading>{tEnum(`ListItemType.${listItem.itemType}`)}</Heading>
        <WaitingListEntry
          {...listItem}
          isActive={true}
          waitingListPath={waitingListPath}
        />
      </DetailsLayout>
    )
  }

  if (
    listItem.__typename === "ReferralItem" ||
    listItem.__typename === "DoctorLetterItem"
  ) {
    const subject = listItem.inboundData.listItem?.subject
    return (
      <DetailsLayout subject={subject}>
        <InboundData
          {...listItem.inboundData}
          open
          refetchQueries={[
            isOrg ? "GetOrganisationWorklist" : "GetProviderWorklist",
          ]}
        />
      </DetailsLayout>
    )
  }

  return null
}

export const JournalEntryDetails = () => {
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })
  const { journalEntryId } = useParams()

  const { data, error } = useGetJournalEntrySuspenseQuery({
    variables: {
      // journalEntryId is always present due to route structure
      journalEntryId: journalEntryId as string,
    },
  })

  const journalEntry = data?.journalEntry

  if (error)
    return (
      <div className={styles.noItemWrapper}>
        <StethoscopeIllustration />
        <Heading>{t("somethingWentWrong")}</Heading>
      </div>
    )

  if (!journalEntry) return null

  return (
    <DetailsLayout subject={journalEntry.subject}>
      <WorklistJournalEntry journalEntry={journalEntry} />
    </DetailsLayout>
  )
}

export const NoWorklistItemSelected = () => {
  const { t } = useTranslation("features", {
    keyPrefix: "worklist",
  })

  return (
    <div className={styles.noItemWrapper}>
      <Heading secondary>{t("noItemSelected")}</Heading>
    </div>
  )
}
