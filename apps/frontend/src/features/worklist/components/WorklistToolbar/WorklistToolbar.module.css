.toolbar {
  padding: 16px 32px;
  border-bottom: 1px solid var(--color-neutral-200);
  margin-top: 8px;
}

.statusSelect {
  margin-left: -8px;
}

.selectItem {
  height: 44px;
}

.checkbox {
  margin-bottom: 0;
}

.selectItem:hover:not([aria-selected="true"]) .checkbox input ~ span {
  background-color: var(--color-background-subtle-hover);
  box-shadow: 0px 0px 20px 0px rgba(13, 16, 57, 0.2);
}

.counter {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 24px;
  height: 24px;
  margin: 0 4px;
  border-radius: 22px;
}
