import { Toolbar, ToolbarSeparator } from "components/Ariakit/Toolbar/Toolbar"

import { WorklistItemType } from "../WorklistItem/WorklistItem"
import {
  WorklistFilterStatus,
  WorklistStatusFilter,
} from "./WorklistStatusFilter"
import styles from "./WorklistToolbar.module.css"
import { WorklistTypeFilter } from "./WorklistTypeFilter"

type WorklistToolbarProps = {
  isOrg?: boolean
  status: WorklistFilterStatus
  types: WorklistItemType[]
  onStatusChange: (newStatus: WorklistFilterStatus) => void
  onTypeChange: (newType: WorklistItemType) => void
}
export const WorklistToolbar = ({
  isOrg = false,
  status,
  types,
  onStatusChange,
  onTypeChange,
}: WorklistToolbarProps) => {
  return (
    <Toolbar className={styles.toolbar}>
      <WorklistStatusFilter status={status} onStatusChange={onStatusChange} />

      <ToolbarSeparator />

      <WorklistTypeFilter
        isOrg={isOrg}
        status={status}
        types={types}
        onTypeChange={onTypeChange}
      />
    </Toolbar>
  )
}
