import c from "classnames"
import { Trans, useTranslation } from "react-i18next"

import { SelectItem } from "components/Ariakit/Select/SelectItem/SelectItem"
import { ToolbarSelect } from "components/Ariakit/Toolbar/Toolbar"
import usePermissions from "features/authentication/hooks/usePermissions"
import { color } from "styles/colors"
import { Checkbox } from "ui"

import { PermissionKey } from "generated/graphql"

import { getTypeLabel, getTypeOptions } from "../../utils/worklistTypeUtils"
import { WorklistItemType } from "../WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "./WorklistStatusFilter"
import styles from "./WorklistToolbar.module.css"

const renderTypeFilterLabel = (types: WorklistItemType[]) => {
  const { t: tWorklist } = useTranslation("features", { keyPrefix: "worklist" })

  if (types.length === 0) {
    return tWorklist("typeAll")
  } else if (types.length === 1) {
    return getTypeLabel(types[0])
  }

  return (
    <Trans
      i18nKey={tWorklist("typeMultipleSelected", { count: types.length })}
      components={{
        counter: <div className={c(styles.counter, color.light)} />,
      }}
    />
  )
}

type WorklistTypeFilterProps = {
  isOrg: boolean
  status: WorklistFilterStatus
  types: WorklistItemType[]
  onTypeChange: (newType: WorklistItemType) => void
}

export const WorklistTypeFilter = ({
  isOrg,
  status,
  types,
  onTypeChange,
}: WorklistTypeFilterProps) => {
  const { t: tWorklist } = useTranslation("features", { keyPrefix: "worklist" })
  const { hasPermission } = usePermissions()
  const canViewSubjectJournal = hasPermission(PermissionKey.SubjectJournalView)

  const availableOptions = getTypeOptions(status, canViewSubjectJournal, isOrg)

  return (
    <ToolbarSelect
      value={types}
      selectProps={{
        "aria-label": tWorklist("typeLabel"),
      }}
      label={renderTypeFilterLabel(types)}
    >
      {availableOptions.map(({ value, label }) => (
        <SelectItem
          key={value}
          className={styles.selectItem}
          value={value}
          // The onClick handler makes the entire row clickable. It only triggers when
          // clicking outside the Checkbox and label area, as the Checkbox stops event propagation.
          onClick={() => {
            onTypeChange(value)
          }}
        >
          <Checkbox
            label={label}
            checked={types.includes(value)}
            onChange={() => onTypeChange(value)}
            className={styles.checkbox}
          />
        </SelectItem>
      ))}
    </ToolbarSelect>
  )
}
