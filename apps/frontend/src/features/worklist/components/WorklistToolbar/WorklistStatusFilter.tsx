import { i18nInstance } from "i18n"
import { useTranslation } from "react-i18next"

import { SelectItem } from "components/Ariakit/Select/SelectItem/SelectItem"
import { ToolbarSelect } from "components/Ariakit/Toolbar/Toolbar"

import styles from "./WorklistToolbar.module.css"

export enum WorklistFilterStatus {
  TO_DO = "toDo", // unread, new, incoming
  IN_PROGRESS = "inProgress", // in review
  RESOLVED = "resolved", // done
}

const statusOptions = [
  {
    label: i18nInstance.t("features:worklist.statusToDo"),
    value: WorklistFilterStatus.TO_DO,
  },
  {
    label: i18nInstance.t("features:worklist.statusInProgress"),
    value: WorklistFilterStatus.IN_PROGRESS,
  },
  {
    label: i18nInstance.t("features:worklist.statusResolved"),
    value: WorklistFilterStatus.RESOLVED,
  },
]

export const getWorklistFilterStatus = (
  value: string | null
): WorklistFilterStatus => {
  if (
    value &&
    Object.values(WorklistFilterStatus).includes(value as WorklistFilterStatus)
  ) {
    return value as WorklistFilterStatus
  }
  return WorklistFilterStatus.TO_DO
}

type WorklistStatusFilterProps = {
  status: WorklistFilterStatus
  onStatusChange: (newStatus: WorklistFilterStatus) => void
}

export const WorklistStatusFilter = ({
  status,
  onStatusChange,
}: WorklistStatusFilterProps) => {
  const { t } = useTranslation("features", { keyPrefix: "worklist" })

  const currentOption =
    statusOptions.find((option) => option.value === status) ?? statusOptions[0]

  return (
    <ToolbarSelect
      label={currentOption.label}
      value={status}
      selectProps={{
        "aria-label": t("statusLabel"),
        className: styles.statusSelect,
      }}
    >
      {statusOptions.map(({ value, label }) => (
        <SelectItem
          key={value}
          value={value}
          onClick={() => onStatusChange(value)}
        >
          {label}
        </SelectItem>
      ))}
    </ToolbarSelect>
  )
}
