import { useMemo } from "react"

import {
  GetOrganisationWorklistQueryVariables,
  GetProviderWorklistQueryVariables,
  ListItemFilterInput,
  ListItemType,
} from "generated/graphql"

import {
  WorklistItemType,
  WL_JOURNAL_ENTRY,
} from "../components/WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "../components/WorklistToolbar/WorklistStatusFilter"

const PAGE_SIZE = 30

const statusToQueryConfigMap: Record<
  WorklistFilterStatus,
  {
    handled: boolean
    removed: boolean | null
  }
> = {
  [WorklistFilterStatus.TO_DO]: {
    handled: false,
    removed: null,
  },
  [WorklistFilterStatus.IN_PROGRESS]: {
    handled: true,
    removed: false,
  },
  [WorklistFilterStatus.RESOLVED]: {
    handled: true,
    removed: true,
  },
}

type UseWorklistQueryParametersArgs = {
  isOrg: boolean
  providerId?: string
  selectedStatus: WorklistFilterStatus
  selectedTypes: WorklistItemType[]
  offset: number
}

export function useWorklistQueryParameters({
  isOrg,
  providerId,
  selectedStatus,
  selectedTypes,
  offset,
}: UseWorklistQueryParametersArgs) {
  const onlyJournalEntryTypeSelected =
    selectedTypes.length === 1 && selectedTypes[0] === WL_JOURNAL_ENTRY
  const onlyListItemTypesSelected =
    selectedTypes.length > 0 &&
    selectedTypes.every((type) => type !== WL_JOURNAL_ENTRY)

  const { handled, removed } = statusToQueryConfigMap[selectedStatus]

  const listItemTypes = useMemo(() => {
    if (selectedTypes.length === 0) {
      return null
    }
    return selectedTypes.filter((type): type is ListItemType =>
      Object.values(ListItemType).includes(type as ListItemType)
    )
  }, [selectedTypes])

  const getQueryVariables = (
    targetOffset: number
  ):
    | GetProviderWorklistQueryVariables
    | GetOrganisationWorklistQueryVariables
    | null => {
    // not fetching list items if only journal entry type is selected
    if (onlyJournalEntryTypeSelected) {
      return null
    }
    const filter: Omit<ListItemFilterInput, "assigneeIds"> = {
      handled,
      listTypes: listItemTypes,
      removed,
    }
    const pagination = { offset: targetOffset, limit: PAGE_SIZE }

    if (isOrg) {
      return {
        filter,
        pagination,
      }
    }

    if (!isOrg && providerId) {
      return {
        filter: {
          ...filter,
          assigneeIds: { excludeNullValues: true, values: [providerId] },
        },
        pagination,
      }
    }

    return null
  }

  const listItemsQueryVariables = useMemo(() => {
    return getQueryVariables(offset)
  }, [offset, selectedStatus, selectedTypes, providerId, isOrg])

  const providerQueryVariables = useMemo(() => {
    if (!isOrg && providerId) {
      return listItemsQueryVariables as GetProviderWorklistQueryVariables | null
    }
    return null
  }, [isOrg, providerId, listItemsQueryVariables])

  const orgQueryVariables = useMemo(() => {
    if (isOrg) {
      return listItemsQueryVariables as GetOrganisationWorklistQueryVariables | null
    }
    return null
  }, [isOrg, listItemsQueryVariables])

  return {
    providerQueryVariables,
    orgQueryVariables,
    onlyListItemTypesSelected,
  }
}
