import { skipToken } from "@apollo/client"
import { renderHook } from "@testing-library/react"
import { vi } from "vitest"

import {
  useGetProviderWorklistSuspenseQuery,
  useGetOrganisationWorklistSuspenseQuery,
  useGetWorklistJournalEntriesSuspenseQuery,
  ListItemType,
} from "generated/graphql"

import { WL_JOURNAL_ENTRY } from "../components/WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "../components/WorklistToolbar/WorklistStatusFilter"
import { useWorklistData } from "./useWorklistData"

vi.mock("generated/graphql", async () => {
  const actual =
    await vi.importActual<typeof import("generated/graphql")>(
      "generated/graphql"
    )

  return {
    ...actual,
    useGetProviderWorklistSuspenseQuery: vi
      .fn()
      .mockReturnValue({ data: undefined } as ReturnType<
        typeof actual.useGetProviderWorklistSuspenseQuery
      >),
    useGetOrganisationWorklistSuspenseQuery: vi
      .fn()
      .mockReturnValue({ data: undefined } as ReturnType<
        typeof actual.useGetOrganisationWorklistSuspenseQuery
      >),
    useGetWorklistJournalEntriesSuspenseQuery: vi
      .fn()
      .mockReturnValue({ data: undefined } as ReturnType<
        typeof actual.useGetWorklistJournalEntriesSuspenseQuery
      >),
  }
})

describe("useWorklistData", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Provider Worklist", () => {
    it("should fetch provider worklist data when providerId is given and isOrg is false", () => {
      const defaultProviderProps = {
        isOrg: false,
        selectedStatus: WorklistFilterStatus.TO_DO,
        providerId: "provider123",
        selectedTypes: [],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            assigneeIds: {
              excludeNullValues: true,
              values: ["provider123"],
            },
            handled: false,
            listTypes: null,
            removed: null,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(defaultProviderProps))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith({
        variables: { providerId: "provider123" },
      })
    })

    it("should skip all queries if providerId is missing for provider worklist", () => {
      const props = {
        isOrg: false,
        providerId: undefined,
        selectedStatus: WorklistFilterStatus.TO_DO,
        selectedTypes: [],
        currentPage: 1,
      }
      renderHook(() => useWorklistData(props))

      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should fetch only journal entries when only journal entry type is selected and status is TO_DO", () => {
      const props = {
        isOrg: false,
        providerId: "provider123",
        selectedStatus: WorklistFilterStatus.TO_DO,
        selectedTypes: [WL_JOURNAL_ENTRY],
        currentPage: 1,
      }
      renderHook(() => useWorklistData(props))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith({
        variables: { providerId: "provider123" },
      })
    })

    it("should not fetch journal entries if only list item types are selected", () => {
      const props = {
        isOrg: false,
        providerId: "provider123",
        selectedStatus: WorklistFilterStatus.TO_DO,
        selectedTypes: [ListItemType.AppointmentRequest, ListItemType.Referral],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            assigneeIds: {
              excludeNullValues: true,
              values: ["provider123"],
            },
            handled: false,
            listTypes: [ListItemType.AppointmentRequest, ListItemType.Referral],
            removed: null,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should handle IN_PROGRESS status for provider worklist", () => {
      const props = {
        isOrg: false,
        providerId: "provider123",
        selectedStatus: WorklistFilterStatus.IN_PROGRESS,
        selectedTypes: [],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            assigneeIds: {
              excludeNullValues: true,
              values: ["provider123"],
            },
            handled: true,
            listTypes: null,
            removed: false,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should handle RESOLVED status for provider worklist", () => {
      const props = {
        isOrg: false,
        providerId: "provider123",
        selectedStatus: WorklistFilterStatus.RESOLVED,
        selectedTypes: [],
        currentPage: 2,
      }
      const expectedVariables = {
        variables: {
          filter: {
            assigneeIds: { excludeNullValues: true, values: ["provider123"] },
            handled: true,
            listTypes: null,
            removed: true,
          },
          pagination: { limit: 30, offset: 30 },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })
  })

  describe("Organisation Worklist", () => {
    it("should fetch only org worklist data when isOrg is true", () => {
      const defaultOrgProps = {
        isOrg: true,
        selectedStatus: WorklistFilterStatus.TO_DO,
        selectedTypes: [],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            handled: false,
            listTypes: null,
            removed: null,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(defaultOrgProps))

      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should handle IN_PROGRESS status for org worklist", () => {
      const props = {
        isOrg: true,
        selectedStatus: WorklistFilterStatus.IN_PROGRESS,
        selectedTypes: [],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            handled: true,
            listTypes: null,
            removed: false,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should handle RESOLVED status and pagination for org worklist", () => {
      const props = {
        isOrg: true,
        selectedStatus: WorklistFilterStatus.RESOLVED,
        selectedTypes: [],
        currentPage: 1,
      }
      const expectedVariables = {
        variables: {
          filter: {
            handled: true,
            listTypes: null,
            removed: true,
          },
          pagination: {
            limit: 30,
            offset: 0,
          },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
      expect(useGetProviderWorklistSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
      expect(useGetWorklistJournalEntriesSuspenseQuery).toHaveBeenCalledWith(
        skipToken
      )
    })

    it("should handle pagination for org worklist", () => {
      const props = {
        isOrg: true,
        selectedStatus: WorklistFilterStatus.RESOLVED,
        selectedTypes: [],
        currentPage: 3,
      }
      const expectedVariables = {
        variables: {
          filter: {
            handled: true,
            listTypes: null,
            removed: true,
          },
          pagination: { limit: 30, offset: 60 },
        },
      }
      renderHook(() => useWorklistData(props))

      expect(useGetOrganisationWorklistSuspenseQuery).toHaveBeenCalledWith(
        expectedVariables
      )
    })
  })
})
