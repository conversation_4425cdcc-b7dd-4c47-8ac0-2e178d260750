import { skipToken } from "@apollo/client"

import {
  useGetProviderWorklistSuspenseQuery,
  useGetOrganisationWorklistSuspenseQuery,
  GetProviderWorklistQuery,
  ListItemPriority,
  WorklistSubjectFragmentFragment,
  GetWorklistJournalEntriesQuery,
  useGetWorklistJournalEntriesSuspenseQuery,
  ProviderSpecialty,
} from "generated/graphql"

import {
  WL_JOURNAL_ENTRY,
  WorklistItemType,
} from "../components/WorklistItem/WorklistItem"
import { WorklistFilterStatus } from "../components/WorklistToolbar/WorklistStatusFilter"
import { useWorklistQueryParameters } from "./useWorklistQueryParameters"

const MEDIUM_HIGH_PRIORITY = "medium-high" as const
export const PAGE_SIZE = 30

const priorityOrder = [
  null,
  ListItemPriority.Critical,
  ListItemPriority.High,
  MEDIUM_HIGH_PRIORITY,
  ListItemPriority.Medium,
  ListItemPriority.Low,
  ListItemPriority.VeryLow,
] as const

export type MergedWorklistItem = {
  id: string
  subject: WorklistSubjectFragmentFragment
  content: string | null
  linkToSectionId: string | null
  linkToEncounterId: string | null
  createdAt: string
  priority: (typeof priorityOrder)[number]
  assignee: { name: string; id: string; specialty: ProviderSpecialty } | null
  type: WorklistItemType
}

function mergeListData({
  journalEntries = [],
  listItems = [],
}: {
  journalEntries?: GetWorklistJournalEntriesQuery["provider"]["journalEntries"]
  listItems?: GetProviderWorklistQuery["listItems"]["items"]
}): MergedWorklistItem[] {
  const listItemData: MergedWorklistItem[] =
    listItems?.map((item) => {
      const { id, comment, createdAt, subject, priority, itemType, assignee } =
        item
      let linkToSectionId = null
      if (
        item.__typename !== "AppointmentRequestItem" &&
        item.inboundData?.entries?.[0]?.sections?.[0]?.id
      ) {
        linkToSectionId = item.inboundData.entries[0].sections[0].id
      }

      return {
        id,
        subject,
        content: comment,
        linkToSectionId,
        linkToEncounterId:
          item.__typename === "AppointmentRequestItem"
            ? null
            : (item.inboundData?.id ?? null),
        createdAt,
        priority,
        type: itemType,
        assignee,
      }
    }) || []

  const journalEntryData: MergedWorklistItem[] =
    journalEntries?.map((journalEntry) => {
      const { id, title, sections, blocks, encounter, createdAt } = journalEntry
      const subject = encounter.subject
      const lastSectionId = sections?.[sections.length - 1]?.id
      const lastBlockId = blocks?.[blocks.length - 1]?.id
      return {
        id,
        subject,
        content: [encounter.reason, title]
          .filter((value): value is string => !!value)
          .join(" — "),
        linkToSectionId: lastSectionId ?? lastBlockId ?? null,
        linkToEncounterId: encounter.id,
        createdAt,
        priority: MEDIUM_HIGH_PRIORITY,
        type: WL_JOURNAL_ENTRY,
        assignee: null,
      }
    }) || []

  return [...journalEntryData, ...listItemData]
}

type UseWorklistDataArgs = {
  isOrg: boolean
  providerId?: string
  selectedStatus: WorklistFilterStatus
  selectedTypes: WorklistItemType[]
  currentPage: number
}

export function useWorklistData({
  isOrg,
  selectedStatus,
  providerId,
  selectedTypes,
  currentPage,
}: UseWorklistDataArgs) {
  const offset = (currentPage - 1) * PAGE_SIZE

  const {
    providerQueryVariables,
    orgQueryVariables,
    onlyListItemTypesSelected,
  } = useWorklistQueryParameters({
    isOrg,
    providerId,
    selectedStatus,
    selectedTypes,
    offset,
  })

  const shouldFetchListItems = !!providerQueryVariables || !!orgQueryVariables
  const { data: providerListItemsData } = useGetProviderWorklistSuspenseQuery(
    shouldFetchListItems && providerQueryVariables
      ? { variables: providerQueryVariables }
      : skipToken
  )
  const { data: orgListItemsData } = useGetOrganisationWorklistSuspenseQuery(
    shouldFetchListItems && orgQueryVariables
      ? { variables: orgQueryVariables }
      : skipToken
  )

  const fetchJournalEntries =
    !isOrg &&
    selectedStatus === WorklistFilterStatus.TO_DO &&
    !onlyListItemTypesSelected
  const { data: providerJournalEntriesData } =
    useGetWorklistJournalEntriesSuspenseQuery(
      providerId && fetchJournalEntries
        ? { variables: { providerId } }
        : skipToken
    )

  const listItems = shouldFetchListItems
    ? isOrg
      ? orgListItemsData?.listItems?.items || []
      : providerListItemsData?.listItems?.items || []
    : []
  const totalCount = shouldFetchListItems
    ? isOrg
      ? orgListItemsData?.listItems?.totalCount || 0
      : providerListItemsData?.listItems?.totalCount || 0
    : 0

  const journalEntries =
    fetchJournalEntries && providerJournalEntriesData?.provider?.journalEntries
      ? providerJournalEntriesData.provider.journalEntries
      : []

  const mergedItems = mergeListData({
    journalEntries,
    listItems,
  })

  return {
    items: mergedItems,
    totalCount,
    totalPages: Math.ceil(totalCount / PAGE_SIZE),
  }
}
