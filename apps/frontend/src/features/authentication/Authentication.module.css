.panel {
  margin-bottom: 8px;
}

.submitButton {
  justify-content: center;
  margin-bottom: 32px;
  margin-top: 8px;
}

.title {
  margin: 0 0 16px 0;
}

.wrap {
  align-content: flex-start;
}

.wrap > * {
  animation: fadeInUp 200ms;
  animation-iteration-count: 1;
  animation-fill-mode: both;
  animation-timing-function: cubic-bezier(0.165, 0.84, 0.44, 1);
}
.wrap > *:nth-child(2) {
  animation-delay: 100ms;
}

.wrap > *:nth-child(3) {
  animation-delay: 150ms;
}

.link {
  padding: 4px 12px;
  border-radius: 16px;
  margin: -4px -12px;
  justify-self: start;
}

/* Fade in-up animation keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translate3d(0, 20px, 0);
  }
  50% {
    opacity: 0.75;
  }
  to {
    opacity: 1;
    transform: translate3d(0, 0, 0);
  }
}
