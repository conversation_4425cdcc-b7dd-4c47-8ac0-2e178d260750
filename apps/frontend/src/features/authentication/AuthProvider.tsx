import base64url from "base64url"
import {
  useState,
  useContext,
  createContext,
  ReactNode,
  useEffect,
} from "react"
import {
  createSearchParams,
  useLocation,
  useNavigate,
  useSearchParams,
} from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"

import { apolloClient } from "../../lib/apollo/apolloClient"
import { broadcastLogout } from "./hooks/useDetectIdle"
import { getTokens, clearTokens, setTokens, Tokens } from "./utils/tokenStorage"

const placeholderFn = () => {
  throw new Error("No AuthContext provided")
}

const AuthContext = createContext({
  isAuthorized: false,
  authenticate: placeholderFn as (tokens: Tokens, path?: string) => void,
  deauthenticate: placeholderFn as (fromIdleTimeout?: boolean) => void,
})

type Props = { children?: ReactNode }

export const AuthProvider = ({ children }: Props) => {
  const navigate = useNavigate()
  const [searchParams] = useSearchParams()
  const encodedTokens = searchParams.get("authenticate")
  const { pathname, search } = useLocation()

  const [isAuthorized, setAuthorized] = useState(
    !!encodedTokens || getTokens() !== null
  )

  // Handle URL-based authentication (encodedTokens from URL)
  useEffect(() => {
    if (encodedTokens) {
      setTokens(JSON.parse(base64url.decode(encodedTokens)))
    }
  }, [encodedTokens])

  const authenticate = async (tokens: Tokens, path = "/") => {
    setTokens(tokens)
    setAuthorized(true)
    await apolloClient.resetStore()

    const queryParams = new URLSearchParams(search)
    const redirectTo = queryParams.get("redirectTo")
    navigate(redirectTo || path)
  }

  const deauthenticate = async (fromIdleTimeout?: boolean) => {
    clearTokens()
    setAuthorized(false)
    broadcastLogout()
    await apolloClient.resetStore()

    if (fromIdleTimeout) {
      // If we are logging the user out due to an idle timeout, we want to be able
      // to redirect back to the last visited page
      navigate({
        pathname: RouteStrings.login,
        search: createSearchParams({
          redirectTo: pathname + search,
        }).toString(),
      })
    }
  }

  return (
    <AuthContext.Provider
      value={{ isAuthorized, authenticate, deauthenticate }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export const useAuth = () => useContext(AuthContext)
