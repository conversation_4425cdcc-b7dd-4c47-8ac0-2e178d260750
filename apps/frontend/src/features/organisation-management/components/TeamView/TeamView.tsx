import { ReactNode } from "react"
import { useTranslation } from "react-i18next"
import { Link, useParams } from "react-router-dom"

import { TeamServiceType } from "components/TeamServiceType/TeamServiceType"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { Heading } from "ui"
import { Center } from "ui/components/Layout/Center"
import { renderDepartmentAndTeam } from "utils/renderDepartmentAndTeam"

import { useTeamQuery } from "generated/graphql"

import styles from "./TeamView.module.css"

export type TeamViewProps = {
  children?: ReactNode
}

/*
  This screen will be for Team members only to do their Team generic (non-servicetype bound) tasks e.g. manage TeamInbox.
  Users outside of Team should also be able to see Team members.
 */
export const TeamView = ({ children }: TeamViewProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "manageTeam" })
  const { teamId } = useParams<{ teamId: string }>()

  const { t: tEnum } = useTranslation("enums")

  const { data } = useTeamQuery({
    variables: {
      id: teamId || "",
    },
  })

  if (!data?.team) {
    return null
  }

  const { id, name, description, members, serviceType, department } = data.team

  const sortedMembers = [...members].sort((a, b) =>
    a.name.localeCompare(b.name)
  )

  return (
    <Center>
      <Heading size="display" as="h1">
        {renderDepartmentAndTeam(name, department?.name)}
      </Heading>
      <div>
        <TeamServiceType serviceType={serviceType} />
        <span>{description}</span>
      </div>

      <hr className={styles.separator} />

      <div className={styles.editLink}>
        <Link to={getRecordPagePath(RouteStrings.teamEdit, id)}>
          {t("editTeam")}
        </Link>
      </div>
      <ul className={styles.members}>
        {sortedMembers.map((member) => (
          <li key={member.id}>
            <Link to={getRecordPagePath(RouteStrings.providerView, member.id)}>
              {`${member.name} • ${tEnum(
                `ProviderSpecialty.${member.specialty}`
              )}`}
            </Link>
          </li>
        ))}
      </ul>
      {children}
    </Center>
  )
}

export default TeamView
