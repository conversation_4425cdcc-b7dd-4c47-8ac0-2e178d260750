import c from "classnames"
import { useTranslation } from "react-i18next"

import { calendarColorMap } from "styles/colors"

import { CalendarColor } from "generated/graphql"

import styles from "./SelectColor.module.css"

type SelectColorProps = {
  name?: string
  defaultValue?: CalendarColor
  disabled?: boolean
}

export const SelectColor = ({
  name,
  defaultValue,
  disabled = false,
}: SelectColorProps) => {
  const { t: tColor } = useTranslation("enums", { keyPrefix: "CalendarColor" })

  const colors = Object.values(CalendarColor)

  return (
    <div className={styles.wrap}>
      {colors.map((color) => (
        <input
          key={color}
          type="radio"
          id={`color-${color}`}
          name={name}
          defaultChecked={defaultValue === color}
          value={color}
          disabled={disabled}
          className={c(styles.radioInput, calendarColorMap[color].light, {
            [styles.radioInputDisabled]: disabled,
          })}
          title={tColor(color)}
        />
      ))}
    </div>
  )
}
