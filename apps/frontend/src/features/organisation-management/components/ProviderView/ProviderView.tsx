import dayjs from "dayjs"
import { useTranslation } from "react-i18next"
import { useParams } from "react-router-dom"

import { FormSection } from "components/FormSection/FormSection"
import { Heading } from "ui"
import { Center } from "ui/components/Layout/Center"
import { Tag } from "ui/components/Tag/Tag"

import {
  ActivationStatus,
  ProviderSpecialty,
  ProviderViewQuery,
  useProviderViewQuery,
} from "generated/graphql"

import { TeamsCard } from "../TeamsCard/TeamsCard"
import styles from "./ProviderView.module.css"

export type Props = {
  providerId: string
  data: ProviderViewQuery
}

const PV = ({ data: { provider } }: Props) => {
  const {
    name,
    email,
    phoneNumber,
    specialty,
    teams,
    createdAt,
    activationStatus,
    doctorNumber,
  } = provider

  const { t } = useTranslation("routes", { keyPrefix: "providerView" })
  const [tg] = useTranslation()

  return (
    <Center>
      <div className={styles.upper}>
        <Heading size="display" as="h1">
          {name}
        </Heading>

        <Tag>
          {tg(
            `enums:ProviderSpecialty.${Object.values(ProviderSpecialty).find(
              (id) => id === specialty
            )}`
          )}
        </Tag>
        {activationStatus === ActivationStatus.Deactivated && (
          <Tag className={styles.deactivatedTag}>
            {`User ${tg(`enums:ActivationStatus.${activationStatus}`)}`}
          </Tag>
        )}
      </div>

      <FormSection iconName={"information-line"}>
        <div>
          {t("userSince")}: {dayjs(createdAt).format("LL")}
        </div>
        <div>
          {tg("email")}: <a href={`mailto:${email}`}>{email}</a>
        </div>
        <div>
          {tg("telephone")}: {phoneNumber}
        </div>
        {doctorNumber && (
          <div>
            {tg("licenseNumber")}: {doctorNumber}
          </div>
        )}
      </FormSection>

      <FormSection iconName={"group-line"}>
        <Heading size="default" as="h2">
          {tg("teams")}
        </Heading>

        <TeamsCard teams={teams} linkTo="view" />
      </FormSection>
    </Center>
  )
}

export function ProviderView() {
  const { providerId } = useParams<{ providerId: string }>()

  const { data } = useProviderViewQuery({
    variables: {
      id: providerId || "",
    },
  })

  if (!providerId || !data) return null

  return <PV providerId={providerId} data={data} />
}
