// import { useSelectStore } from "components/Ariakit/hooks"
// import { InlineGuidePanel } from "components/InlineGuide/InlineGuidePanel"
// import Select from "components/Select/Select"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { useNavigate } from "react-router-dom"
import { z } from "zod"

import Panel from "components/Panel/Panel"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { Button, FormGrid, Input, Modal, Textarea } from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"

import {
  ServiceType,
  // TeamCreateInput,
  useCreateTeamMutation,
  namedOperations,
} from "generated/graphql"

import styles from "./TeamCreate.module.css"

// type FormState = Pick<TeamCreateInput, "departmentId"> & {
//   name: string
//   description: string
// }

type TeamCreateProps = {
  departmentId?: string
  onClose: () => void
}

// const TeamCreateSchema = z.object({
//   name: z.string().min(8),
//   description: z.string().min(1),
// })

const nameSchema = z.string().min(8)
const descriptionSchema = z.string().min(1)

export const TeamCreate = ({ departmentId, onClose }: TeamCreateProps) => {
  const { t } = useTranslation()

  const navigate = useNavigate()

  const [createTeam, { loading, error }] = useCreateTeamMutation({
    onCompleted: ({ createTeam }) =>
      navigate(getRecordPagePath(RouteStrings.team, createTeam.id)),
    // Performance is non-issue here, easier to refetch than to update cache
    refetchQueries: [namedOperations.Query.OrganisationManage],
  })

  const [validationError, setValidationError] = useState<{
    name: string
    description: string
  }>({
    name: "",
    description: "",
  })

  // const serviceTypeStore = useSelectStore<ServiceType>({})

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    // const serviceType = (serviceTypeStore.getState().value as ServiceType) || ""

    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())

    const nameField = nameSchema.safeParse(data.name)
    const descriptionField = descriptionSchema.safeParse(data.description)

    if (!nameField.success) {
      setValidationError((prev) => ({
        ...prev,
        name: t("Name must be at least 8 characters"),
      }))

      return
    }

    if (!descriptionField.success) {
      setValidationError((prev) => ({
        ...prev,
        description: t("validation:TeamCreate.description"),
      }))

      return
    }

    createTeam({
      variables: {
        input: {
          name: nameField.data,
          description: descriptionField.data,
          departmentId,
          // Temporary removed functionality to select serviceType in the form for now we are using only ClinicalAttendance
          serviceType: ServiceType.ClinicalAttendance,
        },
      },
    })
  }

  /* NOTE we are expecting this action to be done only by a trained moderator who should know the best way to name a team and select ServiceId. Therefor we don't allow deleting Team later, only updating. */
  return (
    <Modal
      isOpen
      onClose={onClose}
      title={t("routes:manageTeam.createTeam")}
      contentClassName={styles.modal}
      footer={
        <FormFooter>
          {error && <Panel status="error">{error?.message}</Panel>}

          <Button onClick={onClose}>{t("cancel")}</Button>
          <Button
            type="submit"
            disabled={loading}
            form="create-team-form"
            variant="filled"
          >
            {t("submit")}
          </Button>
        </FormFooter>
      }
    >
      <FormGrid
        onSubmit={handleSubmit}
        autoComplete="off"
        id="create-team-form"
        rowGap={2}
      >
        <Input
          label={t("Name")}
          name="name"
          required
          message={validationError.name}
          status={validationError.name ? "error" : "default"}
          onBlur={() => setValidationError((prev) => ({ ...prev, name: "" }))}
        />

        <Textarea
          label={t("description")}
          name="description"
          required
          message={validationError.description}
          status={validationError.description ? "error" : "default"}
          onBlur={() =>
            setValidationError((prev) => ({ ...prev, description: "" }))
          }
        />

        {/* <Select
          label={t("routes:manageTeam.serviceType")}
          selectStore={serviceTypeStore}
          options={Object.values(ServiceType).map((id) => ({
            value: id,
            label: t(`enums:Team_ServiceType.${id}`),
          }))}
          isClearable
        />
        <InlineGuidePanel id="CREATE_TEAM" /> */}
      </FormGrid>
    </Modal>
  )
}
