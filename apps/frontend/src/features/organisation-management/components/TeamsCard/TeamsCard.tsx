import c from "classnames"
import { Link } from "react-router-dom"

import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { getTeamPathFromServiceType } from "utils/getTeamPathFromServiceType"
import { renderDepartmentAndTeam } from "utils/renderDepartmentAndTeam"

import { TeamsCardFragmentFragment } from "generated/graphql"

import styles from "./TeamsCard.module.css"

type Props = {
  linkTo?: "edit" | "view"
  // spacy; title & description separate lines
  // compact; title & description in same line
  variant?: "spacy" | "compact"
  teams: ReadonlyArray<TeamsCardFragmentFragment>
}

/* Lists provided Teams */
export const TeamsCard = ({
  teams,
  linkTo = "edit",
  variant = "spacy",
}: Props) => (
  <ul className={c(styles.teams, styles[variant])}>
    {teams.map((team) => {
      const { id, name, description, department, serviceType } = team

      return (
        <li key={id}>
          <Link
            to={
              linkTo === "edit"
                ? getRecordPagePath(RouteStrings.teamEdit, id)
                : getTeamPathFromServiceType(serviceType, id)
            }
          >
            {renderDepartmentAndTeam(name, department?.name)}
          </Link>
          <span>{description}</span>
        </li>
      )
    })}
  </ul>
)
