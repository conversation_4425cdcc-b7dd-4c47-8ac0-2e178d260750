.wrap {
  padding-bottom: 120px;
}
.formHeading {
  grid-column: 1 / center-start;
  justify-self: self-end;
  padding-right: 16px;
}
.form {
  position: relative;
}
.form::after {
  content: "";
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: calc(-1 * var(--grid-gap) - 2px);
  width: 2px;
  background-color: var(--color-lev-blue);
  transition:
    width 0.2s ease-in-out,
    left 0.2s ease-in-out;
}
.form:focus-within::after {
  width: 4px;
  left: calc(-1 * var(--grid-gap) - 4px);
}
