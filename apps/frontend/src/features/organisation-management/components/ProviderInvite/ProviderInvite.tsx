import { useState } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, useNavigate } from "react-router-dom"
import { useImmer } from "use-immer"
import { z } from "zod"

import { useSelectStore } from "components/Ariakit/hooks"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { GlobalDataWithNonNullableActor } from "features/mainLayout/GlobalDataWithNonNullableActor"
import UnauthorizedPage from "features/mainLayout/components/ErrorPages/UnauthorizedPage"
import { RouteStrings } from "routes/RouteStrings"
import {
  Button,
  FormGrid,
  Heading,
  Input,
  JournalGrid,
  Label,
  notification,
} from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"
import { Center } from "ui/components/Layout/Center"
import { personaIdSchema } from "utils/personaIdSchema"

import {
  PermissionKey,
  ProviderSpecialty,
  useGetProviderInviteDataQuery,
  useGetProvidersAndInvitesQuery,
  useInviteProviderMutation,
} from "generated/graphql"

import styles from "./ProviderInvite.module.css"

type ProviderInviteProps = {
  leviosaKindId: GlobalDataWithNonNullableActor["config"]["leviosaKindId"]
}

type Status = {
  valid: boolean | "indeterminate"
  message: string
}

type FieldError = {
  email: Status
  personaId: Status
  phoneNumber: Status
  externalEhrId: Status
}

const emailSchema = z.string().email()
const phoneNumberSchema = z
  .string()
  .length(7)
  .regex(/^\d{7}$/)
// COMEBACK should be number since Saga only accepts INT
const externalEhrIdSchema = z.string().min(1)

const ProviderInviteWithoutPermissionCheck = ({
  leviosaKindId,
}: ProviderInviteProps) => {
  const isLite = leviosaKindId === "LITE"
  const [validationError, setValidationError] = useState<z.ZodError | null>(
    null
  )
  const { data: providersData } = useGetProvidersAndInvitesQuery()
  const providersAndInvites = providersData
    ? [...providersData.providers, ...providersData.invitedProviders]
    : []

  const [fieldErrors, setFieldErrors] = useImmer<FieldError>({
    email: { valid: "indeterminate", message: "" },
    personaId: { valid: "indeterminate", message: "" },
    phoneNumber: { valid: "indeterminate", message: "" },
    externalEhrId: { valid: "indeterminate", message: "" },
  })

  const { t } = useTranslation()
  const { t: tInvite } = useTranslation("routes", {
    keyPrefix: "inviteProvider",
  })

  const ProviderInviteSchema = z.object({
    name: z.string().min(3).max(255),
    email: emailSchema,
    personaId: personaIdSchema,
    teamId: z.string().uuid(),
    phoneNumber: phoneNumberSchema,
    specialty: z.nativeEnum(ProviderSpecialty),
    roles: z.array(z.string()).nonempty(),
    externalEhrId: isLite ? externalEhrIdSchema : z.undefined(),
    doctorNumber: z.string().optional(),
  })

  const navigate = useNavigate()

  const selectSpecialtyStore = useSelectStore({})
  const selectTeamStore = useSelectStore({})
  const { data } = useGetProviderInviteDataQuery()

  const teams = (
    data?.teams?.map(({ id, name, department }) => ({
      value: id,
      label: department ? `${department.name}: ${name}` : name,
    })) || []
  ).sort((a, b) => a.label.localeCompare(b.label))

  const roles =
    data?.roles?.map(({ id }) => ({
      value: id,
      label: id,
    })) || []

  const selectRolesStore = useSelectStore({ defaultValue: [] })

  const [inviteProvider, { error }] = useInviteProviderMutation({
    onCompleted: (data) => {
      if (!data.inviteProvider) return
    },
  })

  const onSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)
    const data = Object.fromEntries(formData.entries())

    const roles = formData.getAll("roles")

    const validatedInput = ProviderInviteSchema.safeParse({ ...data, roles })

    if (!validatedInput.success) {
      setValidationError(validatedInput.error)

      return
    }

    const res = await inviteProvider({
      variables: {
        input: {
          ...validatedInput.data,
          doctorNumber: validatedInput.data.doctorNumber || null,
          externalEhrId: isLite
            ? validatedInput.data.externalEhrId || ""
            : undefined,
        },
      },
    })

    if (res.data?.inviteProvider) {
      notification.create({
        status: "success",
        message: t("invitationSubmitted"),
      })

      setValidationError(null)

      navigate(
        generatePath(RouteStrings.userManagement, {
          view: "invited",
        })
      )
    }
  }

  const handleBlur = (
    field: keyof FieldError,
    value: string,
    alreadyExistsMessage: string
  ) => {
    if (value === "") {
      setFieldErrors((draft) => {
        draft[field].valid = false
        draft[field].message = "Please enter a value"
      })

      return
    }

    if (providersAndInvites?.some((p) => p[field] === value)) {
      setFieldErrors((draft) => {
        draft[field].valid = false
        draft[field].message = alreadyExistsMessage
      })

      return
    }

    setFieldErrors((draft) => {
      draft[field].valid = true
      draft[field].message = ""
    })
  }

  const checkStatus = (field: keyof FieldError) => {
    if (fieldErrors[field].valid === "indeterminate") return undefined

    return fieldErrors[field].valid ? "success" : "error"
  }

  const clearError = (field: keyof FieldError) => () => {
    setFieldErrors((draft) => {
      draft[field].valid = "indeterminate"
      draft[field].message = ""
    })
  }

  const providerSpecialtyValues = Object.values(ProviderSpecialty)

  return (
    <JournalGrid as="form" onSubmit={onSubmit} className={styles.wrap}>
      <Center>
        <Heading as="h1" size="display">
          {t("inviteProvider")}
        </Heading>
      </Center>
      <Label as="h3" className={styles.formHeading}>
        Personal Information
      </Label>
      <FormGrid className={styles.form} as="div" colSpan={4}>
        <Input
          onBlur={() => {
            setValidationError(null)
          }}
          label={t("Name")}
          name="name"
          autoComplete="off"
        />

        <Input
          label={t("Email")}
          name="email"
          type="email"
          message={fieldErrors.email.message}
          status={checkStatus("email")}
          onBlur={({ target: { value } }) => {
            setValidationError(null)

            const result = emailSchema.safeParse(value)

            if (result.success) {
              handleBlur("email", value, tInvite("emailAlreadyExists"))

              return
            }

            setFieldErrors((draft) => {
              draft.email.valid = false
              draft.email.message = t("Please enter a valid email address")
            })
          }}
          onFocus={clearError("email")}
          autoComplete="off"
        />

        <Input
          message={fieldErrors.personaId.message}
          label={t("personaId")}
          name="personaId"
          status={checkStatus("personaId")}
          onBlur={({ target: { value } }) => {
            setValidationError(null)

            const result = personaIdSchema.safeParse(value)

            if (result.success) {
              handleBlur("personaId", value, tInvite("personaIdAlreadyExists"))

              return
            }

            setFieldErrors((draft) => {
              draft.personaId.valid = false
              draft.personaId.message = t("invalidPersonaId")
            })
          }}
          onFocus={clearError("personaId")}
          autoComplete="off"
        />
      </FormGrid>

      <Label as="h3" className={styles.formHeading}>
        License Information
      </Label>
      <FormGrid className={styles.form} as="div" colSpan={4}>
        <Input
          onBlur={() => {
            setValidationError(null)
          }}
          label={t("License Number")}
          name="doctorNumber"
          autoComplete="off"
        />
      </FormGrid>
      <Label as="h3" className={styles.formHeading}>
        {t("Roles & Access")}
      </Label>
      <FormGrid className={styles.form} as="div" colSpan={4}>
        <Select
          label={t("Team")}
          name="teamId"
          onBlur={() => setValidationError(null)}
          options={teams}
          required
          selectStore={selectTeamStore}
        />
        <Select
          label={t("Specialty")}
          isClearable
          name="specialty"
          onBlur={() => setValidationError(null)}
          options={providerSpecialtyValues.map((specialty) => ({
            label: t(`enums:ProviderSpecialty.${specialty}`),
            value: specialty,
          }))}
          selectStore={selectSpecialtyStore}
        />

        <Select
          name="roles"
          options={roles}
          label="Access Roles"
          selectStore={selectRolesStore}
          onBlur={() => setValidationError(null)}
        />

        {(validationError || error) && (
          <Panel status="error">
            {validationError?.message || error?.message}
          </Panel>
        )}

        <Input
          label={t("phoneNumber")}
          name="phoneNumber"
          status={checkStatus("phoneNumber")}
          message={fieldErrors.phoneNumber.message}
          onBlur={({ target: { value } }) => {
            setValidationError(null)

            const result = phoneNumberSchema.safeParse(value)

            if (result.success) {
              handleBlur(
                "phoneNumber",
                value,
                tInvite("phoneNumberAlreadyExists")
              )
              return
            }

            setFieldErrors((draft) => {
              draft.phoneNumber.valid = false
              draft.phoneNumber.message = t(
                "Please enter a valid 7 digit phone number"
              )
            })
          }}
          onFocus={clearError("phoneNumber")}
          autoComplete="off"
        />

        {isLite && (
          <Input
            type="number"
            min={1}
            label={t("externalEhrId")}
            name="externalEhrId"
            placeholder={tInvite("externalEhrIdPlaceholder")}
            status={checkStatus("externalEhrId")}
            message={fieldErrors.externalEhrId.message}
            onFocus={clearError("externalEhrId")}
            autoComplete="off"
            onBlur={({ target: { value } }) => {
              setValidationError(null)

              const result = externalEhrIdSchema.safeParse(value)

              if (result.success) {
                handleBlur(
                  "externalEhrId",
                  value,
                  tInvite("ehrUserIdAlreadyExists")
                )
                return
              }

              setFieldErrors((draft) => {
                draft.externalEhrId.valid = false
                draft.externalEhrId.message = tInvite("ehrInvalidFormat")
              })
            }}
          />
        )}

        <FormFooter>
          <Button variant="clear" size="large" onClick={() => navigate(-1)}>
            {t("doCancel")}
          </Button>
          <Button variant="filled" size="large" type="submit">
            {t("invite")}
          </Button>
        </FormFooter>
      </FormGrid>
    </JournalGrid>
  )
}

export default function ProviderInvite(props: ProviderInviteProps) {
  return (
    <Restricted
      to={PermissionKey.AccountsProviderInvite}
      fallback={<UnauthorizedPage />}
    >
      <ProviderInviteWithoutPermissionCheck {...props} />
    </Restricted>
  )
}
