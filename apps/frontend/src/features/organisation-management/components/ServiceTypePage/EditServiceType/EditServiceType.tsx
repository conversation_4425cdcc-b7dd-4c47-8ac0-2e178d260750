import { useNavigate, useParams } from "react-router-dom"

import ServiceTypeModal from "features/organisation-management/components/ServiceTypeModal/ServiceTypeModal"
import { PrivateRoutes } from "routes/RouteStrings"

import { useGetServiceTypeSuspenseQuery } from "generated/graphql"

const EditServiceType = () => {
  const { serviceTypeId } = useParams<{ serviceTypeId: string }>()

  const navigate = useNavigate()

  if (!serviceTypeId) return null

  const { data } = useGetServiceTypeSuspenseQuery({
    variables: {
      id: serviceTypeId,
    },
  })

  if (!serviceTypeId || !data) return null

  return (
    <ServiceTypeModal
      serviceType={data.externalServiceType}
      showModal={true}
      closeModal={() => {
        navigate(PrivateRoutes.serviceTypes)
      }}
    />
  )
}

export default EditServiceType
