import { ReactNode } from "react"
import { useTranslation } from "react-i18next"
import { generatePath, Link, useNavigate } from "react-router-dom"

import Icon from "components/Icon/Icon"
import { CenteredLayout } from "features/billing/components/CenteredLayout/CenteredLayout"
import { RouteStrings } from "routes/RouteStrings"
import { Button, Heading, Table, TextWithIcon } from "ui"

import { useGetServiceTypesQuery } from "generated/graphql"

import { ServiceTypeColor } from "./ServiceTypeColor/ServiceTypeColor"
import styles from "./ServiceTypePage.module.css"

type ServiceTypePageProps = {
  children: ReactNode
}

const ServiceTypePage = ({ children }: ServiceTypePageProps) => {
  const { t } = useTranslation()

  const { data } = useGetServiceTypesQuery()

  const navigate = useNavigate()

  const { t: tEnum } = useTranslation("enums")

  return (
    <CenteredLayout>
      <header className={styles.header}>
        <Heading size="large">{t("Services")}</Heading>
        <Button
          as={Link}
          icon={<Icon name={"pencil-line"} />}
          to={generatePath(RouteStrings.createServiceType)}
        >
          {t("Add Service")}
        </Button>
      </header>

      <Table>
        <thead>
          <tr>
            <th>{t("Color")}</th>
            <th>{t("Name")}</th>
            <th>{t("Description")}</th>
            <th>{t("Modality")}</th>
          </tr>
        </thead>
        <tbody>
          {data?.externalServiceTypes &&
            data.externalServiceTypes.length === 0 && (
              <tr>
                <td colSpan={4}>
                  <TextWithIcon iconName="information-line">
                    {t("No services found.")}
                  </TextWithIcon>
                </td>
              </tr>
            )}

          {data?.externalServiceTypes.map((serviceType) => (
            <tr
              key={serviceType.id}
              onClick={() => {
                navigate(
                  generatePath(RouteStrings.editServiceType, {
                    serviceTypeId: serviceType.id,
                  })
                )
              }}
              className={styles.tableRow}
            >
              <td>
                <ServiceTypeColor color={serviceType.color} />
              </td>
              <td>{serviceType.name}</td>
              <td>{serviceType.description}</td>
              <td>{tEnum(`ServiceTypeModality.${serviceType.modality}`)}</td>
            </tr>
          ))}
        </tbody>
      </Table>

      {children}
    </CenteredLayout>
  )
}

export default ServiceTypePage
