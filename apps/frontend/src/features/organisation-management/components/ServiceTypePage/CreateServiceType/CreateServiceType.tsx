import { useNavigate } from "react-router-dom"

import ServiceTypeModal from "features/organisation-management/components/ServiceTypeModal/ServiceTypeModal"
import { PrivateRoutes } from "routes/RouteStrings"

const CreateServiceType = () => {
  const navigate = useNavigate()

  return (
    <ServiceTypeModal
      serviceType={null}
      showModal={true}
      closeModal={() => {
        navigate(PrivateRoutes.serviceTypes)
      }}
    />
  )
}

export default CreateServiceType
