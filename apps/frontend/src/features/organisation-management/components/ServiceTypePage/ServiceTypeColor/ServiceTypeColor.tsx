import c from "classnames"

import { calendarColorMap } from "styles/colors"

import { CalendarColor } from "generated/graphql"

import styles from "./ServiceTypeColor.module.css"

type ServiceTypeColorCalendar =
  | CalendarColor.Blue
  | CalendarColor.LevBlue
  | CalendarColor.LevGreen
  | CalendarColor.Orange
  | CalendarColor.Pink
  | CalendarColor.HotOrange
  | CalendarColor.Lavender
  | CalendarColor.LightBlue
  | CalendarColor.Olive
  | CalendarColor.Gray

type ServiceTypeColorProps = {
  color: ServiceTypeColorCalendar
}

export const ServiceTypeColor = ({ color }: ServiceTypeColorProps) => {
  return <div className={c(styles.wrap, calendarColorMap[color])} />
}
