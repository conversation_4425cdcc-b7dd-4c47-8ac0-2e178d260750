import { FormEvent, useState } from "react"
import { useTranslation } from "react-i18next"

import { FormSection } from "components/FormSection/FormSection"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import usePasswordValidation from "hooks/usePasswordValidation"
import { Button, FormGrid, Input, notification } from "ui"
import FormFooter from "ui/components/FormFooter/FormFooter"
import NewPasswordInput from "ui/components/Input/NewPasswordInput"
import { Center } from "ui/components/Layout/Center"

import {
  useActorChangePasswordMutation,
  useDefaultIssuerForProviderQuery,
} from "generated/graphql"

import styles from "./ActorSettings.module.css"
import { SelectIssuer } from "./SelectIssuer"

/* This route will later allow Actor to edit own profile parameters & config. Currently there is nothing to edit so has only password change. */
export const ActorSettings = () => {
  const { t: tProvider } = useTranslation("routes", {
    keyPrefix: "manageProvider",
  })

  const [currentPassword, setCurrentPassword] = useState("")

  const {
    passwordRef,
    repeatedPasswordRef,
    passwordStatus,
    setPasswordStatus,
    repeatedPasswordStatus,
    setRepeatedPasswordStatus,
    validatePassword,
    validateRepeatedPassword,
  } = usePasswordValidation()

  const { globalData } = useGlobalState()

  const actor = globalData.actor

  const { data } = useDefaultIssuerForProviderQuery({
    variables: {
      providerId: actor.id,
    },
  })

  const [updatePassword, { loading: loadingPassword }] =
    useActorChangePasswordMutation()

  const handleSubmitPassword = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!validatePassword() || !validateRepeatedPassword()) return

    if (!passwordRef.current) return

    updatePassword({
      variables: { newPassword: passwordRef.current.value, currentPassword },
      onCompleted: (data) => {
        if (data?.changePassword)
          notification.create({
            status: "success",
            message: tProvider("passwordChangeSuccess"),
          })
      },
    })
  }

  return (
    <Center>
      <FormGrid
        colSpan={4}
        as="form"
        onSubmit={handleSubmitPassword}
        className={styles.changePassword}
      >
        <FormSection showLine={true} iconName={"key-line"}>
          <Input
            defaultValue={currentPassword}
            type="password"
            label={tProvider("currentPassword")}
            onChange={({ currentTarget: { value } }) =>
              setCurrentPassword(value)
            }
          />
          <NewPasswordInput
            label={tProvider("newPassword")}
            ref={passwordRef}
            status={passwordStatus.status}
            message={passwordStatus.message}
            onFocus={() =>
              setPasswordStatus({ status: "default", message: "" })
            }
            onBlur={validatePassword}
          />
          <Input
            type="password"
            label={tProvider("repeatPassword")}
            ref={repeatedPasswordRef}
            status={repeatedPasswordStatus.status}
            message={repeatedPasswordStatus.message}
            onBlur={validateRepeatedPassword}
            onFocus={() =>
              setRepeatedPasswordStatus({ status: "default", message: "" })
            }
          />

          <FormFooter>
            <Button
              variant="filled"
              disabled={loadingPassword}
              icon={loadingPassword ? <Icon name="loader-4-line" spin /> : null}
              type="submit"
            >
              {tProvider("changePassword")}
            </Button>
          </FormFooter>
        </FormSection>

        <FormSection showLine={false} iconName={"user-line"}>
          <SelectIssuer defaultValue={data?.defaultIssuerForProvider.id} />
        </FormSection>
      </FormGrid>
    </Center>
  )
}
