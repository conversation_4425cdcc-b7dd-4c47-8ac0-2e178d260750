import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"
import { Link, useNavigate, useParams } from "react-router-dom"

import { useSelectStore } from "components/Ariakit/hooks"
import { FormSection } from "components/FormSection/FormSection"
import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import Panel from "components/Panel/Panel"
import Select from "components/Select/Select"
import { RouteStrings } from "routes/RouteStrings"
import { getRecordPagePath } from "routes/recordPage"
import { FormGrid, Grid, Heading, Input, Table, Textarea } from "ui"
import Button from "ui/components/Button/Button"
import FormFooter from "ui/components/FormFooter/FormFooter"
import { renderDepartmentAndTeam } from "utils/renderDepartmentAndTeam"

import {
  ActivationStatus,
  ProviderSpecialty,
  Team,
  TeamQuery,
  useAddProvidersToTeamMutation,
  useRemoveProvidersFromTeamMutation,
  useTeamQuery,
  useTeamUpdateMutation,
} from "generated/graphql"

import styles from "./TeamEdit.module.css"

export type TEProps = {
  id: string
  data: TeamQuery
}

type FormState = Pick<Team, "name" | "description">

const TE = ({ data }: TEProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "manageTeam" })
  const [tg] = useTranslation()
  const { globalData } = useGlobalState()

  // COMEBACK ideally organisation is root query
  const { organisation } = globalData.actor
  const {
    team: { id: teamId, name, description, department, members },
    providers,
  } = data

  const navigate = useNavigate()

  const [formState, setFormState] = useState<FormState>({
    name,
    description,
  })

  const updateState = (
    keyValuePair: Partial<Pick<FormState, keyof FormState>>
  ) => setFormState((prev) => ({ ...prev, ...keyValuePair }))

  const [updateTeam, { loading, error }] = useTeamUpdateMutation({
    onCompleted: () => navigate(getRecordPagePath(RouteStrings.team, teamId)),
  })

  const [addProvidersToTeamMember] = useAddProvidersToTeamMutation({
    onCompleted: () => selectProvidersStore.setValue(""),
  })
  const [removeProvidersFromTeamMember] = useRemoveProvidersFromTeamMutation({
    onCompleted: () => selectProvidersStore.setValue(""),
  })

  const selectProvidersStore = useSelectStore({})

  const submitOne = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    updateTeam({ variables: { id: teamId, input: formState } })
  }

  const sortedProviderOptions = providers
    .filter(
      (provider) =>
        provider.activationStatus === ActivationStatus.Active &&
        !members.find((member) => member.id === provider.id)
    )
    .map(({ id, name }) => ({
      value: id,
      label: name,
    }))
    .sort((a, b) => a.label.localeCompare(b.label))

  return (
    <Grid rowGap={4}>
      <FormGrid onSubmit={submitOne}>
        <Heading size="display" as="h1">
          {renderDepartmentAndTeam(name, department?.name)}
        </Heading>

        <FormSection showLine={true} iconName="settings-line">
          <Input
            label={tg("Name")}
            value={formState.name}
            onChange={({ target }) => updateState({ name: target.value })}
            /* message={<TeamServiceType serviceType={serviceType} />} */
          />

          <div className={styles.belongsToBlock}>
            <span className={styles.belongsTo}>{tg("belongsTo")}</span>
            {department ? (
              <Link
                to={getRecordPagePath(
                  RouteStrings.departmentEdit,
                  department.id
                )}
              >
                {department.name}
              </Link>
            ) : (
              <Link
                to={getRecordPagePath(
                  RouteStrings.organisationManage,
                  organisation.id
                )}
              >
                {organisation.name}
              </Link>
            )}
          </div>

          <Textarea
            value={formState.description || ""}
            label={tg("description")}
            onChange={({ target }) =>
              updateState({ description: target.value })
            }
          />

          <FormFooter>
            {error && <Panel status="error">{error.message}</Panel>}
            <Button onClick={() => navigate(-1)}>{tg("cancel")}</Button>
            <Button type="submit" disabled={loading} variant="filled">
              {tg("submit")}
            </Button>
          </FormFooter>
        </FormSection>

        <FormSection
          className={styles.formSection}
          showLine={false}
          iconName={"group-line"}
        >
          <div className={styles.editMembersWrap}>
            <Heading size="default" as="h2">
              {t("editMembers")}
            </Heading>
            <Table>
              <tbody>
                {members.map((member) => (
                  <tr
                    key={member.id}
                    className={c({
                      [styles.deactivated]:
                        member.activationStatus ===
                        ActivationStatus.Deactivated,
                    })}
                  >
                    <td>
                      <Link
                        to={getRecordPagePath(
                          RouteStrings.providerView,
                          member.id
                        )}
                      >
                        {member.name}
                      </Link>
                    </td>
                    <td>
                      {tg(
                        `enums:ProviderSpecialty.${Object.values(
                          ProviderSpecialty
                        ).find((id) => id === member.specialty)}`
                      )}
                    </td>
                    <td className={styles.deleteColumn}>
                      <Button
                        aria-label="delete"
                        variant="clear"
                        size="default"
                        icon={<Icon name="close-line" />}
                        onClick={() =>
                          removeProvidersFromTeamMember({
                            variables: {
                              teamId,
                              providerIds: [member.id],
                            },
                          })
                        }
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </Table>
            {/* Design decision: Select is used only to add Providers. List below is to remove. */}
            <Select
              label=""
              sameWidth
              placeholder={t("addMember")}
              selectStore={selectProvidersStore}
              options={sortedProviderOptions}
              onSelectChange={(value) => {
                if (Array.isArray(value) || value === null) return

                addProvidersToTeamMember({
                  variables: { teamId, providerIds: [value] },
                })
              }}
              status={
                members.length === providers.length ? "warning" : undefined
              }
            />
          </div>
        </FormSection>
      </FormGrid>
    </Grid>
  )
}
type TeamEditProps = Omit<TEProps, "id" | "data">

export default function TeamEdit(props: TeamEditProps) {
  const { teamId } = useParams<{ teamId: string }>()

  const { data } = useTeamQuery({
    variables: { id: teamId || "" },
  })

  if (!teamId || !data) return null

  return <TE {...props} data={data} id={teamId} />
}
