.belongsToBlock {
  margin-bottom: var(--grid-gap);
}

.belongsTo {
  margin-right: 4px;
}

.belongsTo::after {
  content: ":";
}

.deactivated {
  font-style: italic;
  position: relative;
}

.deactivated::after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(211, 211, 211, 0.5);
  pointer-events: none;
}

.editMembersWrap {
  display: grid;
  gap: var(--grid-gap);
}

.formSection > div:first-child {
  margin-top: 4px;
}

.deleteColumn {
  text-align: right;
}
