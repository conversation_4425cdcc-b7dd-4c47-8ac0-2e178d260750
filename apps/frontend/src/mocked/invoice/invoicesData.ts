import { faker } from "@faker-js/faker"

export interface InvoiceMockData {
  id: string
  date: string
  invoiceNumber: string
  subject: string
  provider: string
  total: number
  isDraft: boolean
}

const generateMockInvoices = (count: number): InvoiceMockData[] => {
  const invoices: InvoiceMockData[] = []

  for (let i = 0; i < count; i++) {
    const invoice: InvoiceMockData = {
      id: faker.datatype.uuid(),
      date: faker.date.recent().toISOString(),
      invoiceNumber: `${faker.datatype.number({
        min: 100,
        max: 999,
      })}-${faker.datatype.number({ min: 1000000, max: 9999999 })}`,
      subject: `${faker.name.firstName()} ${faker.name.lastName()}`,
      provider: `${faker.name.firstName()} ${faker.name.lastName()}`,
      total: faker.datatype.number({ min: 1000, max: 9000 }),
      isDraft: faker.datatype.boolean(),
    }

    invoices.push(invoice)
  }

  return invoices
}

export const mockedInvoiceData = generateMockInvoices(100)
