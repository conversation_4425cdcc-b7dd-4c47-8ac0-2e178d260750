type InvoiceOverview {
  id: UUID!
  date: String!
  invoiceNumber: String!
  subject: String!
  provider: String!
  total: Float!
  isDraft: Boolean!
}

type InvoiceOverviewPage {
  invoices: [InvoiceOverview!]!
  totalItems: Int!
  totalPages: Int!
  currentPage: Int!
}

input InvoiceOverviewInput {
  page: Int!
  pageSize: Int!
  search: String!
  fromDate: String
  toDate: String
  isDraft: Boolean
}

type Query {
  invoiceMock(input: InvoiceOverviewInput!): InvoiceOverviewPage!
}

schema {
  query: Query
  mutation: Mutation
}
