import { faker } from "@faker-js/faker"

export interface PriceListData {
  id: string
  itemId: string
  itemName: string
  units: number
  unitsPrice: number
  currency: string
  vat: number
  isLiked: boolean
}

const generateMockPriceList = (count: number): PriceListData[] => {
  const priceLists: PriceListData[] = []

  for (let i = 0; i < count; i++) {
    const priceList: PriceListData = {
      id: faker.datatype.uuid(),
      itemId:
        faker.random.alpha({ count: 1 }).toUpperCase() +
        faker.random.numeric(1),
      itemName: faker.commerce.productName(),
      units: faker.datatype.number({ min: 1, max: 100 }),
      unitsPrice: parseFloat(faker.commerce.price(1000)),
      currency: "ISK",
      vat: parseFloat(faker.finance.amount(0, 20, 2)),
      isLiked: faker.datatype.boolean(),
    }

    priceLists.push(priceList)
  }

  return priceLists
}

export const mockedPriceListData = generateMockPriceList(100).sort((a) =>
  a.isLiked ? -1 : 1
)
