{"admin": {"editUser": "Edit user", "inviteUser": "Invite user", "omnipresentModels": "Models omnipresent"}, "auth": {"acceptInvitation": "Accept invitation", "backToLogin": "Back to login", "doLogin": "Log In", "doLoginWithElectronicId": "Log in with Electronic ID", "doLogout": "Log Out", "phone": "Phone Number", "email": "Email", "forgotPassword": "Forgot password", "invitationTitle": "Create password", "inviteSuccess": "Invitation link has been sent to email", "loginAsSomeoneElse": "Log in as someone else", "loginGotInvite": "Got an invite?", "newPassword": "New password", "password": "Password", "passwordChangeSuccess": "Password successfully changed", "repeatPassword": "Repeat password", "resetPassword": "Reset", "successPasswordChange": "Password has been successfully changed. You can now log in with your new password", "resetPasswordSuccess": "Reset link has been sent to your email.", "passwordMisMatch": "Entered passwords do not match. Please make sure they are correctly entered.", "passwordFoundInBreaches": "Please choose a different password. This password has been found in next number of breaches:", "sessionCookieRemovalError": "Something went wrong. Please try again.", "unableToCheckPassword": "Unable to check password. Please try again later.", "welcomeBack": "Welcome back"}, "billing": {"export": {"downloadCSVFile": "Download a csv file with the data", "exportButton": "Export Invoices", "exportInvoiceData": "Export invoice data", "header": "Export invoice data"}, "filter": {"encounterFrom": "Encounter from", "encounterTo": "Encounter to", "encounterSelectDate": "Select date", "header": "Filtering", "invoiceOverviewLimitReached": "Results limited to {{maxValue}} for performance. <br/>Use filters to see others.", "issuers": "Issuers", "paymentMethod": "Payment method", "paymentMethodAny": "Any payment method", "resetAll": "Reset all", "selectIssuer": "Select issuer", "status": "Status", "statusAny": "Any status", "statusDraft": "Draft"}, "invoiceCreditInvoiceCreation": {"success": "Credit invoice created", "error": "Something went wrong, could not create credit invoice"}, "invoiceDeletion": {"success": "Invoice deleted", "error": "Something went wrong, could not delete invoice"}, "invoiceFooter": {"totalDiscount": "Total discount: <amount>{{value}} ISK</amount>", "discountPatientsShare": "Discount patient's insured share", "discountPatientsShareInputError": "Discount can't be higher than patient's share", "discountPatientsShareCreditInvoice": "Discount patient's share: <amount>{{value}} ISK</amount>", "nhiPayableBySubject": "Total patient's insured share: <amount>{{value}} ISK</amount>", "nhiPaysAll": "NHI covers 100% of cost", "nhiPaysAllTooltip": "When enabled, NHI covers 100% of eligible items. Patient pays only for non-NHI items.", "totalInvoice": "Total invoice: <amount>{{value}} ISK</amount>", "totalPayableByPatient": "Total due for patient: <amount>{{value}} ISK</amount>", "totalPayableByInsurance": "Payable by insurance: <amount>{{value}} ISK</amount>", "totalVAT": "Total VAT: <amount>{{value}} ISK</amount>"}, "invoiceOverviewHeading": "Invoice Overview", "paymentMethod": {"menuLabel": "Select payment method", "unselectedTag": "Select", "update": {"error": "Failed to update payment method"}}, "invoiceEmailPopover": {"heading": "Email invoice", "inputLabel": "Recipient email", "sendEmail": {"success": "Invoice has been sent to {{email}}", "error": "Something went wrong, could not send invoice in email"}, "submitButton": "Send"}, "menuButton": {"emailInvoice": "Email invoice", "printInvoice": "Print invoice", "createCreditInvoice": "Create credit invoice", "deleteDraftInvoice": "Delete draft invoice"}, "refreshButtonLabel": "Refresh"}, "manageDepartment": {"configuration": "Configuration", "externalId": "[LITE] EHR departmentId", "selectDepartment": "Select department", "teams": "Teams in Department", "createNewTeam": "Create Team"}, "manageOrg": {"departmentsAndTeams": "Departments and Teams", "configuration": "Configuration", "orgTeams": "Global Teams", "emailSubjectPrefix": "Email prefix (header)", "createDepartment": "Create Department"}, "teamStatistics": {"statsButton": "Statistics", "since_midnight": "since midnight", "last_week": "last week", "current_year": "current year", "nullStatus": "Missing status", "avgAge": "Age (male %)"}, "manageTeam": {"providerNotInAnyTeam": "You are not member of any Teams", "serviceTypeUnset": "custom service", "serviceType": "Service type", "editMembers": "Team members", "addMember": "Add member", "allMembersAdded": "All Providers added", "editTeam": "Edit Team", "errorLoadingDepartments": "Error loading Departments", "createTeam": "Create Team"}, "manageProvider": {"changePassword": "Change password", "currentPassword": "Current password", "newPassword": "New password", "repeatPassword": "Repeat new password", "updatePassword": "Update password", "userSettings": "User settings", "passwordChangeSuccess": "password has been changed successfully", "defaultInvoiceIssuer": "Default invoice issuer"}, "providerView": {"userSince": "User since"}, "forms": {"addOption": "Add option", "keyboardFirst": "Keyboard first", "allowMultiple": "Allow multiple", "allowCustomValue": "Allow custom value", "attachToScore": "Attach to score", "close": "Close", "errors": {"formatNotSupported": "Format not supported"}, "description": {"description": "Description popup to explain form handle to user", "title": "Description"}, "initialScroll": {"description": "Step value on first mouse-scroll", "title": "Initial scroll"}, "initialValue": {"description": "Initial field value", "title": "Initial value"}, "optionDescription": {"description": "A better description of option meaning", "title": "Description"}, "placeholder": {"description": "Descriptive info text input field", "title": "Placeholder"}, "prefix": "Prefix", "removeFromScore": "<PERSON><PERSON><PERSON> from score", "renderEmptyFormHandleSelect": {"description": "If no option selected, insert provided text", "title": "Empty handle text"}, "renderEmptyFormText": {"description": "If all form handles are empty, insert provided text", "title": "Empty form text"}, "renderScoreFormat": {"description": "Allows custom formatting of rendered output by using the three blocked constants. Set format in handle below.", "title": "Rendered Score format"}, "renderText": "Render text", "renderedValue": {"description": "Overrides option name with human readable value. For example if option name is a medical jargon such as \"dysuria\" which doctor easily recognises and selects while in medical note, we want to use human phrase \"pain on urination\" instead", "title": "Value"}, "save": "Save", "scoreValue": {"description": "Score value is used to calculate trigger score, i.e. score total of 10 or above is highly sensitive for a serious condition and will trigger Score that this handle is attached too.", "title": "Score value"}, "step": {"description": "Incremental steps when using mouse wheel scroll ('up/down')", "title": "Scroll step"}, "suffix": "Suffix", "triggerRange": "Trigger range"}, "inlineGuide": {"guideModel": {"description": "Inline guide related to Model content", "title": "Guide"}, "previewKind": {"description": "How the tutorial field is presented to the user either a inline text or a icon", "title": "Preview type"}, "tutoring": "Tutoring", "videoUrlIncorrectSyntax": "Video url does not match required syntax"}, "interventionPeriod": {"noViewPermission": "You are not authorised to view this page", "hiddenEntry": "{{count}} other entry hidden", "hiddenEntry_plural": "{{count}} other entries hidden", "hiddenInterventionPeriod": "{{count}} other intervention period hidden", "hiddenInterventionPeriod_plural": "{{count}} other intervention periods hidden", "addInterventionPeriod": "Add Intervention Period", "addToInterventionPeriod": "Add to Intervention Period"}, "journalEntryStatuses": {"saved": "All changes saved", "saving": "Saving changes...", "lastSavedAt": "Last saved at", "error": "Could Not Save Changes. Trying again in 10s"}, "powerMenu": {"noMatchingCommands": "No matching commands"}, "subjectInfo": {"allergies": "Allergies", "diagnosis": "Diagnosis", "labResults": "Lab results", "medications": "Medications", "operations": "Operations", "privateNote": "Private note", "selectSubject": "Select subject", "subjectJournal": "Subject journal"}, "subjectJournal": {"signItem": "Sign", "hideItem": "<PERSON>de", "journalEntries": {"createNewEntry": "Create new entry", "blankEntry": "Blank entry", "createEntryError": "We were unable to create a new journal entry. No data was lost due to this error. Please try again, but if the problem persists please contact support at ", "templateGallery": "Template gallery", "filterTemplates": "Filter templates", "addSupplements": "Add prescription, clinical coding, certificate or billing", "entryDeleted": "Journal entry has been deleted", "deleteFailed": "Failed to delete", "entryOptions": "Journal Entry Options", "copyEntry": "Copy entry", "entryCopied": "Journal entry has been copied", "deleteEntry": "Delete entry", "deleteConfirmation": "Are you sure you want to delete this journal entry?", "deletePermanentWarning": "This action is permanent and can't be undone.", "entryCompleted": "Journal entry has been completed", "undoCompletionFailed": "Failed to undo completion", "displayError": "We've encountered an error while displaying this data. Please refresh the page.", "validStateRequired": "Write a note or submit clinical data before completing the entry", "completeEntry": "Complete Entry", "entryCompletedLabel": "Entry completed", "undoAvailableUntil": "Undo available until {{date}}, {{time}}", "undo": "Undo"}, "inboundData": {"openWaitingListButton": "Open waiting list", "waitingListEntryTitle": "Waiting list entry", "close": "Close", "decline": "Decline", "markAsRead": "<PERSON> as read", "accept": "Accept", "bookAppointment": "Book appointment", "declineClinicalReferral": "Decline clinical referral", "cantDeclineReferralHasBookedEvent": "You can't decline this referral because there is a booked event", "declineClinicalCorrespondence": "Decline clinical correspondence", "declineClinicalCorrespondenceMessage": "Are you sure you want to decline this clinical correspondence?", "clinicalCorrespondenceDeclined": "Clinical correspondence has been declined", "reasonForDeclining": "Reason for declining", "declined": "{{inboundType}} declined", "cancelled": "{{inboundType}} cancelled", "goToEvent": "Go to event"}, "outboundMessages": {"cancelRequest": "Cancel request", "description": {"DOCTORS_LETTER": "Doctor letter", "REFERRAL": "some referral description"}, "documentText": "Document text", "draft": "Draft", "organisationProvider": "Organisation provider", "recipientOrganisation": "Recipient organisation", "noOrganisationsFound": "No organisations found", "searchOrganisation": "Type to search for organisation", "selectOrganisation": "Select organisation", "recipientOrganisationGuide": "Select organisation and department. For some organisations, individual providers may be listed instead of departments.", "organisationProviderGuide": "Indicate a specific clinician as recipient. This does not guarantee that the clinician immediately sees the item, it is dependent on the organisations internal workflow.", "documentTextGuide": "Keep in mind that the recipient can see the complete journal entries in their system. Thus, you should not need to include extensive notes or attachments.", "addEntryContent": "Add entry content"}, "viewDocument": "View document"}, "validations": {"cannotBeEmpty": "Cannot be empty", "invalidEmail": "<PERSON><PERSON> is invalid", "isNotEmail": "Does not match an email address", "nameIsInvalid": "Name is invalid", "nameIsTooShort": "Name cannot have less then 3 characters", "passwordIsInvalid": "Password is invalid", "passwordIsTooShort": "Password must be at least 8 characters long", "passwordsNoMatch": "Passwords do not match", "phoneNumberIsInvalid": "Phone must be at least 7 numeric characters"}, "subjectEdit": {"screenTitle": "Edit Subject", "configuration": "Configuration"}, "providerInbox": {"untitledJournalEntry": "Untitled entry"}, "dashboard": {"notTeamMember": "You are not member of Team, you cannot edit this dashboard", "priority": "Priority", "duration": "Duration", "empty_encounter": "None", "addPeople": "Add People", "filterDashboard": "Filter dashboard", "filterByStatus": "Filter by status", "filterByTag": "Filter by tag", "filterByTagDescription": "You can choose to show or hide items based on a tag. Add a #hashtag to the note to see the option appear in this menu."}, "subjectFields": {"name": "Name", "subject": "Subject", "location": "Location", "age": "Age", "gender": "Gender"}, "encounterFields": {"arrival": "Arrival reason", "dietaryAllowance": "Diet", "responsibleProviders": "Providers", "journalEntries": "Entries"}, "editableNote": {"note": "Message", "updateNote": "Update message", "clickToEdit": "Click to edit"}, "addResponsibleProviders": {"providers": "Responsible providers", "addProviders": "Add", "addMe": "Add me"}, "journalEntryBlockClinicalCoding": {"title": "Clinical coding", "closesWithEncounter": "Closes with <PERSON><PERSON><PERSON><PERSON>?", "criticalTypeLabel": "Is critical code?", "allergyDiagnosis": "Physical reaction?"}, "drugPrescription": {"title": "Prescription", "selectDrug": "Select medication", "searchForDrug": "Search for medication...", "dosing": "<PERSON><PERSON>", "reasoning": "Reason for prescription", "drugIntention": "Dosing instructions", "drugIntention_placeholder": "Shorthand or free-text", "clinicalIndication": "Clinical indication", "maxDailyUnits": "Max daily units", "daysBetweenDispension": "Days between", "rMarked": "R", "packageCount": "Packages", "duration": "Duration", "validFrom": "<PERSON>id from", "validTo": "Valid to", "from": "From", "to": "To", "commentToDispenser": "Note to pharmacist", "reSelectDrug": "Select another medication", "submit": "Submit", "noFilterMatch": "No match", "send": "Send", "sending": "Sending...", "resendPrescription": "Resend Prescription", "delete": "Delete", "draft": "Draft", "errorStatus": "Error", "failedToSend": "Failed to send", "serlyfjaskra": "S<PERSON>rlyfjaskrá", "warnings": {"hasSecurityNote": {"heading": "Medication has special warnings or considerations", "details": "Medication has special warnings or considerations the prescriber must be aware of, please see <PERSON><PERSON><PERSON><PERSON><PERSON>"}, "isExempted": {"heading": "Medication is on exemption list", "details": "This drug is listed on the Directorate of Health's exemption list. A justification is required to prescribe this medication. Please provide clinical reasoning for why this drug is necessary for the patient. [Read more](https://www.lyfjastofnun.is/lyf/undanthagulyf/upplysingar-um-undanthagulyf-fyrir-heilbrigdistarfsfolk/)"}, "isOtc": {"heading": "OTC drug", "details": "There is no need for prescription unless the goal is to lower cost to patient."}, "requiresSpecialist": {"heading": "Specialist required", "details": "The pharmacist will check your license and speciality."}, "addictiveDrug": {"generic": {"heading": "Addictive medication", "details": "Please consider harmful effects of drug dependency, see [prescription guidelines](https://island.is/lyfjaavisanir-eftirlit-leidbeiningar/leidbeiningar-um-goda-starfs<PERSON><PERSON>-laekna)."}, "monitored": {"heading": "Monitored medication", "details": "Pharmacy is required to request patient's ID and hand-written signature when received and report delivery to relevant agency. There are restrictions on dosing period and quantity."}, "monitored_quantityRestrictions": "restricted to 30 days duration and ", "quantityRestricted": "addiction medication is restricted as follows: "}, "doctorNumberMissing": "A requirement for sending a drug-prescription is that provider has physician license number. Please contact your administrator", "hideDetails": "Hide details", "seeDetails": "See details", "moreWarnings": "more warnings"}, "error": {"networkError": "Could not reach Leviosa servers. Please check your internet connection and try again."}}, "clinicalCoding": {"clinicalCoding": "Clinical coding", "tagWithCcLabel": "Tag with Clinical-Code", "createNewCc": "--- Create new Clinical-Code:", "warnBeforeClose": "Closing a clinical-coding unit is a permanent action and removes the condition from the patient's coding history. This code cannot be tagged again. Please confirm that you understand <PERSON><PERSON>'s way of coding before closing this code.", "recentlyUsedCodes": "Recently used codes", "description": "Description", "markAsCritical": "<PERSON> as critical", "entryWillBeMarkedAsOriginOfDiagnosis": "This entry will be marked as the origin of this diagnosis", "selectCodingType": "Select {{type}}", "newCoding": "New {{type}}"}, "userManagement": {"deactivateUser": "Deactivate User", "activationStatus": "Activation status", "activationStatusInfo": "Deactivating user removes all access to Leviosa. User will not be able to log in or access any data. User can be reactivated at any time."}, "inviteProvider": {"emailAlreadyExists": "This email has already been registered. Please use a different email address or resend the invitation from the user management page.", "personaIdAlreadyExists": "This persona id has already been registered. Please resend the invitation from the user management page", "phoneNumberAlreadyExists": "This phone has already been registered. Please resend the invitation from the user management page", "ehrUserIdAlreadyExists": "This EHR user id has already been registered.", "ehrInvalidFormat": "LITE Saga-ID is integer value, min 1.", "externalEhrIdPlaceholder": "integer value matching User ID in Saga"}, "locations": {"organisationTitle": "Organisation has {{buildingCount}} buildings with  {{roomsCount}} rooms and  {{bedsCount}} beds", "buildingDescription": "{{label}} building has {{corridorsCount}} corridors with {{roomsCount}} rooms and {{bedsCount}} beds", "corridorDescription": "{{label}} corridor has {{roomsCount}} rooms and {{bedsCount}} beds"}, "manageSubject": {"personaIdTypes": {"IS": "icelandic 'kennitala' (10 digits)"}, "invalidPersonaId": "Invalid persona id", "registerNewSubject": "Register subject", "editSubject": "Edit subject", "personaIdAlreadyExists": "Subject already exists", "missingRequiredFields": "Please fill in all required fields", "basicInfo": "Personal details", "dateOfBirth": "Date of birth", "nationality": "Nationality", "phoneNumber": "Phone number", "address": "Address", "addressLine1": "Street address 1", "addressLine2": "Street address 2", "city": "City", "postalCode": "Postal code", "region": "Region", "country": "Country", "subjectUpdateComplete": "Subject data was updated.", "validationErrors": {"generic": "Field is missing or invalid", "requiredField": "Required field"}, "pseudoIdNumberInfoPanelText": "You are about to create a new subject with a pseudo ID number (gervik<PERSON><PERSON><PERSON><PERSON>)."}, "journalTemplates": {"uniqueReferenceError": "Reference must be unique", "updateReferenceError": "Something went wrong, could not update reference", "addDescription": "Click to add description", "addTitle": "Click to add title", "addSectionTitle": "Click to add section title", "addReference": "Click to add reference", "addJournalEntryTitle": "Click to add title for entry", "sectionTitle": "Section title", "publish": "Publish", "resolutionNote": "Resolution note", "drugPrescription": "Drug Prescription", "addSupplements": "Add supplement", "delete": "Delete", "cancel": "Cancel", "add": "Add", "deleteConfirmation": "Are you sure you want to delete this?", "showTemplateOptions": "Add title for entry, reference, description", "addSection": "Add new section", "createdAt": "Created at: {{date}}, {{time}}", "updatedAt": "{{date}}, {{time}}", "publishSuccess": "Template has been published", "publishError": "Could not publish template. {{error}}", "missingTitles": "All sections must have a title before publishing", "deleteSection": "Delete section", "deleteSectionSuccess": "Section has been deleted", "deleteSectionError": "Something went wrong, could not delete section", "deleteTemplate": "Delete template", "deleteTemplateConfirmation": "Are you sure you want to delete this template? This cannot be undone.", "deleteTemplateError": "Something went wrong, could not delete template", "keepTemplate": "Keep template", "addSectionInlineGuide": "Add section inline guide", "addTemplateInlineGuide": "Add template inline guide", "clickToAddTemplateInlineGuide": "Click to add template inline guide", "clickToAddSectionInlineGuide": "Click to add section inline guide", "titleRequired": "Title cannot be empty", "sectionTitleRequired": "Section title cannot be empty", "keepSection": "Keep section", "deleteSectionConfirmation": "This action will delete this section from the template. This cannot be undone.", "documentBasedOn": "This document is based on:", "customise": "Customise", "customiseFailed": "Something went wrong. Could not create customised template.", "addReferenceKey": "Add API reference key", "referenceKeyHeading": "Update API reference key", "apiReferenceKey": "API reference key", "referenceKeyDescription": "This reference key is used by external systems to apply the template through the Leviosa API.", "publishTemplate": {"PRIVATE": "Publish as private", "PUBLIC_READ": "Publish as publicly viewable", "PUBLIC_WRITE": "Publish as publicly editable"}, "publishAccessScopeError": "Failed to set access for template, defaulting to private"}, "communications": {"noSubjectPhoneNr": "No subject phone number", "registerPhoneNrToSend": "Register recipient phone number to be able to send text messages", "noMessagesSentToSubject": "No messages have been sent to this subject", "smsSent": "SMS has been sent", "smsNotSent": "Failed to send SMS, please try again", "automaticRemindersForAppointments": "Automatic reminders for upcoming appointments", "onlyShowingMostRecentMessages": "We are only showing the 5 most recently created messages"}, "subjectSummary": {"showClosedCodes": "Show resolved codings", "hideClosedCodes": "Hide resolved codings", "recentEvents": "Recent events", "errorLoadingRecentEvents": "Something went wrong fetching recent events, please try again.", "eventCancelled": "Event cancelled"}, "waitingList": {"addNewButton": "Add new", "addNewButtonDisabledTooltip": "You do not have permission to add to the waiting list", "assignee": {"add": "Add assignee", "change": "Change assignee", "unassigned": "Not assigned"}, "bookAppointmentButton": "Book appointment", "linkAppointment": "Link appointment", "changePrioritySelectTooltip": "Change priority", "filterAll": "All", "filterAssignedCount": "Assigned to me ({{count}})", "filterUnassignedCount": "Unassigned ({{count}})", "note": {"addButton": "Add note", "cancelButton": "Cancel", "closeButton": "Close", "edit": "Edit note", "failedToUpdateError": "Failed to update note, please try again", "label": "Note", "saveButton": "Save", "view": "View note"}, "noWaitingListItems": "No waiting list items found", "mainHeading": "Waiting List", "removeFromWaitingListButtonTooltip": "Remove from waiting list", "service": {"add": "Add service", "change": "Change service", "search": "Search...", "unassigned": "No service"}, "subject": {"openJournal": "Open journal", "selectSubject": "Select subject"}, "tableColumns": {"assignee": "Assignee", "dateAdded": "Date added", "entryType": "Entry type", "serviceType": "Service type", "subject": "Subject", "team": "Team"}, "team": {"add": "Add team", "change": "Change team", "search": "Search...", "unassigned": "Not assigned"}, "referralModalError": "Something went wrong while fetching the referral, please try again", "successLinkingItemToEvent": "Waiting list entry has been successfully linked to an existing appointment", "errorLinkingItemToEvent": "Failed to link waiting list entry to an existing appointment, please try again", "linkAppointmentToWaitingListEntry": "Link this appointment with <bold>{{subjectName}}</bold>'s waitlist entry?", "link": "Link", "noProvider": "No provider"}, "addSupplements": {"addSupplements": "Add supplements", "drugPrescription": "Drug prescription", "freeTextDocument": "Free text document", "certificate": "Certificate", "billing": "Billing", "addAttachment": "Add attachment", "clinicalReferral": "Clinical referral", "clinicalCorrespondence": "Clinical correspondence"}, "calendar": {"popover": {"eventBookedOnline": "Booked online on {{createdAtDate}}", "eventBookedByOnDate": "Booked by {{name}} on {{createdAtDate}}"}, "events": "Events", "showCancelledEvents": "Show cancelled events", "allJournalEntriesCompleted": "All journal entries completed", "journalEntryNotCompleted": "Journal entry not completed", "eventHasBeenCancelled": "Event has been cancelled", "eventsHaveBeenCancelled": "Events have been cancelled", "eventCancelledByOn": "Cancelled by <b>{{provider}}</b> on <b>{{date}}</b>", "failedToCancelEvent": "Failed to cancel event, please try again or contact support", "failedToCancelEvents": "Failed to cancel recurring events, please try again or contact support", "eventForWithOn": "<b>{{eventTitle}}</b> for <b>{{subject}}</b> with <b>{{provider}}</b> on <b>{{date}}</b>", "cancelEvent": "Cancel event", "close": "Close", "cancelThisEventOnly": "Cancel this event only", "cancelThisAndFollowing": "Cancel this and following events", "cancellationReasonOrNote": "Cancellation reason or note", "failedToCreateInvoice": "Failed to create invoice", "creatingInvoice": "Creating invoice...", "createInvoice": "Create invoice", "noSubjects": "No subjects", "noProviders": "No providers", "subjects": "Subjects", "providers": "Providers", "cancelEventsForThisDay": "Cancel events for this day", "cancelEvents": "Cancel events", "cancellingEvents": "Cancelling events...", "noProviderToCancelFor": "A provider must be selected to cancel events", "noEventsToCancel": "There are no events to cancel for <bold>{{name}}</bold> on <bold>{{date}}</bold>.", "scheduleForDate": "<bold>{{name}}</bold>'s schedule for <bold>{{date}}</bold>", "eventsSelected": "<bold>{{number}} events</bold> selected", "phoneNumberNotSet": "Phone number is not set, cannot send cancellation message", "cancellingWillAlsoAffectOthers": "Cancelling will also affect the schedule for {{providers}}", "eventsSuccessfullyCancelled": "Events have been successfully cancelled.", "somethingWentWrong": "Something went wrong, please try again.", "hideProvider": "Hide provider", "showProvider": "Show provider", "removeProvider": "Remove provider", "addProvider": "Add provider", "noProviderFound": "No provider found", "unavailable": "Unavailable", "markDayAsUnavailable": "<PERSON> unavailable for this day", "errorMarkingDayAsUnavailable": "Something went wrong trying to mark day as unavailable, please try again."}}