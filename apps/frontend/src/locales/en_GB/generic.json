{"access": "Access", "active": "Active", "activate": "Activate", "addChapter": "Add chapter", "addFavorite": "Add favorite", "addMembers": "Add members", "addNewChapter": "Add chapter", "addNewSection": "Add section", "addSubLocation": "Add Sub Location", "allSubLocations": "All Sub Locations", "title": "Title", "attachFile": "Attach file", "baseForm": {"description": "Selected formTemplate for new DocSession", "title": "Base form"}, "birthDate": "Birth date", "cancel": "Cancel", "category": "Category", "certificate": "Certificate", "freeTextDocument": "Freetext document", "billing": "Billing", "chapter": "Chapter", "confirm": "Confirm", "chooseLocation": "Choose location", "clickToEdit": "Click to edit", "close": "Close", "comment": "Comment", "content": "Content", "contentLanguage": {"description": "Language of content in template", "title": "Content language"}, "copyAll": "Copy all", "createInterventionPeriod": "Create Intervention period", "createLive": "Create session", "dateTime": "Date Time", "deactivate": "Deactivate", "default": "<PERSON><PERSON><PERSON>", "department": "Department", "departmentConfiguration": "Department configuration", "departmentCreate": "Create department", "departmentExternalId": "Department external Id", "description": "Description", "doCancel": "Cancel", "doClear": "Clear", "doClose": "Close", "doConnect": "Connect", "doCreate": "Create", "doCreateNew": "Create new", "doctorNumber": "Physician license number", "documentSessionDoesWrap": "Document session does wrap", "doDelete": "Delete", "doDeleteConfirmation": "Are you sure you want to delete?", "doDetach": "<PERSON><PERSON>", "doInsert": "Insert", "doOpen": "Open", "doSave": "Save", "doSubmit": "Submit", "drugPrescription": "Drug prescription", "editors": "Editors", "editTeam": "Edit Team", "ehrDocumentFor": "EHR document for", "email": "Email", "errorHandlerMessage": "An unexpected error has occurred. We are working hard to fix bugs like these but please let us know if this occurs frequently!", "errorMessage": "Error occurred", "externalEhrId": "External EHR Id", "externalUserId": "External user Id", "failedCreatingLiveSession": "Could not create live session", "favorite": "Favorite", "favorites": "Favorites", "fileIsNotUploaded": "File is not uploaded", "formatIsNotSupported": "Format not supported, chose one of", "formTemplate": "Form-template", "formTemplateHandleKind": {"BINARY": "Select-dual", "FLOAT": "Float number", "INT": "Integer number", "SELECT": "Select", "TEXT": "Text"}, "formsContainer": "Forms-container", "formsContainers": "Forms-containers", "from": "From", "belongsTo": "belongs to", "fromNewDocument": "From new Document", "global": "Global", "gender": "Gender", "handle": "<PERSON><PERSON>", "hideItem": "Hide item", "insertSnippet": "Insert $t(snippet)", "invitationAccepted": "Invitation accepted", "invitationSubmitted": "Invitation has been submitted successfully", "invite": "Invite", "inviteProvider": "<PERSON><PERSON><PERSON>", "isNameHidden": "Hide name", "isRequired": "Required", "keep": "Keep", "label": "Label", "language": "Language", "leviosaTeam": "The Leviosa team", "leviosaTemplate": "Leviosa template", "limited": "Limited", "list": "List", "loading": "Loading", "location": "Location", "locations": "Locations", "locationsTable": "All locations", "locationCreate": "Create location", "buildingCreate": "Create building", "roomCreate": "Create room", "bedCreate": "Create bed", "corridorCreate": "Create corridor", "longDateFormat": {"description": "Long date format", "title": "Long date format"}, "markdownLink": "See markdown example", "markdownPreview": "Markdown preview", "max": "Max", "message": "Message", "min": "Min", "modelInfo": "Info", "modelKindIds": {"FORM_TEMPLATE": "Form-template", "FORMS_CONTAINER": "Forms-container", "SNIPPETS_CONTAINER": "Snippets-container"}, "modelLanguage": {"description": "Language for search/browse Models menu", "title": "Model language"}, "model_pleural": "Models", "name": "name", "new": "New", "newMessage": "New Message", "nnMode": "nnMode", "no": "No", "noResults": "No results found", "omnipresent": "Omnipresent", "options": "Options", "owner": "Owner", "password": "Password", "patient": "Patient", "patient_plural": "Patients", "profile": "Profile", "personaId": "Persona Id", "invalidPersonaId": "<PERSON><PERSON> is invalid", "phoneNumber": "Phone Number", "preview": "Preview", "provider": "Provider", "publish": "Publish", "refresh": "Refresh", "retry": "Retry", "rxMatchPattern": "Pattern", "saved": "Saved", "score": "Score", "search": "Search", "sectionName": "Section name", "security": "Security", "selected": "Selected", "send": "Send", "sent": "<PERSON><PERSON>", "separator": "Separator", "settings": "Settings", "setAsOwnerQuestion": "Do you want to set <0>{{name}}</0> this user as owner?", "shareabilitySettings": "Shareability settings", "sheetKind": "Sheet-kind", "sheetKind_pleural": "Sheet-kinds", "shortDateFormat": {"description": "Short date format", "title": "Short date format"}, "showDiff": "Show diff in previewState", "sign": "Sign", "signedSuccessfully": "Signed successfully", "specialty": "Specialty", "snippetsContainer": "Snippets-container", "status": "Status", "subjectCreate": "Create subject", "sublocation": "Sublocation", "submit": "Submit", "subscribeTo": "Subscribe to", "success": "Success", "successfulPasswordChange": "Password has been successfully changed", "supplementContainers": "Supplement containers", "supplements": "Supplements", "supplementaryMaterials": "Supplementary materials", "supportsBlockRender": "Name can be prefixed with '@' and will then be hidden in Tablet-Live and in final note.", "tabletLive": "Tablet", "tabletLive_pleural": "Tablets", "tabletTemplate": "Tablet-template", "target": "Target", "team": "Team", "teamCsuName": "Team CSU name", "teamCsuDescription": "Team CSU description", "teamMembers": "Team members", "teams": "Teams", "to": "To", "type": "Type", "uiLanguage": {"description": "Language of application user-interface", "title": "UI language"}, "updateSubjectLocation": "Update Subject Location", "weight": "Weight", "journalEntry": "Journal-entry", "reason": "Encounter reason", "yes": "Yes", "encounter": "Encounter", "encounters": "Encounters", "opened": "Opened", "concluded": "Concluded", "completedNotes": "Completed notes", "addEntry": "Add entry", "otherInterventionPeriods": "Other intervention periods", "clickToAddTitle": "Click to add title", "clickToAddEntryTitle": "Click to add entry title", "untitledEntry": "Untitled Entry", "untitled": "Untitled", "createdAt": "Created at", "updatedAt": "Updated at", "subject": "Subject", "roles": "Roles", "buildingCreated": "Building created", "roomCreated": "Room created", "addressOne": "Address 1", "addressTwo": "Address 2", "city": "City", "postalCode": "Postal code", "region": "Region", "country": "Country", "building": "Building", "capacity": "Capacity", "roomType": "Room type", "locationType": "Location type", "otherSubjects": "and {{count}} other subjects", "personSchedulingConflictWarning": "<strong>{{name}}</strong> has a scheduling conflict for the selected time.", "teamCreate": "Create team", "duplicate": "Duplicate", "calendar": {"repeatIntervalWeek": "Every week until {{date}}", "repeatIntervalWeekDay": "Every weekday until {{date}}", "repeatIntervalMonth": "Every month until {{date}}", "eventPopover": "Edit {{count}} event", "eventPopover_other": "Edit {{count}} events", "eventCreateHeader": "Create from template: {{eventTitle}}"}, "ageShort": {"years": "{{count}} Y", "yearsMonths": "1 Y, {{count}} M", "months": "{{count}} M", "days": "{{count}} D", "monthsDays": "{{monthCount}} M, {{dayCount}} D"}, "time": {"yearsAgo": "{{count}}y ago", "daysAgo": "{{count}}d ago", "hoursAgo": "{{count}}h ago", "minutesAgo": "{{count}}m ago", "justNow": "Just now", "inFuture": "In the future"}, "telephone": "Telephone", "licenseNumber": "License number", "typeToSearch": "Start typing to search", "errorLoadingUpcomingEvents": "Something went wrong while loading upcoming events", "pleaseFillInContactInfoForCommunications": "Please provide the subject's contact information to receive appointment reminders", "subjectHasBeenAdded": "Subject has been added", "couldNotAddSubjectFromExternalRegistry": "Could not add subject from national registry, please try again later", "noSubjectsMatchCriteria": "No subjects matching the search criteria were found within your organisation", "enterValidPersonaIdToSearchExternalRegistry": "Enter a valid persona id to search the National Registry", "enterValidPersonaIdForMoreAccurateResults": "Enter a valid persona id for more accurate search results", "currency": "ISK", "openJournal": "Open journal"}