{"mainLayout": {"goBack": "Go Back", "NotFound": {"heading": "Page Not Found", "message": "The page you're looking for doesn't exist, may have been moved, or is no longer available.", "suggestion1": "<bold>Check the URL</bold> for any errors", "suggestion2": "<bold>Try searching</bold> for what you need.", "suggestion3": "<bold>Go back</bold> to continue working.", "subMessage": "If you believe this is a mistake, please contact support."}, "Unauthorized": {"heading": "Access Denied", "message": "You don't have permission to view this page", "suggestion1": "Check your <bold>login credentials</bold> to ensure you're signed in with the correct account.", "suggestion2": "If you believe you should have access, <bold>contact your administrator</bold> for assistance.", "suggestion3": "<bold>Go back</bold> to continue working.", "subMessage": "If you need further help, please reach out to support."}, "UnexpectedError": {"heading": "Unexpected Error", "message": "Something went wrong on our end", "suggestion1": "Try <bold>refreshing the page</bold> to see if the issue resolves.", "suggestion2": "<bold>Go back</bold> to continue working.", "suggestion3": "<bold>If the problem persists, please contact support.</bold>", "subMessage": "This error has been logged and we're working to fix this as soon as possible."}, "PrintSubjectLabel": {"cancel": "Cancel", "print": "Print", "physiciansNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pleaseEnterRegistrationNumberBelow": "Please enter your registration number below. Once entered, your number will be stored on this device for future use", "registrationNumber": "Registration number", "somethingWentWrong": "Something went wrong, could not print label"}}, "calendar": {"selectSubjectsLabel": "Subjects", "selectSubjectPlaceholder": "Select subject", "selectSubjectRecentSubjects": "Recent subjects", "selectSubjectSearchResults": "Search results", "selectSubjectNoResults": "No results", "selectProviderLabel": "Provider", "selectProviderPlaceholder": "Select provider", "selectProviderNoResults": "No providers found", "setAsDefault": "Set as default"}, "subjectSummary": {"editContactInfo": "Edit contact info", "errorFetchingSSData": "Something went wrong, some data may be missing. Please refresh the page to try again.", "errorPhoneNumberEmpty": "Phone number cannot be empty", "errorPhoneNumberNotValid": "Phone number must only include numbers", "tabContact": "Contact", "tabClinicalSummary": "Clinical summary", "tabBackground": "Background", "tabEncounters": "Encounters", "tabCommunications": "Communications"}, "worklist": {"mainHeading": "Worklist", "searchboxPlaceholder": "Search", "journalEntryTagLabel": "Journal entry", "noItemSelected": "No item selected", "noAccess": "You do not have access to view this item", "somethingWentWrong": "Something went wrong, please try again", "noResolvedItems": "No resolved items", "noUnresolvedItems": "No unresolved items", "statusLabel": "Status", "statusToDo": "To do", "statusInProgress": "In progress", "statusResolved": "Resolved", "typeLabel": "Type", "typeAll": "Everything", "typeAppointmentRequest": "Appointment Request", "typeJournalEntry": "Journal Entry", "typeMultipleSelected": "Type <counter>{{count}}</counter>", "allCaughtUp": "You're all caught up!", "noIncomingTasksAtm": "No incoming tasks at the moment. Enjoy the calm or start something new!", "noItemsMatchFilters": "Oops! No items match your current filters", "broadenYourSearchToSeeMore": "Broaden your search to see more results", "closeItem": "Close", "emptyNote": "This note is empty"}}