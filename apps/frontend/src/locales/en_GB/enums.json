{"Team_ServiceType": {"CLINICAL_ATTENDANCE": "Clinical attendance", "CLINICAL_CONSULTATION": "Clinical consultation"}, "DietaryAllowance": {"UNSET": "", "GENERIC": "Generic", "FASTING": "Fasting", "FASTING_EXCEPT_MEDICATIONS": "Fasting except medications", "FASTING_EXCEPT_DRINKS": "Fasting except drinks", "CLEAR_FLUIDS": "Clear fluids", "DIABETIC": "Diabetic"}, "ClinicalCoding_CriticalCodeId": {"NOT_CRITICAL": "No", "NORMAL": "Yes", "HIGH": "Yes, high-severity"}, "ClinicalCoding_CodingType": {"DIAGNOSIS": "Diagnosis", "OPERATION": "Operation/procedure", "ALLERGY": "Allergy", "BEHAVIOR": "Behavior", "TREATMENT_RESTRICTION": "Treatment restriction", "REQUESTDRUG": "Medication"}, "ClinicalCoding_Codeset": {"ICD_10": "ICD10", "NCSP": "NCSP+", "LEVIOSA_ALLERGY": "", "LEVIOSA_BEHAVIOR": "", "LEVIOSA_TREATMENT_RESTRICTION": ""}, "ProviderSpecialty": {"ATTENDING_PHYSICIAN": "Attending physician", "CLINICAL_PHARMACIST": "Clinical pharmacist", "CLINICAL_PSYCHOLOGIST": "Clinical psychologist", "GENERAL_NURSE": "General nurse", "HOSPITAL_NURSE": "Hospital nurse", "MEDICAL_BIOCHEMIST": "Medical biochemist", "MEDICAL_RECORD_ADMINISTRATOR": "Medical record administrator", "MEDICAL_SECRETARY": "Medical secretary", "MEDICAL_STUDENT": "Medical student", "NURSING_ASSISTANT": "Nursing assistant", "OTHER": "Other", "PHYSICIAN": "Physician", "PHYSIOTHERAPIST": "Physiotherapist", "PRIMARY_CARE_PHYSICIAN": "Primary care physician", "RADIOLOGIST": "Radiologist", "REGISTRAR": "Registrar", "RESIDENT_PHYSICIAN": "Resident physician", "STUDENT_NURSE": "Student nurse"}, "EncounterStatus_state": {"IN_PROGRESS": "In Progress", "CONCLUDED": "Concluded", "CANCELLED": "Cancelled", "CHECKED_OUT": "Checked-out", "PLANNED": "Planned"}, "EncounterStatus_action": {"IN_PROGRESS": "Check-in", "CONCLUDED": "Conclude", "CANCELLED": "Cancel", "CHECKED_OUT": "Check-out", "DELETED": "Delete"}, "EncounterDisposition": {"DISCHARGE": "Discharge", "HOME_LEAVE": "Home leave", "OBSERVATION": "Observation", "TRANSFER": "Transfer", "TRANSFER_EXTERNAL": "Transfer external", "NULL": "None"}, "ActivationStatus": {"DEACTIVATED": "Deactivated", "PENDING": "Pending", "ACTIVE": "Active"}, "inputValidators": {"rxHumanName": "Name expected to be min. 3 chars", "rxGenericEmail": "Email address not valid", "rxIcelandicPhoneNumber": "Icelandic phone number must be of format nnnnnnn (7 letters)"}, "JournalTemplateStatus": {"DRAFT": "Draft", "ARCHIVED": "Archived", "PUBLISHED": "Published"}, "UserManagementView": {"active": "Active", "deactivated": "Deactivated", "invited": "Invited"}, "RoomType": {"EXAMINATION_ROOM": "Examination room", "GROUP_THERAPY_ROOM": "Group therapy room", "INDIVIDUAL_THERAPY_ROOM": "Individual therapy room", "INTERVIEW_ROOM": "Interview room", "MEETING_ROOM": "Meeting room", "OFFICE": "Office", "OPERATING_THEATRE": "Operating theatre", "PATIENT_ROOM": "Patient room", "RESIDENTIAL_ROOM": "Residential room"}, "MessageCategory": {"CUSTOM": "Custom", "REMINDER": "Reminder", "NOTIFICATION": "Notification", "CONFIRMATION": "Confirmation"}, "MessageType": {"SMS": "SMS"}, "MessageStatus": {"SENT": "<PERSON><PERSON>", "ERROR": "Error", "FAILED": "Failed", "DELETED": "Deleted", "SCHEDULED": "Scheduled"}, "Weekday": {"MONDAY": "Monday", "TUESDAY": "Tuesday", "WEDNESDAY": "Wednesday", "THURSDAY": "Thursday", "FRIDAY": "Friday", "SATURDAY": "Saturday", "SUNDAY": "Sunday"}, "Weekday_short": {"MONDAY": "Mon", "TUESDAY": "<PERSON><PERSON>", "WEDNESDAY": "Wed", "THURSDAY": "<PERSON>hu", "FRIDAY": "<PERSON><PERSON>", "SATURDAY": "Sat", "SUNDAY": "Sun"}, "ServiceTypeModality": {"ONSITE": "Onsite", "VIRTUAL": "Virtual", "PHONE_CALL": "Phone call"}, "AvailabilityScheduleStatus": {"DRAFT": "Draft", "PUBLISHED": "Published", "DELETED": "Deleted"}, "PaymentStatus": {"UNPAID": "Unpaid", "PAID": "Paid", "CLAIM_CREATED": "<PERSON><PERSON><PERSON> created"}, "DocumentType": {"APPOINTMENT": {"label": "Appointment", "description": "For planned or booked contacts of any kind (onsite, telephone, virtual). This is the most common type used in the clinical office environment."}, "DROP_IN": {"label": "Drop in", "description": "For any drop-in contact i.e. where patient is received when he arrives and on announced opening hours. This kind of document puts certain restraints e.g. excludes events in the encounter."}, "SURGERY": {"label": "Surgery", "description": "For minor or major surgeries, taking place in a special operating room or theater and which requires planning and additional resources such as equipment and trained staff."}, "ADMISSION": {"label": "Admission", "description": "The admission documentation in a hospital or complex office environment. The document is expected to collect various background information and analysis of some problem that leads up to the admission."}, "PROGRESS": {"label": "Progress", "description": "For any kind of documentation of the progress of the patient who is currently receiving care by the responsible providers. This may happen sporadically, daily or frequently over the day e.g. at each handover between clinical teams\n\nExamples: daily progress note, SOAP note."}, "DISCHARGE": {"label": "Discharge", "description": "For the patient being discharged from the clinical team, whether as transferral to the next team (department) or when leaving the organisation."}, "CLINICAL_ADMINISTRATION": {"label": "Clinical administration", "description": "For any kind of administrative post-hoc work after a proper initial contact. It involves clinical decisions and thus justifies documentation. This kind of work is sometimes done outside the office e.g. from home and is most commonly without contact with the patient. Is sometimes called “paperwork”.\n\nExample: review of incoming lab-results, reviewing questionnaire in preparation for a surgery."}, "CONSULTATION": {"label": "Consultation", "description": "For any kind of 3rd party input in the ongoing Encounter, served by someone who is not the responsible provider. This may be requested or not requested.\n\nThis does not need to involve true contact e.g. when a pathologist is evaluating a specimen and wants to emphasize his findings in the “main traffic” i.e. the current Encounter."}, "PROCEDURE": {"label": "Procedure", "description": "For any kind of procedure that justifies a separate documentation entry. This allows separation of contacts involving formal interventions and enables filtering to include them or exclude from others in journal. This type may require the user to add clinical operational codes for statistics, reporting, quality control etc.\n\nIt is up to the Organisation to decide what defines a procedure that should be documented in separate entry, for example a suture of minor skin wound may be documented in a generic contact entry while some Organisations may required it to be a separate entry.\n\nExamples: suture of skin wound, procedural sedation, shoulder relocation, cardiac arrest."}}, "ListItemPriority": {"CRITICAL": "Critical", "HIGH": "High", "MEDIUM": "Medium", "LOW": "Low", "VERY_LOW": "Very Low", "NO_PRIORITY": "No priority"}, "ListItemType": {"REFERRAL": "Clinical referral", "APPOINTMENT_REQUEST": "Appointment request", "DOCTOR_LETTER": "Clinical correspondence"}, "InboundDataType": {"REFERRAL": "Clinical referral", "DOCTOR_LETTER": "Clinical correspondence"}, "OutboundDataType": {"REFERRAL": "Clinical referral", "DOCTOR_LETTER": "Clinical correspondence"}, "CommunicationStatus": {"DRAFT": "Draft", "SENT": "<PERSON><PERSON>", "READ": "Read", "RECEIVED": "Received", "DECLINED": "Declined", "FAILED": "Failed", "CANCELLED": "Cancelled"}, "EventInstanceStatus": {"READY": "Ready", "CANCELED_BY_PROVIDER": "Provider cancelled", "CANCELED_BY_SUBJECT": "Subject cancelled", "NO_SHOW": "No-show", "DELETED": "Delete"}, "PaymentMethods": {"BankTransfer": "Transfer", "Card": "Card", "Cash": "Cash", "Claim": "<PERSON><PERSON><PERSON>", "Other": "Other"}, "CalendarColor": {"Blue": "Blue", "Gray": "Grey", "HotOrange": "Hot Orange", "Lavender": "Lavender", "LevBlue": "Leviosa Blue", "LevGreen": "<PERSON><PERSON> Green", "LightBlue": "Light Blue", "Olive": "<PERSON>", "Orange": "Orange", "Pink": "Pink"}}