declare module "react-i18next/hooks" {
  export const initReactI18next: any
}

declare module "react-speech-recognition" {
  interface Command {
    command: string | RegExp
    callback: (...args: any[]) => unknown
    isFuzzyMatch?: boolean
    matchInterim?: boolean
    fuzzyMatchingThreshold?: number
  }

  export interface ListeningOptions {
    continuous?: boolean
    language?: string
  }

  interface SpeechRecognition {
    getRecognition(): SpeechRecognition | null
    startListening(options?: ListeningOptions): Promise<void>
    stopListening(): void
    abortListening(): void
    browserSupportsSpeechRecognition(): boolean
  }

  export interface SpeechRecognitionOptions {
    transcribing?: boolean
    clearTranscriptOnListen?: boolean
    commands?: ReadonlyArray<Command>
  }

  export function useSpeechRecognition(options?: SpeechRecognitionOptions): {
    transcript: string
    interimTranscript: string
    finalTranscript: string
    listening: boolean
    resetTranscript: () => void
  }

  declare const SpeechRecognition: SpeechRecognition

  export default SpeechRecognition
}

declare module "react-world-flags" {
  export default Flag
}
