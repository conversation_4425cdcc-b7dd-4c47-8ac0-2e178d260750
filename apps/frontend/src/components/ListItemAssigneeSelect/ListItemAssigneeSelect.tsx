import { gql, useApolloClient } from "@apollo/client"
import { SelectProviderProps } from "@ariakit/react"
import { startTransition, useState } from "react"
import { useTranslation } from "react-i18next"

import { ComboboxProvider, SelectProvider } from "components/Ariakit"
import { ProviderSelectPopover } from "components/ProviderSelect/ProviderSelectPopover"
import Restricted from "features/authentication/components/Restricted/Restricted"
import { WaitingListItem } from "features/waiting-list/components/WaitingListPage/WaitingListPage"
import { Text, TableCellSelect } from "ui"

import {
  PermissionKey,
  ProviderSpecialty,
  useUpdateListItemsMutation,
} from "generated/graphql"

type SelectedAssigneeProps = {
  value?: string | null
}
const SelectedAssignee = ({ value }: SelectedAssigneeProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  return value ? (
    <>{value}</>
  ) : (
    <Text secondary>{tRoutes("assignee.unassigned")}</Text>
  )
}

type ListItemAssigneeSelectProps = {
  isEditable?: boolean
  id: string
  value?: string | null
  waitingListItem?: WaitingListItem
  portal?: boolean
  placement?: SelectProviderProps["placement"]
  refetchQueries?: string[]
  open?: boolean
  onClose?: (event: Event) => void
}

export const ListItemAssigneeSelect = ({
  isEditable = true,
  id,
  value,
  waitingListItem,
  portal,
  placement,
  refetchQueries = [],
  open,
  onClose,
}: ListItemAssigneeSelectProps) => {
  const { t: tRoutes } = useTranslation("routes", { keyPrefix: "waitingList" })

  const [searchValue, setSearchValue] = useState("")

  const [updateListItems] = useUpdateListItemsMutation()

  const client = useApolloClient()

  if (!isEditable) {
    return <SelectedAssignee value={value} />
  }

  return (
    <Restricted
      to={PermissionKey.ListsWaitingListEdit}
      fallback={<SelectedAssignee value={value} />}
    >
      <ComboboxProvider
        resetValueOnHide
        setValue={(value) => {
          startTransition(() => {
            setSearchValue(value)
          })
        }}
      >
        <SelectProvider
          defaultValue={value || ""}
          setValue={(providerId: string) => {
            const cachedProvider = client.readFragment<{
              id: string
              name: string
              specialty: ProviderSpecialty
            }>({
              id: `Provider:${providerId}`,
              fragment: gql`
                fragment ProviderFragment on Provider {
                  id
                  name
                  specialty
                }
              `,
            })

            updateListItems({
              variables: {
                input: {
                  id,
                  assigneeId: {
                    set: providerId || null,
                  },
                },
              },

              optimisticResponse: waitingListItem && {
                updateListItems: [
                  {
                    ...waitingListItem,
                    assignee: cachedProvider?.id
                      ? {
                          id: providerId,
                          name: cachedProvider.name,
                          specialty: cachedProvider.specialty,
                          __typename: "Provider",
                        }
                      : null,
                  },
                ],

                __typename: "Mutation" as const,
              },
              refetchQueries,
            })
          }}
          placement={placement}
        >
          <TableCellSelect
            tooltipContent={tRoutes(value ? "assignee.change" : "assignee.add")}
          >
            <SelectedAssignee value={value} />
          </TableCellSelect>
          <ProviderSelectPopover
            sameWidth={false}
            searchValue={searchValue}
            unmountOnHide
            portal={portal}
            open={open}
            onClose={onClose}
          />
        </SelectProvider>
      </ComboboxProvider>
    </Restricted>
  )
}
