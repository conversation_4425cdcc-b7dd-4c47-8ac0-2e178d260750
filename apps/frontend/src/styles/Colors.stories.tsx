import { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react-vite"
import c from "classnames"
import React, { ReactNode, useLayoutEffect, useState } from "react"

import Icon from "components/Icon/Icon"
import Popover from "components/Popover/Popover"
import { color as colorStyle } from "styles/colors"
import { ButtonGroup } from "ui"
import Button, { ButtonOwnProps } from "ui/components/Button/Button"
import Input from "ui/components/Input/Input"
import Switch from "ui/components/Switch/Switch"
import { Heading } from "ui/components/typography/Heading/Heading"
import { Text } from "ui/components/typography/Text/Text"

import styles from "./ColorStories.module.css"

const colorOptions = {
  levBlue: colorStyle.levBlue,
  neutral: colorStyle.neutral,
  levGreen: colorStyle.levGreen,
  lightBlue: colorStyle.lightBlue,
  orange: colorStyle.orange,
  pink: colorStyle.pink,
  success: colorStyle.success,
  warning: colorStyle.warning,
  critical: colorStyle.critical,
  hotOrange: colorStyle.hotOrange,
  blue: colorStyle.blue,
  lavender: colorStyle.lavender,
  gray: colorStyle.gray,
  olive: colorStyle.olive,
}
type Color = keyof typeof colorOptions

type ColorStoryProps = {
  color: Color | ""
  inputStatus: "default" | "success" | "error" | "warning"
  variant: "" | "light" | "dark"
}

//👇 This default export determines where your story goes in the story list
export default {
  title: "Components/Colors",
  component: Popover,
  argTypes: {
    color: {
      control: "select",
      options: ["", ...Object.keys(colorOptions)],
    },
    inputStatus: {
      control: "select",
      options: ["default", "success", "error", "warning"],
    },
    variant: {
      control: "select",
      options: ["", "light", "dark"],
      defaultValue: "off",
    },
    buttonGroupVariant: {
      control: "select",
      options: ["outline", "clear", "filled", "filled-light"],
    },
  },
  subcomponents: { Button },
} as Meta<ColorStoryProps>

type Story = StoryObj<
  ColorStoryProps & { buttonGroupVariant: ButtonOwnProps["variant"] }
>

export const Main: Story = {
  args: {
    inputStatus: "default",
    color: "",
    variant: "",
    buttonGroupVariant: "outline",
  },
  render: (args) => {
    return (
      <div style={{ display: "flex", gap: 16, flexDirection: "column" }}>
        <div
          style={{
            width: 700,
            height: "fit-content",
            maxHeight: "unset",
            display: "grid",
            gap: 16,
            padding: 16,
            justifyItems: "start",
            zIndex: 0,
            borderRadius: 8,
          }}
          className={c(
            args.color && colorStyle[args.color],
            args.variant && colorStyle[args.variant]
          )}
        >
          <Heading>Colors</Heading>
          <Text>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum
            condimentum cursus varius. Sed condimentum nisi id metus porta
            efficitur. Vestibulum at purus{" "}
            <a href="">
              hendrerit, vehicula elit sit amet, varius metus. Vivamus sem
              lacus, scelerisque id sodales eget,
            </a>
            placerat nec nisl. Donec placerat velit a rhoncus congue. Lorem
            ipsum dolor sit amet, consectetur adipiscing elit. Donec aliquet
            odio sapien, a sodales neque.
          </Text>
          <Text secondary>
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Vestibulum
            condimentum cursus varius. Sed condimentum nisi id metus porta
            efficitur. Vestibulum at purus hendrerit, vehicula elit sit amet,
            varius metus. Vivamus sem lacus, scelerisque id sodales eget,
            placerat nec nisl. Donec placerat velit a rhoncus congue. Lorem
            ipsum dolor sit amet.
          </Text>

          <div style={{ display: "flex", gap: 16 }}>
            <Button icon={<Icon name="add-circle-line" />} className="button">
              Default variant
            </Button>
            <Button
              variant="clear"
              icon={<Icon name="file-copy-line" />}
              className="button"
            >
              Clear variant
            </Button>
            <Button
              variant="filled"
              icon={<Icon name="heart-pulse-line" />}
              className="button"
            >
              Filled
            </Button>
            <Button
              variant="filled-light"
              icon={<Icon name="team-line" />}
              className="button"
            >
              Filled light
            </Button>
          </div>
          <Button
            variant={args.buttonGroupVariant}
            icon={<Icon name="prohibited-2-line" />}
            className="button"
            disabled
          >
            Disabled
          </Button>

          <ButtonGroup variant={args.buttonGroupVariant}>
            <Button aria-selected="true" icon={<Icon name="add-circle-line" />}>
              Selected
            </Button>
            <Button icon={<Icon name="add-circle-line" />}>Selected</Button>
            <Button icon={<Icon name="add-circle-line" />}>Selected</Button>
          </ButtonGroup>

          <div>
            <Input
              label="Try me"
              status={args.inputStatus}
              placeholder="Type something"
            />
            <label>
              <Switch /> Switch it up
            </label>
          </div>
        </div>
      </div>
    )
  },
}

const mainPalette = [
  "levBlue",
  "neutral",
  "levGreen",
  "lightBlue",
  "orange",
  "pink",
]
const stateColors = ["success", "warning", "critical"]
const additionalColors = ["hotOrange", "blue", "lavender", "gray", "olive"]

const ColorVariant = ({
  backgroundColor,
  color = "black",
  children,
}: {
  backgroundColor: string
  color?: string
  children: ReactNode
}) => {
  const ref = React.useRef<HTMLLIElement>(null)
  const [colorCode, setColorCode] = useState("")

  useLayoutEffect(() => {
    if (ref.current) {
      const color = getComputedStyle(ref.current).backgroundColor
      console.log(color)
      setColorCode(color)
    }
  }, [ref.current])

  return (
    <li
      ref={ref}
      className={styles.colorVariant}
      style={{
        backgroundColor,
        color,
      }}
    >
      {children}
      <span>{colorCode}</span>
    </li>
  )
}

const Color = ({ color }: { color: Color }) => {
  const mainTextColor = ["levBlue", "neutral", "critical", "blue"].includes(
    color
  )
    ? "white"
    : "black"

  return (
    <div>
      <Text as="h2">{color}</Text>
      <ul className={`${styles.colorPanel} ${colorStyle[color]}`}>
        <ColorVariant backgroundColor="var(--color-500)" color={mainTextColor}>
          Main | 500
        </ColorVariant>
        <ColorVariant backgroundColor="var(--color-800)" color="white">
          800
        </ColorVariant>
        <ColorVariant backgroundColor="var(--color-400)">400</ColorVariant>
        <ColorVariant backgroundColor="var(--color-700)" color="white">
          700
        </ColorVariant>
        <ColorVariant backgroundColor="var(--color-300)">300</ColorVariant>
        {!(color === "levBlue" || color === "neutral") && (
          <ColorVariant backgroundColor="var(--color-600)" color="white">
            600
          </ColorVariant>
        )}
        <ColorVariant backgroundColor="var(--color-200)">200</ColorVariant>
      </ul>
    </div>
  )
}

export const Palette = () => {
  return (
    <div className={styles.palette}>
      <Heading as="h1" size="display">
        Main Palette
      </Heading>
      <div className={styles.palette}>
        {mainPalette.map((color) => (
          <Color key={color} color={color} />
        ))}
      </div>
      <Heading as="h1" size="display">
        State Colors
      </Heading>
      <div className={styles.palette}>
        {stateColors.map((color) => (
          <Color key={color} color={color} />
        ))}
      </div>
      <Heading as="h1" size="display">
        Addtional
      </Heading>
      <div className={styles.palette}>
        {additionalColors.map((color) => (
          <Color key={color} color={color} />
        ))}
      </div>
    </div>
  )
}
