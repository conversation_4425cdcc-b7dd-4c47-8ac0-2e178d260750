.palette {
  width: 90vw;

  display: grid;
  gap: 64px;

  h2 {
    text-transform: capitalize;
    font-size: 18px;
    margin-bottom: 8px;
  }
}

.colorPanel {
  display: grid;

  grid-template-columns: 1fr 1fr;
  grid-template-rows: 88px repeat(3, 44px);

  :first-child {
    grid-column: 1 / -1;
  }
  :last-child {
    grid-column: -2 / -1;
  }

  > * {
    display: grid;
    align-items: end;
    color: black;
    padding: 12px 16px;
  }
  &:hover span {
    opacity: 0.5;
  }
}
.colorVariant {
  display: flex;
  align-items: end;
  justify-content: space-between;

  span {
    color: inherit;
    opacity: 0;
    transition: color 0.2s;
  }
}

@media (min-width: 768px) {
  .palette .palette {
    grid-template-columns: repeat(2, 1fr);
  }
}
