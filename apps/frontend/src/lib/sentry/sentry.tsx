import * as Sentry from "@sentry/react"
import { EventHint, SeverityLevel } from "@sentry/react"
import { useEffect } from "react"
import {
  createRoutesFromChildren,
  matchRoutes,
  Routes,
  useLocation,
  useNavigationType,
} from "react-router-dom"

import { determineEnvironment, isLocal } from "utils/appEnvironment"

// we're currently not using replays, because they might contain sensitive data
// Match the release name format used in sentry-config script (without "release/" prefix)
const RELEASE = import.meta.env.VITE_GIT_REF_NAME
  ? import.meta.env.VITE_GIT_REF_NAME.replace(/^release\//, "")
  : "unknown"

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_CLINIC_PORTAL_DSN,
  enabled: !isLocal,
  environment: determineEnvironment(),
  tracesSampleRate: 1,
  sampleRate: 0.05,
  profilesSampleRate: 0.05,
  // Add release configuration to match sourcemaps
  release: RELEASE,
  // todo: this does not work correctly currently
  tracePropagationTargets: ["localhost", /^https:\/\/.*\.leviosa\.is\/graphql/],

  integrations: [
    Sentry.browserTracingIntegration(),
    Sentry.captureConsoleIntegration({
      levels: ["warn", "error"],
    }),
    // https://docs.sentry.io/platforms/javascript/guides/react/configuration/integrations/react-router/
    Sentry.reactRouterV6BrowserTracingIntegration({
      useEffect,
      useLocation,
      useNavigationType,
      createRoutesFromChildren,
      matchRoutes,
    }),
    // Extracts all non-native attributes from the error object and attaches them to the event as extra data.
    Sentry.extraErrorDataIntegration(),
  ],

  // filter events before sending them to sentry
  beforeSend(event) {
    const { level, message } = event
    // For console warnings, check if they're Apollo related
    if (level === "warning" && message && typeof message === "string") {
      if (message.includes("apollo")) {
        return event
      }
      return null
    }
    return event
  },

  beforeBreadcrumb(breadcrumb) {
    // we don't want to spam sentry issues with useless deprecation warnings
    const deprecatedMessages = [
      "The `as` prop is deprecated.", // remove once https://leviosa.atlassian.net/browse/DEV-4398 is done i.e. we've removed the deprecated `as` prop
      "React.createFactory() is deprecated", // this is related to react-big-calendar
    ]

    if (deprecatedMessages.some((msg) => breadcrumb.message?.includes(msg))) {
      return null
    }

    // no need to log graphql fetch requests (don't add value)
    if (
      breadcrumb.category === "fetch" &&
      breadcrumb.data?.url.includes("graphql")
    ) {
      return null
    }

    return breadcrumb
  },
})

export const SentryRoutes = Sentry.withSentryReactRouterV6Routing(Routes)

export const logException = (exception: unknown, hint?: EventHint) => {
  Sentry.captureException(exception, hint)
}

export const logMessage = (message: string, captureContext?: SeverityLevel) => {
  Sentry.captureMessage(message, captureContext)
}
