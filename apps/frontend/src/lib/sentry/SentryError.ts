import { Operation } from "@apollo/client"
import * as Sen<PERSON> from "@sentry/react"
import { GraphQLFormattedError, OperationDefinitionNode } from "graphql"

function sanitizeVariables(variables: Record<string, unknown> = {}) {
  const sanitized: Record<string, unknown> = {}

  Object.entries(variables).forEach(([key, value]) => {
    if (value === null) {
      sanitized[key] = null
    } else if (typeof value === "object") {
      sanitized[key] = {} as Record<string, unknown>
      Object.entries(value as Record<string, unknown>).forEach(([k, v]) => {
        ;(sanitized[key] as Record<string, unknown>)[k] = typeof v
      })
    } else {
      sanitized[key] = typeof value
    }
  })

  return sanitized
}

// very simplified regex patterns for sensitive data, in case the BE returns an error message with sensitive data
const SENSITIVE_PATTERNS = {
  kennitala: {
    regex: /\b\d{6}-?[a-zA-Z0-9]{4}\b/g,
    keywords: ["kennitala", "kt", "ssn", "person_id", "persona_id"],
    replace: "[xxxxxx-xxxx]",
  },
  email: {
    regex: /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g,
    keywords: ["email", "e-mail", "mail"],
    replace: "[MASKED EMAIL]",
  },
  name: {
    regex: /\b([A-Z][a-z]+ [A-Z][a-z]+)\b/g,
    keywords: ["name"],
    replace: "[MASKED NAME]",
  },
  phone: {
    // - Optional + or 00 at start
    // - Allows spaces, dashes, parentheses
    // - Requires at least 7 digits
    regex: /(?:\+|00)?[-\s.(]?\d{3}[-\s.)]+\d{3}[-\s.]+\d{4}\b/g,
    keywords: ["phone", "number"],
    replace: "[MASKED PHONE]",
  },
}

function sanitizeErrorMessage(message: string) {
  for (const { regex, keywords, replace } of Object.values(
    SENSITIVE_PATTERNS
  )) {
    if (keywords.some((keyword) => message.toLowerCase().includes(keyword))) {
      message = message.replace(regex, replace)
    }
  }
  return message
}

export class SentryError extends Error {
  arg: string
  code: string
  operationName: string
  operationType: string
  path: string
  variables: { [key: string]: unknown }

  constructor(graphQLError: GraphQLFormattedError, operation: Operation) {
    super(sanitizeErrorMessage(graphQLError.message))

    this.name = "GraphQLError"

    const { arg_name, code } = graphQLError.extensions ?? {}
    this.arg = typeof arg_name === "string" ? arg_name : "-"
    this.code = typeof code === "string" ? code : "-"
    this.operationName = operation.operationName
    this.operationType =
      (operation.query.definitions[0] as OperationDefinitionNode)?.operation ??
      "-"
    this.path = graphQLError.path?.join(".") ?? "-"
    this.variables = sanitizeVariables(operation.variables)
  }

  logException() {
    return Sentry.captureException(this, {
      level: "error",
      tags: {
        "graphql-arg": this.arg,
        "graphql-error-code": this.code,
        "graphql-operation-name": this.operationName,
        "graphql-operation-type": this.operationType,
        "graphql-path": this.path,
      },
      extra: {
        variables: this.variables,
      },
    })
  }
}
