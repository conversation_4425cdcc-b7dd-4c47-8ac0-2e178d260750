import { FetchResult } from "@apollo/client"

import { clientConfig } from "../../clientConfig"

const host = clientConfig.host

export default async function fetchGql<TData, TVariables>(
  query: string,
  variables?: TVariables
): Promise<FetchResult<TData>> {
  const response = await fetch(`${host}/graphql`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
    body: JSON.stringify({
      query,
      variables,
    }),
  })

  return response.json()
}
