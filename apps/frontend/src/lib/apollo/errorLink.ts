import { onError } from "@apollo/client/link/error"

import { SentryError } from "lib/sentry/SentryError"
import { isProduction } from "utils/appEnvironment"

const CODES_TO_IGNORE = [
  // HTTP handler codes
  "BAD_REQUEST",
  "FORBIDDEN",
  "UNAUTHORIZED",

  // Custom error codes - authorization
  "ERR_AUTH_NOT_AUTHENTICATED",
  "ERR_AUTH_MUST_BE_ADMIN",
  "ERR_AUTH_MUST_BE_USER",
  "ERR_AUTH_MALFORMED_TOKEN",
  "ERR_AUTH_UNAUTHORIZED",
  // Custom error codes - input
  "ERR_INPUT",
  // Custom error codes - password
  "ERR_PASSWORD",
  // Custom error codes - not found
  "ERR_NOT_FOUND",
  // Custom error codes - permissions
  "ERR_PERMISSIONS",
  // Custom error codes - configuration
  "ERR_NOT_CONFIGURED",
]
const NETWORK_ERROR_MESSAGES_TO_IGNORE = ["Failed to fetch"]

export const errorLink = onError(
  ({ graphQLErrors, networkError, operation }) => {
    if (graphQLErrors) {
      graphQLErrors.forEach((error) => {
        if (!isProduction) {
          console.error(
            `${error.message} (${
              error.extensions ? error.extensions.code : null
            })`
          )
        }

        // Don't log irrelevant user errors to Sentry
        if (
          error.extensions &&
          CODES_TO_IGNORE.includes(String(error.extensions.code ?? ""))
        ) {
          return
        }

        const sentryError = new SentryError(error, operation)
        sentryError.logException()
      })
    }

    if (
      networkError &&
      !NETWORK_ERROR_MESSAGES_TO_IGNORE.includes(networkError.message)
    ) {
      const sentryError = new SentryError(networkError, operation)
      sentryError.logException()
    }
  }
)
