import { from<PERSON>rom<PERSON>, NextLink, Operation, toPromise } from "@apollo/client"
import { onError } from "@apollo/client/link/error"
import DataLoader from "dataloader"

import { broadcastLogout } from "features/authentication/hooks/useDetectIdle"
import {
  clearTokens,
  getTokens,
  setTokens,
  Tokens,
} from "features/authentication/utils/tokenStorage"
import { ErrorCode } from "utils/ErrorCode.enum"

import { PublicRoutes } from "../../routes/RouteStrings"
import fetchGql from "./fetchGql"

const REFRESH_TOKEN_MUTATION = `
  mutation RefreshAccessToken($refreshToken: String!) {
    refreshAccessToken(refreshToken: $refreshToken) {
      accessToken
      refreshToken
    }
  }
`

type RefreshTokenResponse = {
  refreshAccessToken: Tokens
}

/**
 * DataLoader to prevent multiple refresh token requests at the same time.
 * If multiple requests are made at the same time, only one request will be made.
 * The result of the first request will be used for all requests.
 */
const refreshLoader = new DataLoader<string, Tokens | null>(
  async (refreshTokens) => {
    try {
      const response = await fetchGql<
        RefreshTokenResponse,
        { refreshToken: string }
      >(REFRESH_TOKEN_MUTATION, {
        refreshToken: refreshTokens[0],
      })

      if (response?.data?.refreshAccessToken) {
        // Token refresh was successful. Update the tokens and return them.
        const { accessToken, refreshToken } = response.data.refreshAccessToken
        setTokens({ accessToken, refreshToken })

        return refreshTokens.map(() => ({ accessToken, refreshToken }))
      } else {
        console.error("Token refresh failed")
        clearTokens()
        return refreshTokens.map(() => null)
      }
    } catch (error) {
      console.error("Error in refreshLoader:", error)
      clearTokens()
      return refreshTokens.map(() => null)
    }
  }
)

/**
 * Handles logout by clearing tokens, resetting headers, broadcasting logout event,
 * redirecting to login, and preventing further execution in Apollo Link chain.
 */
function handleLogoutAndRedirect(operation: Operation): Promise<never> {
  clearTokens()
  broadcastLogout()
  operation.setContext({
    headers: {},
  })
  window.location.href = PublicRoutes.login

  return new Promise(() => {
    //
  })
}

const refreshAccessTokenAndRetry = async (
  operation: Operation,
  forward: NextLink
) => {
  const tokens = getTokens()
  if (!tokens) {
    return handleLogoutAndRedirect(operation)
  }

  const { refreshToken } = tokens
  const newTokens = await refreshLoader.load(refreshToken)

  if (!newTokens) {
    // Getting refresh token request failed, which means the refreshToken is invalid.
    return handleLogoutAndRedirect(operation)
  } else {
    const { accessToken } = newTokens
    const oldHeaders = operation.getContext().headers

    operation.setContext({
      headers: {
        ...oldHeaders,
        authorization: accessToken,
      },
    })
  }

  return toPromise(forward(operation))
}

export const refreshLink = onError(({ graphQLErrors, operation, forward }) => {
  if (graphQLErrors) {
    const tokenError = graphQLErrors.find(
      ({ extensions }) => extensions?.code === ErrorCode.TOKEN_INVALID
    )

    if (tokenError) {
      return fromPromise(refreshAccessTokenAndRetry(operation, forward))
    }
  }
  return
})
