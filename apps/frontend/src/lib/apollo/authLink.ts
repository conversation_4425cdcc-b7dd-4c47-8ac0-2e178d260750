import { setContext } from "@apollo/client/link/context"

import { getTokens } from "features/authentication/utils/tokenStorage"

export const authLink = setContext((_, { headers }) => {
  const tokens = getTokens()
  const accessToken = tokens?.accessToken ?? null

  // return the headers to the context so httpLink can read them
  return {
    headers: {
      ...headers,
      authorization: accessToken ?? "",
    },
  }
})
