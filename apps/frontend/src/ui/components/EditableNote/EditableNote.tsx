import c from "classnames"
import { useTranslation } from "react-i18next"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import Icon from "components/Icon/Icon"

import { InlineTextForm } from "../InlineTextForm/InlineTextForm"
import styles from "./EditableNote.module.css"

type EditableNoteProps = {
  note: string | null
  className?: string
  showForm: boolean
  setShowForm: (showForm: boolean) => void
  handleNoteSubmit: (value: string) => void
  disabled?: boolean
  size?: "default" | "small"
}

export const EditableNote = ({
  note,
  className,
  showForm,
  handleNoteSubmit,
  setShowForm,
  disabled,
  size = "default",
}: EditableNoteProps) => {
  const { t } = useTranslation("routes", { keyPrefix: "editableNote" })

  if (disabled) {
    return <span>{note}</span>
  }

  return (
    <span className={c(className, size === "small" && styles.small)}>
      {showForm ? (
        <InlineTextForm
          value={note || ""}
          label={t("updateNote")}
          onSubmit={({ value }) => {
            handleNoteSubmit(value)
          }}
          onCancel={() => {
            setShowForm(false)
          }}
          placeholder={t("note")}
          size={size}
        />
      ) : (
        <Tooltip tooltipContent={t("clickToEdit")} placement="bottom-start">
          <button
            className={styles.note}
            onClick={() => {
              setShowForm(true)
            }}
          >
            <Icon className={styles.noteIcon} name="chat-2-line" />
            <span className={c(!note && styles.placeholderNote)}>
              {note || t("note")}
            </span>
          </button>
        </Tooltip>
      )}
    </span>
  )
}
