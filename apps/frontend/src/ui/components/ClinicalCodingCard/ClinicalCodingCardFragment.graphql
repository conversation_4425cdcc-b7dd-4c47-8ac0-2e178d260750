fragment ClinicalCodingCardFragment on ClinicalCoding {
  id
  # COMEBACK language
  code(languageId: IS) {
    id
    codeset
    displayLabel
    codingType
  }
  criticalType

  closedAt
  closedBy {
    id
    name
  }

  updatedAt
  updatedBy {
    id
    name
  }

  # notes referencing CC. Fetching to get the last updatedBy. Should be a field-resolver with counter from BE.
  notes {
    id
    updatedAt
  }
  description
}
