import { Visually<PERSON><PERSON><PERSON> } from "@ariakit/react"
import c from "classnames"
import dayjs from "dayjs"
import { useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import Icon from "components/Icon/Icon"
import { Button, Label, Text, Textarea } from "ui"
import { useDaysAgoFormatter } from "utils/useDaysAgoFormatter"

import {
  ClinicalCodingCardFragmentFragment,
  ClinicalCodingCriticalType,
  ClinicalCodingTemplateFragmentFragment,
  JournalEntryStatus,
  useCloseClinicalCodingMutation,
  useOpenClinicalCodingMutation,
  useUpdateClinicalCodingMutation,
} from "generated/graphql"

import { Counter } from "../Counter/Counter"
import Supplement from "../Supplement/Supplement"
import styles from "./ClinicalCodingCard.module.css"

type ClinicalCodingCardProps = (
  | ClinicalCodingCardFragmentFragment
  | (ClinicalCodingTemplateFragmentFragment & {
      description?: string | null
      notes: []
    })
) & {
  onDelete?: () => void

  isExpanded?: boolean
  resizable?: boolean
  entryStatus?: JournalEntryStatus
  closedAt?: string | null
  closeableCode?: boolean
  showDescription?: boolean
  editableDescription?: boolean
}

export default function ClinicalCodingCard({
  id,
  closedAt,
  description: codeDescription,
  code,
  criticalType,
  notes = [],
  isExpanded = false,
  resizable = true,
  closeableCode = true,
  showDescription = true,
  editableDescription = true,
  updatedAt,
  updatedBy,
  entryStatus,
  onDelete,
}: ClinicalCodingCardProps) {
  const { t } = useTranslation()
  const { t: tEnums } = useTranslation("enums")
  const [showDescriptionForm, setShowDescriptionForm] = useState(false)
  const [description, setDescription] = useState(codeDescription || "")
  const [updateClinicalCoding] = useUpdateClinicalCodingMutation()
  const editButtonRef = useRef<HTMLButtonElement>(null)

  const formatDaysAgo = useDaysAgoFormatter()

  if (!code) return null

  const updateClinicalCodingDescription = () => {
    updateClinicalCoding({
      variables: {
        id,
        input: {
          codeId: code.id,
          description,
          criticalType,
        },
      },
    })
    setShowDescriptionForm(false)
  }

  const [openClinicalCoding] = useOpenClinicalCodingMutation()
  const [closeClinicalCoding] = useCloseClinicalCodingMutation()

  const codingType = tEnums(
    `ClinicalCoding_CodingType.${code.codingType}`
  ).toLocaleUpperCase()
  const codeset = tEnums(`ClinicalCoding_Codeset.${code.codeset}`)

  // If codeset is present, use that as the label otherwise use codingType
  const codingLabel = codeset || codingType

  const firstMentioned =
    notes.length > 0 ? dayjs(notes[0].updatedAt).format("LLL") : ""
  const lastMentioned =
    notes.length > 1
      ? ` - ${dayjs(notes[notes.length - 1].updatedAt).format("LLL")}`
      : ""
  const notesUpdatedAtLabel = `${firstMentioned}${lastMentioned}`

  const completedEntry = entryStatus === JournalEntryStatus.Completed

  const showFooter =
    notes.length > 0 || notesUpdatedAtLabel || onDelete || showDescriptionForm

  const formattedDays = formatDaysAgo(updatedAt)

  const closeAction = (
    <Button
      className={`${styles.action} ${styles.closeAction}`}
      icon={<Icon name={"checkbox-circle-line"} />}
      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation()
        e.preventDefault()
        closeClinicalCoding({
          variables: {
            closeClinicalCodingId: id,
          },
        })
      }}
      variant={"clear"}
    >
      {t("Close")}
    </Button>
  )

  const reopenAction = (
    <Button
      className={`${styles.action} ${styles.reopenAction}`}
      icon={<Icon name={"restart-line"} />}
      onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
        e.stopPropagation()
        e.preventDefault()
        openClinicalCoding({
          variables: {
            openClinicalCodingId: id,
          },
        })
      }}
      variant={"clear"}
    >
      {t("Reopen")}
    </Button>
  )

  const renderDescription = () => {
    if (!showDescription) return null

    if (!editableDescription) {
      return <Text className={styles.viewDescription}>{description || ""}</Text>
    }

    if (showDescriptionForm) {
      return (
        <Textarea
          label={t("Clinical Coding Description")}
          hideLabel
          hideMessage
          textareaProps={{
            autoFocus: true,
          }}
          testId="clinical-coding-description"
          autoGrow
          onKeyDown={(e) => {
            const isCommandOrCtrlPressed = e.metaKey || e.ctrlKey

            if (e.key === "Enter" && isCommandOrCtrlPressed) {
              updateClinicalCodingDescription()
            }
          }}
          onClick={(e) => {
            e.stopPropagation()
            e.preventDefault()
          }}
          onChange={(e) => setDescription(e.target.value)}
          className={styles.descriptionForm}
          defaultValue={description || ""}
        />
      )
    }

    return (
      <>
        <div
          className={c(styles.description, {
            [styles.emptyDescription]: !description,
            [styles.confirmedDescription]: completedEntry,
          })}
          onClick={(e) => {
            e.stopPropagation()
            editButtonRef.current?.click()
          }}
        >
          {description || "Add description"}
        </div>
        <VisuallyHidden
          render={
            <button
              type="button"
              ref={editButtonRef}
              data-testid="clinical-coding-description-empty"
              onClick={(e) => {
                e.stopPropagation()
                setShowDescriptionForm(true)
              }}
            />
          }
        >
          {t("Edit Coding summary")}
        </VisuallyHidden>
      </>
    )
  }

  return (
    <Supplement
      id={id}
      heading={
        <>
          {code.displayLabel}
          {closeableCode && (closedAt === null ? closeAction : reopenAction)}
        </>
      }
      minimizedHeading={code.displayLabel}
      subHeading={
        <div className={styles.subHeading}>
          {codingLabel}
          {showDescription && (
            <div className={styles.updaterInfo}>
              <Icon name="history-line" />
              <div className={styles.updatedByWrapper}>
                <div title={updatedBy.name} className={styles.updatedByName}>
                  {updatedBy.name}
                </div>
                <span> • </span>
                <span>{formattedDays}</span>
              </div>
            </div>
          )}
        </div>
      }
      icon="heart-pulse-line"
      color={
        closedAt
          ? "grey"
          : criticalType === ClinicalCodingCriticalType.NotCritical
            ? "default"
            : "red"
      }
      isMinimized={!isExpanded}
      resizable={resizable}
      confirmed={completedEntry}
      minimizedSubheading={
        <>
          {codingLabel}
          {closedAt && (
            <Text
              as="span"
              weight="regular"
              size={"small"}
              className={styles.closedText}
            >
              {t("Closed")}
            </Text>
          )}
        </>
      }
      contentClassName={styles.supplementContent}
      data-cc-critical-type={criticalType}
    >
      {renderDescription()}

      {showFooter && (
        <div className={styles.supplementContentFooter}>
          {(notes.length > 0 || notesUpdatedAtLabel) && (
            <div className={styles.notesInfo}>
              {notes.length > 0 && <Counter number={notes.length} />}
              {notesUpdatedAtLabel && (
                <Label size="small">{notesUpdatedAtLabel}</Label>
              )}
            </div>
          )}
          {onDelete && !showDescriptionForm && (
            <Button
              onClick={onDelete}
              variant="clear"
              className={styles.removeButton}
            >
              {t("doDelete")}
            </Button>
          )}
          {showDescriptionForm && (
            <div className={styles.confirmationButtons}>
              <Button
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.stopPropagation()
                  e.preventDefault()
                  setDescription(codeDescription || "")
                  setShowDescriptionForm(false)
                }}
                variant="clear"
                className={styles.removeButton}
              >
                {t("cancel")}
              </Button>
              <Button
                onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
                  e.stopPropagation()
                  e.preventDefault()
                  updateClinicalCodingDescription()
                }}
                variant="filled"
                className={styles.removeButton}
              >
                {t("doSave")}
              </Button>
            </div>
          )}
        </div>
      )}
    </Supplement>
  )
}
