.removeButton {
  margin-left: auto;
}

.supplementContent {
  display: flex;
  gap: 12px;
  flex-direction: column;
  padding-left: 24px;
}

.supplementContentFooter {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
}

.notesInfo {
  display: flex;
  gap: 4px;
  align-items: center;
}

.commonDescription {
  position: relative;
  white-space: pre-line;
  z-index: 0;
  margin: -6px 0 -6px -8px;
  padding: 6px 8px 6px 8px;
  border-radius: var(--radius-button-half);
}

.viewDescription {
  composes: commonDescription;
}

.description {
  composes: commonDescription;
  transition: background-color 0.2s ease-in-out;
}

.description:hover {
  cursor: pointer;
  background-color: var(--color-lev-blue-300);
}

[data-cc-critical-type="NORMAL"] .description:hover {
  background-color: var(--color-critical-200);
}

[data-cc-critical-type="NORMAL"] .confirmedDescription:hover {
  background-color: var(--color-white);
}

.emptyDescription {
  color: var(--color-blue-gray-violet-dark);
}

.descriptionForm textarea,
.descriptionForm::after {
  gap: 0;
  /* 1px larger than padding to account for border */
  margin: -16px 0px -7px -17px;
  width: auto;
}

.confirmationButtons {
  display: flex;
  gap: 8px;
  margin-left: auto;
}

.action {
  padding: 4px 10px;
  margin-left: 8px;
  margin-top: -6px;
  margin-bottom: -6px;
  position: relative;
  z-index: 1;
  translate: 0 2px;
}

.closeAction {
  color: var(--color-green);
}

.reopenAction {
  color: var(--color-lev-blue-3);
}

.closedText {
  margin-left: 4px;
  color: var(--LEV-Blue-Gray-violet-dark, #8386b8);
  font-size: 12px;
  font-weight: 300;
}

.subHeading {
  display: flex;
  justify-content: space-between;
}

.updaterInfo {
  display: flex;
  gap: 4px;
  color: var(--color-neutral-700);
  align-items: end;
}

.updaterInfo svg {
  font-size: 16px;
}

.updatedByWrapper {
  display: flex;
  gap: 4px;
  align-items: center;
}

.updatedByName {
  max-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
