import { ReactNode } from "react"

import { Modal, ModalProps } from "../Modal/Modal"
import styles from "./Dialog.module.css"

export type DialogProps = {
  title: string
  actions?: ReactNode
  position?: "left" | "right"
  actionsClassName?: string
} & ModalProps

export const Dialog = ({
  children,
  actions,
  position = "right",
  actionsClassName = "",
  ...rest
}: DialogProps) => {
  const { contentClassName, ...restProps } = rest

  return (
    <Modal
      closeOnClickOutside={false}
      contentClassName={`${styles.content} ${contentClassName}`}
      data-testid="dialog"
      {...restProps}
    >
      {children}
      <div
        data-position={position}
        className={`${styles.actionsWrap} ${actionsClassName}`}
      >
        {actions}
      </div>
    </Modal>
  )
}
