import { Meta } from "@storybook/react-vite"
import React, { useState } from "react"

import { Button } from "../Button/Button"
import { Text } from "../typography/Text/Text"
import { Dialog } from "./Dialog"

export default {
  title: "Components/Dialog",
  component: Dialog,
  argTypes: { onClick: { action: "deleted" } },
} as Meta

const Template = (args) => {
  const [showDialog, setShowDialog] = useState(false)

  return (
    <>
      <Button onClick={() => setShowDialog(true)}>Open Dialog</Button>
      <Dialog
        {...args}
        isOpen={showDialog}
        title="Are you sure you want to save."
        onClose={() => setShowDialog(false)}
        actions={
          <>
            <Button
              variant={"clear"}
              onClick={() => {
                setShowDialog(false)
              }}
            >
              Save
            </Button>
            <Button
              variant={"filled"}
              onClick={() => {
                setShowDialog(false)
              }}
            >
              Cancel
            </Button>
          </>
        }
      >
        <Text>
          Lorem ipsum dolor sit amet consectetur adipisicing elit. Fugit
          voluptatibus, ipsum earum iure molestiae reiciendis nemo ducimus a aut
          est incidunt et perspiciatis unde corporis exercitationem temporibus!
          Beatae, inventore possimus.
        </Text>
      </Dialog>
    </>
  )
}
export const Default = Template.bind({})
