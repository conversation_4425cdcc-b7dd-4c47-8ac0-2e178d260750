import { ReactNode } from "react"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"
import { PiiSensitive } from "components/PiiSensitive/PiiSensitive"
import { Text } from "ui"

import styles from "./ContactInformation.module.css"

export const ContactInformation = ({ children }: { children: ReactNode }) => {
  return <dl className={styles.informationWrapper}>{children}</dl>
}

type ContactInformationItemProps = {
  icon?: IconName
  label: string
  children: ReactNode
}
export const ContactInformationItem = ({
  icon,
  label,
  children,
}: ContactInformationItemProps) => {
  return (
    <div data-testid={label}>
      <dt className={styles.informationItemLabel} aria-label={label}></dt>
      <Text as="dd" className={styles.informationItem}>
        {icon && <Icon name={icon} className={styles.icon} />}
        <PiiSensitive className={styles.piiSensitive}>{children}</PiiSensitive>
      </Text>
    </div>
  )
}
