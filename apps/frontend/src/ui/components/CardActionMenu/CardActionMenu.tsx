import c from "classnames"
import {
  differenceInDays,
  differenceInHours,
  differenceInMinutes,
  format,
} from "date-fns"
import { useTranslation } from "react-i18next"

import { MenuButton } from "components/Ariakit"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"
import { color } from "styles/colors"
import { Text } from "ui"

import { EncounterDisposition, EncounterStatus } from "generated/graphql"

import { ActionMenu } from "./ActionMenu"
import styles from "./CardActionMenu.module.css"

const statusColorMap = {
  [EncounterStatus.Planned]: color.lightBlue.light,
  [EncounterStatus.InProgress]: color.levBlue.dark,
  [EncounterStatus.CheckedOut]: color.levGreen.light,
  [EncounterStatus.Concluded]: color.success.dark,
  [EncounterStatus.Cancelled]: color.orange.light,
  [EncounterStatus.Deleted]: color.gray.light,
}

type CardActionMenuProps = {
  disposition: EncounterDisposition | null
  allowedTransitions: readonly EncounterStatus[]
  status: EncounterStatus
  fromDate: string
  toDate?: string | null
  updateEncounterStatus?: (status: EncounterStatus) => void
  updateEncounterDisposition?: (
    disposition: EncounterDisposition | null
  ) => void
  canDiscardEncounter?: boolean
}

export const CardActionMenu = ({
  fromDate,
  toDate,
  disposition,
  allowedTransitions,
  status,
  updateEncounterStatus,
  updateEncounterDisposition,
  canDiscardEncounter,
}: CardActionMenuProps) => {
  const menu = useMenuStore({
    placement: "bottom-start",
  })

  const isOpen = menu.useState().open

  const { t: tEnum } = useTranslation("enums")

  const toDateObj = toDate ? new Date(toDate) : null
  const fromDateObj = fromDate ? new Date(fromDate) : null

  const diffDays = fromDateObj ? differenceInDays(new Date(), fromDateObj) : 0
  const diffHours = fromDateObj ? differenceInHours(new Date(), fromDateObj) : 0
  const diffMinutes = fromDateObj
    ? differenceInMinutes(new Date(), fromDateObj)
    : 0

  let date = ""

  if (status === EncounterStatus.InProgress) {
    if (diffDays >= 1) {
      date = `${diffDays}d`
    } else if (diffHours >= 1) {
      date = `${diffHours}h`
    } else {
      date = `${diffMinutes}m`
    }
  } else if (
    fromDateObj &&
    (status === EncounterStatus.Planned || status === EncounterStatus.Cancelled)
  ) {
    date = format(fromDateObj, "d MMM")
  } else if (toDateObj !== null) {
    date = format(toDateObj, "d MMM")
  }

  const hideArrowIcon =
    status === EncounterStatus.Concluded ||
    status === EncounterStatus.Cancelled ||
    allowedTransitions.length === 0

  return (
    <div className={styles.wrap}>
      <MenuButton
        store={menu}
        className={c(styles.stateStatus, statusColorMap[status], {
          [styles.disabled]: hideArrowIcon,
        })}
        data-testid="encounter-status-transition-button"
      >
        {status !== EncounterStatus.Deleted
          ? tEnum(`EncounterStatus_state.${status}`)
          : ""}

        <span className={styles.stateStatusDate}>{date}</span>
        {!hideArrowIcon && (
          <Icon
            className={styles.arrowIcon}
            name={isOpen ? "arrow-up-s-line" : "arrow-down-s-line"}
          />
        )}
      </MenuButton>

      {disposition !== null && status === EncounterStatus.InProgress && (
        <Text className={styles.stateSetValue}>
          {tEnum(`EncounterDisposition.${disposition}`)}
        </Text>
      )}

      <ActionMenu
        allowedTransitions={allowedTransitions}
        updateEncounterStatus={updateEncounterStatus}
        updateEncounterDisposition={updateEncounterDisposition}
        disposition={disposition}
        menu={menu}
        status={status}
        canDiscardEncounter={canDiscardEncounter}
      />
    </div>
  )
}
