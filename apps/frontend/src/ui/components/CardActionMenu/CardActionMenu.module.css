.wrap {
  display: flex;
  flex-wrap: nowrap;
}

.stateStatus {
  border-radius: var(--radius-button-half) 0px;
  padding: 8px 16px;
  border: none;
  position: relative;
  white-space: nowrap;
}

.stateStatusDate {
  font-size: 12px;
  font-weight: 300;
  margin-left: 4px;
}

.disabled {
  cursor: default;
  pointer-events: none;
}

.arrowIcon {
  width: 18px;
  height: 18px;
  margin: 0px;
  margin-left: 12px;
}

.stateSetValue {
  background: var(--color-lev-blue-300);
  color: var(--color-lev-blue-500);
  padding: 8px 9px;
  margin-left: -8px;
  padding: 8px 12px;
  margin-left: -20px;
  border-radius: var(--radius-button-half) 0px;
  white-space: nowrap;
  padding-left: 28px;
}

.disposition {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
}

.checkCircleIcon {
  width: 24px;
  height: 24px;

  color: var(--color-lev-blue);
}

.menuItem[data-action="CANCELLED"],
.menuItem[data-action="DELETED"] {
  color: var(--color-critical);
}

.actionMenu,
.subMenu {
  z-index: var(--z-index-modal);
}
