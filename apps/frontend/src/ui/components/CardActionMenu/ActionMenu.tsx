import { useTranslation } from "react-i18next"

import { Menu } from "components/Ariakit"
import { MenuItem } from "components/Ariakit/Menu/MenuItem/MenuItem"
import { SubMenu } from "components/Ariakit/SubMenu/SubMenu"
import { useMenuStore } from "components/Ariakit/hooks"
import Icon from "components/Icon/Icon"

import { EncounterDisposition, EncounterStatus } from "generated/graphql"

import styles from "./CardActionMenu.module.css"

type MenuProps = {
  allowedTransitions: readonly EncounterStatus[]
  disposition: EncounterDisposition | null
  updateEncounterStatus?: (status: EncounterStatus) => void
  updateEncounterDisposition?: (
    disposition: EncounterDisposition | null
  ) => void
  menu: ReturnType<typeof useMenuStore>
  status: EncounterStatus
  canDiscardEncounter?: boolean
}

export const ActionMenu = ({
  menu,
  status,
  disposition,
  allowedTransitions,
  updateEncounterDisposition,
  updateEncounterStatus,
  canDiscardEncounter = false,
}: MenuProps) => {
  const { t: tEnum } = useTranslation("enums")

  const prioritizedActionKeys = [
    EncounterStatus.InProgress,
    EncounterStatus.Planned,
    EncounterStatus.CheckedOut,
    EncounterStatus.Concluded,
    EncounterStatus.Cancelled,
    EncounterStatus.Deleted,
  ]

  const prioritizedDispositionKeys = [
    EncounterDisposition.Observation,
    // HomeLeave not currently in use
    // EncounterDisposition.HomeLeave,
    EncounterDisposition.Discharge,
    EncounterDisposition.Transfer,
    EncounterDisposition.TransferExternal,
  ]

  if (allowedTransitions.length === 0) return null

  const allowedActionKeys = prioritizedActionKeys
    .filter((key) => allowedTransitions.includes(key))
    .filter((key) => key !== EncounterStatus.Deleted || canDiscardEncounter)
    .filter((key) => key !== EncounterStatus.Cancelled || !canDiscardEncounter)

  const firstMenuItems = allowedActionKeys.length
    ? allowedActionKeys.slice(0, -1)
    : allowedActionKeys
  const lastMenuItem = allowedActionKeys.length
    ? allowedActionKeys[allowedActionKeys.length - 1]
    : null

  const showDispositionMenu = status === EncounterStatus.InProgress

  return (
    <Menu
      className={styles.actionMenu}
      gutter={4}
      store={menu}
      hasSubmenu
      portal
      data-testid="card-action-menu"
    >
      {firstMenuItems.map((actionKey) => (
        <MenuItem
          key={actionKey}
          data-action={actionKey}
          className={styles.menuItem}
          onClick={(e) => {
            e.stopPropagation()
            updateEncounterStatus?.(actionKey)
          }}
        >
          {tEnum(`EncounterStatus_action.${actionKey}`)}
        </MenuItem>
      ))}
      {showDispositionMenu && (
        <SubMenu label="Set" className={styles.subMenu}>
          <MenuItem
            onClick={(e) => {
              e.stopPropagation()
              updateEncounterDisposition?.(null)
            }}
            className={styles.disposition}
          >
            {tEnum("EncounterDisposition.NULL")}
            {disposition === null && (
              <Icon
                className={styles.checkCircleIcon}
                name={"checkbox-circle-line"}
              />
            )}
          </MenuItem>
          {prioritizedDispositionKeys.map((dispositionKey) => {
            const isSelected = dispositionKey === disposition

            return (
              <MenuItem
                key={dispositionKey}
                onClick={(e) => {
                  e.stopPropagation()
                  updateEncounterDisposition?.(dispositionKey)
                }}
                className={styles.disposition}
              >
                {tEnum(`EncounterDisposition.${dispositionKey}`)}

                {isSelected && (
                  <Icon
                    className={styles.checkCircleIcon}
                    name={"checkbox-circle-line"}
                  />
                )}
              </MenuItem>
            )
          })}
        </SubMenu>
      )}
      {lastMenuItem && (
        <MenuItem
          data-action={lastMenuItem}
          className={styles.menuItem}
          onClick={(e) => {
            e.stopPropagation()
            updateEncounterStatus?.(lastMenuItem)
          }}
        >
          {tEnum(`EncounterStatus_action.${lastMenuItem}`)}
        </MenuItem>
      )}
    </Menu>
  )
}
