import React, { useState } from "react"

import {
  EncounterDisposition,
  EncounterStatus,
} from "../../../generated/graphql"
import { CardActionMenu } from "./CardActionMenu"

export default {
  title: "Components/CardActionMenu",
  component: CardActionMenu,
}

export const CardActionMenuExample = () => {
  const [status, setStatus] = useState<EncounterStatus>(EncounterStatus.Planned)
  const [disposition, setDisposition] = useState<EncounterDisposition | null>(
    null
  )

  return (
    <div style={{ display: "flex" }}>
      <CardActionMenu
        disposition={disposition}
        updateEncounterDisposition={setDisposition}
        updateEncounterStatus={setStatus}
        allowedTransitions={Object.values(EncounterStatus)}
        status={status}
        fromDate="2021-09-01T00:00:00Z"
      />
    </div>
  )
}
