import c from "classnames"
import { HTMLAttributes } from "react"

import styles from "./CountCircled.module.css"

export type CountCircledProps = HTMLAttributes<HTMLSpanElement> & {
  size?: "small" | "medium"
}

export default function CountCircled({
  className = "",
  size = "medium",
  ...rest
}: CountCircledProps) {
  return (
    <span className={c(styles.wrap, className)} data-size={size} {...rest} />
  )
}
