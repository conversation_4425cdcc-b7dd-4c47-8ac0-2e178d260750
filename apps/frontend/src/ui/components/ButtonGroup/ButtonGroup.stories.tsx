import { StoryFn, Meta } from "@storybook/react-vite"

import Icon from "components/Icon/Icon"

import Button from "../Button/Button"
import ButtonGroup, { ButtonGroupProps } from "./ButtonGroup"

export default {
  title: "Components/ButtonGroup",
  component: ButtonGroup,
} as Meta

const Template: StoryFn<ButtonGroupProps<"button">> = (args) => {
  return (
    <ButtonGroup {...args}>
      <Button>Btn1 with larger text</Button>
      <Button>Btn2</Button>
      <Button>Btn3</Button>
      <Button variant="filled">Btn4</Button>
      <Button icon={<Icon name="delete-bin-line" />}>BtnIcon</Button>
    </ButtonGroup>
  )
}

export const ButtonGroupVertical = Template.bind({})
