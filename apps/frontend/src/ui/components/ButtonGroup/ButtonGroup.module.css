@layer components {
  /* Main buttonGroup style */
  .buttonGroup {
    display: flex;
    min-width: max-content;
  }

  .buttonGroup > * {
    position: relative;
    max-width: 100%;
  }
  /* Styles that apply on vertical direction */
  .buttonGroup[data-direction="horizontal"] {
    flex-direction: row;
  }

  .buttonGroup[data-direction="horizontal"] > *:not(:last-child) {
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .buttonGroup[data-direction="horizontal"] > *:is(:hover, :focus) {
    z-index: 1;
  }

  .buttonGroup[data-direction="horizontal"] > *:not(:first-child) {
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
    margin-left: -1px; /* border width */
  }

  /* Styles that apply on horizontal direction */
  .buttonGroup[data-direction="vertical"] {
    flex-direction: column;
  }

  .buttonGroup[data-direction="vertical"] > *:not(:last-child) {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
  }

  .buttonGroup[data-direction="vertical"] > *:is(:hover, :focus) {
    z-index: 1;
  }

  .buttonGroup[data-direction="vertical"] > *:not(:first-child) {
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
    margin-top: -1px; /* border width */
  }
}
