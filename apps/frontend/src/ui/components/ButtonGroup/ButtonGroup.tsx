import c from "classnames"
import { createContext, useContext, forwardRef } from "react"

import { ButtonOwnProps } from "ui/components/Button/Button"

import styles from "./ButtonGroup.module.css"

type ButtonGroupOwnProps = {
  direction?: "vertical" | "horizontal"
} & Pick<ButtonOwnProps, "size" | "variant">

const ButtonGroupContext = createContext<
  Pick<ButtonOwnProps, "size" | "variant">
>({
  size: "default",
  variant: "outline",
})

export const useButtonGroup = () => useContext(ButtonGroupContext)

export type ButtonGroupProps = React.HTMLAttributes<HTMLDivElement> &
  ButtonGroupOwnProps

export const ButtonGroup = forwardRef<HTMLDivElement, ButtonGroupProps>(
  (
    {
      children,
      size,
      variant,
      direction = "horizontal",
      className = "",
      ...rest
    },
    ref
  ) => {
    return (
      <ButtonGroupContext.Provider
        value={{
          size,
          variant,
        }}
      >
        <div
          className={c(styles.buttonGroup, className)}
          data-direction={direction}
          {...rest}
          ref={ref}
        >
          {children}
        </div>
      </ButtonGroupContext.Provider>
    )
  }
)

export default ButtonGroup
