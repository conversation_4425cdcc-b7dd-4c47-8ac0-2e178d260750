import c from "classnames"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>act<PERSON><PERSON>, useRef, useState } from "react"
import { useTranslation } from "react-i18next"

import { useGlobalState } from "components/GlobalDataContext/GlobalData.context"
import Icon from "components/Icon/Icon"
import AddResponsibleProviders from "features/subject-journal/components/AddResponsibleProviders/AddResponsibleProviders"
import JournalBlockLink from "features/subject-journal/components/JournalBlockLink/JournalBlockLink"
import { Button, ButtonText, Heading, Tag, Text } from "ui"

import {
  ProviderInfoFragmentFragment,
  useUpdateEncounterMutation,
} from "generated/graphql"

import { EditableNote } from "../EditableNote/EditableNote"
import { EncounterPriority } from "../EncounterPriority/EncounterPriority"
import styles from "./EncounterCard.module.css"

type EncounterCardProps = {
  actionMenu: ReactNode
  codingLabel?: string
  entriesLength?: number
  id: string
  note: string
  priority?: number
  reason: string
  responsibleProviders: ProviderInfoFragmentFragment[]
  subjectId: string
  teamMembers: ProviderInfoFragmentFragment[]
  teamName: string

  onAddResponsibleProvider: (providerId: string) => void
  onDeleteResponsibleProvider: (providerId: string) => void
}

export const EncounterCard = ({
  actionMenu,
  codingLabel,
  entriesLength,
  id,
  note,
  priority,
  reason,
  responsibleProviders,
  subjectId,
  teamMembers,
  teamName,
  onAddResponsibleProvider,
  onDeleteResponsibleProvider,
}: EncounterCardProps) => {
  const [showNoteForm, setShowNoteForm] = useState(false)
  const [showPriorityForm, setShowPriorityForm] = useState(false)
  const [showAddProviders, setShowAddProviders] = useState(false)
  const linkRef = useRef<HTMLAnchorElement>(null)
  const [updateEncounter] = useUpdateEncounterMutation()

  const { globalData } = useGlobalState()
  const actorId = globalData.actor.id

  const { t } = useTranslation()

  const { t: tProviders } = useTranslation("routes", {
    keyPrefix: "addResponsibleProviders",
  })

  const handleCardClick: MouseEventHandler<HTMLDivElement> = (e) => {
    if (e.target instanceof HTMLButtonElement) return
    if (e.target instanceof HTMLElement || e.target instanceof SVGElement) {
      const hasButton = e.target.closest("button")
      if (hasButton) return
      if (e.target.role === "menuItem") return
      if (e.target.closest('[role="menuItem]')) return
    }
    linkRef.current?.click()
  }

  const handleNoteSubmit = (value: string) => {
    updateEncounter({
      variables: {
        id,
        input: {
          note: value,
        },
      },
    })
    setShowNoteForm(false)
  }

  const providers = responsibleProviders.map((rp) => rp.name).join(", ")
  const teamAndProviders = `${teamName}${providers ? `: ${providers}` : ""}`

  const responsibleProviderIds = responsibleProviders.map((rp) => rp.id)

  const showAddMe = !responsibleProviderIds.includes(actorId)

  const hasEntires = entriesLength !== undefined && entriesLength > 0

  return (
    <div
      className={c(styles.wrap, {
        [styles.addProvidersWrap]: showAddProviders,
      })}
      onClick={handleCardClick}
    >
      <div className={styles.header}>
        {actionMenu}
        {hasEntires && (
          <Tag size="small" className={styles.entries}>
            {entriesLength === 1 ? "1 entry" : `${entriesLength} entries`}
          </Tag>
        )}

        <div className={styles.providersWrap}>
          <ButtonText
            className={styles.providers}
            size="small"
            as="button"
            onClick={() => setShowAddProviders(!showAddProviders)}
            title={teamAndProviders}
          >
            {teamAndProviders}
          </ButtonText>
          {showAddMe && (
            <Button
              className={styles.addMeButton}
              onClick={() => onAddResponsibleProvider(actorId)}
              variant="clear"
              size="small"
            >
              {t(tProviders("addMe"))}
            </Button>
          )}
        </div>
      </div>
      {showAddProviders ? (
        <AddResponsibleProviders
          members={teamMembers}
          responsibleProviders={responsibleProviderIds}
          onRequestClose={() => setShowAddProviders(false)}
          onAddResponsibleProvider={onAddResponsibleProvider}
          onDeleteResponsibleProvider={onDeleteResponsibleProvider}
        />
      ) : (
        <>
          <div className={styles.entryContent}>
            <EncounterPriority
              id={id}
              priority={priority}
              showForm={showPriorityForm}
              setShowForm={setShowPriorityForm}
              size="small"
            />
            <Heading>{reason}</Heading>
            {codingLabel && (
              <>
                <div className={styles.divider} />
                <Text className={styles.diagnosis}>{codingLabel}</Text>
              </>
            )}
          </div>
          <EditableNote
            className={styles.note}
            note={note}
            handleNoteSubmit={handleNoteSubmit}
            showForm={showNoteForm}
            setShowForm={setShowNoteForm}
          />
          {!showNoteForm && (
            <Button
              as={JournalBlockLink}
              encounterId={id}
              ref={linkRef}
              className={styles.openJournalButton}
              variant="clear"
              iconEnd={<Icon name="arrow-right-s-line" />}
              subjectId={subjectId}
              sectionId={null}
            >
              {t("Open journal")}
            </Button>
          )}
        </>
      )}
    </div>
  )
}
