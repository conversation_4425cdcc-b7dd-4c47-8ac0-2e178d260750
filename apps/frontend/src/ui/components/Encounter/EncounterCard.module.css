.wrap {
  --card-gap: 16px;
  border-radius: var(--radius-button-half);
  background-color: var(--color-white);

  /* Move box shadow to variables */
  box-shadow: 0px 0px 10px 0px rgba(198, 201, 219, 0.3);

  display: grid;
  grid-template-columns: 1fr auto;
  gap: var(--card-gap);
  position: relative;
  padding: var(--card-gap);
  transition: background-color 0.1s ease-in-out;
}

.wrap:not(.addProvidersWrap):hover:not(
    :has(:is(.note:hover, .providersWrap:hover))
  ):has(.openJournalButton) {
  background-color: var(--color-background-hover);
  cursor: pointer;
}
.wrap:not(.addProvidersWrap):active:not(
    :has(:is(.note:hover, .providersWrap:hover))
  ):has(.openJournalButton) {
  background-color: var(--color-background-active);
}

.addProvidersWrap {
  grid-template-columns: 1fr;
}

.entryContent {
  grid-column: 1 / -1;
  grid-row: 2;
  display: flex;
  align-items: center;
  gap: 12px;
}

.header {
  grid-column: 1 / -1;
  margin: calc(-1 * var(--card-gap)) calc(-1 * var(--card-gap) + 7px) 0
    calc(-1 * var(--card-gap));
  display: grid;
  grid-template-columns: max-content max-content 1fr;
  gap: 12px;
  align-items: start;
  justify-content: flex-start;
}

.entries {
  margin-top: 7px;
}

.header:not(:has(.entries)) {
  grid-template-columns: max-content 1fr;
}

.providersWrap {
  width: 100%;
  border-radius: var(--radius-button-half);
  cursor: pointer;
  position: relative;
  margin-top: 7px;
}
.providersWrap:hover {
  background: var(--color-background-hover);
}

.entries + .providersWrap {
  max-width: min(500px, 100%);
}

.providersWrap:hover {
  cursor: pointer;
}

.addMeButton {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  right: 0;
  opacity: 0;
  background: var(--color-background-subtle);
}

.providersWrap:hover .addMeButton {
  opacity: 1;
}

.addMeButton:hover,
.addMeButton:active {
  background: var(--color-background-subtle-hover);
}

.providers {
  width: 100%;
  display: block;
  margin-left: -4px;
  justify-self: start;
  padding: 4px 10px;
  cursor: pointer;
  border-radius: var(--radius-button-half);
}

.providersWrap:hover .providers:has(+ .addMeButton) {
  padding-right: 108px;
}

.divider {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: var(--color-text);
}

.diagnosis {
  text-overflow: ellipsis;
  display: flex;
  align-items: center;
}

.openJournalButton.openJournalButton {
  border-radius: var(--radius-button-half) 0px;
  margin: 0 calc(-1 * var(--card-gap)) calc(-1 * var(--card-gap)) 0;
  align-self: end;
}

.wrap:hover:not(:has(.note:hover)) .openJournalButton.openJournalButton {
  border-radius: var(--radius-button-half) 0px;
  border: 1px solid var(--color-lev-blue);
  background: var(--color-lev-blue);
  color: var(--color-white);
}
