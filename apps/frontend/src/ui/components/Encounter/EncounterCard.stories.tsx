import React, { useState } from "react"

import {
  EncounterDisposition,
  EncounterStatus,
  ProviderInfoFragmentFragment,
  ProviderSpecialty,
} from "../../../generated/graphql"
import { CardActionMenu } from "../CardActionMenu/CardActionMenu"
import { EncounterCard } from "./EncounterCard"

export default {
  title: "Components/EncounterCard",
  component: EncounterCard,
}

const team: ProviderInfoFragmentFragment[] = [
  {
    __typename: "Provider" as const,
    id: "1",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    nameInitials: "",
    specialty: ProviderSpecialty.AttendingPhysician,
    phoneNumber: "",
  },
  {
    __typename: "Provider" as const,
    id: "2",
    name: "<PERSON><PERSON><PERSON>r",
    nameInitials: "HG",
    specialty: ProviderSpecialty.AttendingPhysician,
    phoneNumber: "",
  },
  {
    __typename: "Provider" as const,
    id: "3",
    name: "<PERSON>",
    nameInitials: "RW",
    specialty: ProviderSpecialty.AttendingPhysician,
    phoneNumber: "",
  },
]

export const EncounterCardExample = () => {
  const [status, setStatus] = useState<EncounterStatus>(EncounterStatus.Planned)
  const [disposition, setDisposition] = useState<EncounterDisposition | null>(
    null
  )

  return (
    <>
      <EncounterCard
        id="1"
        priority={1}
        reason="Sore throat, fever"
        codingLabel="M48.40X Fatigue fracture of vertebra, site"
        teamName="ED/ED-team"
        responsibleProviders={team}
        teamMembers={team}
        entriesLength={2}
        note={"spouse asks to be informed (see tel)"}
        actionMenu={
          <CardActionMenu
            fromDate="2024-09-01T08:00:00Z"
            toDate="2024-09-02T09:00:00Z"
            disposition={disposition}
            allowedTransitions={Object.values(EncounterStatus)}
            status={status}
            updateEncounterStatus={setStatus}
            updateEncounterDisposition={setDisposition}
          />
        }
        to=""
        onAddResponsibleProvider={console.log}
        onDeleteResponsibleProvider={console.log}
      />
    </>
  )
}
