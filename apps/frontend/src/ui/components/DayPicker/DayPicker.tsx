import c from "classnames"
import { addMonths, addYears, subMonths, subYears } from "date-fns"
import { useState } from "react"

import styles from "./DayPicker.module.css"
import { Days } from "./components/Days/Days"
import { Header } from "./components/Header/Header"

export type DayPickerProps = {
  onDateSelect: (date: Date) => void
  footer?: React.ReactNode
  selectedDate?: Date
  className?: string
  minDate?: Date
  maxDate?: Date
}

export const DayPicker = ({
  onDateSelect,
  footer,
  selectedDate: initialSelectedDate,
  className,
  minDate,
  maxDate,
}: DayPickerProps) => {
  const selectedDate = initialSelectedDate ? initialSelectedDate : new Date()

  const [currentMonth, setCurrentMonth] = useState(selectedDate)

  return (
    <div className={c(className, styles.dayPicker)}>
      <Header
        onPrevMonth={() => setCurrentMonth(subMonths(currentMonth, 1))}
        onNextMonth={() => setCurrentMonth(addMonths(currentMonth, 1))}
        onPrevYear={() => setCurrentMonth(subYears(currentMonth, 1))}
        onNextYear={() => setCurrentMonth(addYears(currentMonth, 1))}
        currentMonth={currentMonth}
      />
      <Days
        currentMonth={currentMonth}
        selectedDate={selectedDate}
        onDateSelect={(date) => {
          onDateSelect(date)
        }}
        minDate={minDate}
        maxDate={maxDate}
      />

      {footer && <>{footer}</>}
    </div>
  )
}
