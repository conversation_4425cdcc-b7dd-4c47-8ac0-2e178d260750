import c from "classnames"
import {
  addDays,
  format,
  getDay,
  isSameDay,
  isSameMonth,
  startOfMonth,
  startOfWeek,
  sub,
  startOfDay,
  endOfDay,
} from "date-fns"
import { nanoid } from "nanoid"

import { Button, Text } from "ui"

import styles from "./Days.module.css"

type DaysProps = {
  currentMonth: Date
  onDateSelect: (date: Date) => void
  selectedDate: Date
  minDate?: Date
  maxDate?: Date
}

type CalendarDayType = {
  date: Date
  isToday: boolean
  isCurrentMonth: boolean
  isSelectedDay: boolean
  isDisabled: boolean
}

export const Days = ({
  currentMonth,
  onDateSelect,
  selectedDate,
  minDate,
  maxDate,
}: DaysProps) => {
  const weekTitles = []
  const startDate = startOfWeek(currentMonth, { weekStartsOn: 1 })
  for (let i = 0; i < 7; i++) {
    weekTitles.push(format(addDays(startDate, i), "EEEEE"))
  }

  const weeks: CalendarDayType[][] = Array.from({ length: 7 }, () => [])

  const startOfMonthDate = startOfMonth(currentMonth)
  const dayOfWeek = (getDay(startOfMonthDate) + 6) % 7
  const previousMonday = sub(startOfMonthDate, { days: dayOfWeek })

  for (let i = 0; i < 6; i++) {
    weeks[i] = Array.from({ length: 7 }, (_, j) => {
      const date = addDays(previousMonday, i * 7 + j)
      const isToday = isSameDay(date, new Date())
      const isCurrentMonth = isSameMonth(date, startOfMonthDate)
      const isSelectedDay = isSameDay(date, selectedDate)
      const isDisabled = !!(
        (minDate && date < startOfDay(minDate)) ||
        (maxDate && date > endOfDay(maxDate))
      )

      return { date, isToday, isCurrentMonth, isSelectedDay, isDisabled }
    })
  }

  return (
    <table className={styles.table}>
      <thead>
        <tr>
          {weekTitles.map((title) => (
            <th key={nanoid()}>
              <Text className={styles.tableRow}>{title}</Text>
            </th>
          ))}
        </tr>
      </thead>

      <tbody>
        {weeks.map((days) => (
          <tr key={nanoid()}>
            {days.map((day) => (
              <td key={nanoid()}>
                <Button
                  variant="clear"
                  key={nanoid()}
                  onClick={() => !day.isDisabled && onDateSelect(day.date)}
                  aria-current={day.isToday ? "date" : undefined}
                  data-selected={day.isSelectedDay}
                  disabled={day.isDisabled}
                  className={c({
                    [styles.tableRow]: true,
                    [styles.day]: true,
                    [styles.notCurrentMonth]: !day.isCurrentMonth,
                  })}
                >
                  {format(day.date, "d")}
                </Button>
              </td>
            ))}
          </tr>
        ))}
      </tbody>
    </table>
  )
}
