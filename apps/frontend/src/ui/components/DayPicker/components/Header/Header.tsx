import { format } from "date-fns"

import Icon from "components/Icon/Icon"
import { Button, Label } from "ui"

import styles from "./Header.module.css"

type Props = {
  onPrevMonth: () => void
  onPrevYear: () => void
  onNextMonth: () => void
  onNextYear: () => void
  currentMonth: Date
}

export const Header = ({
  onPrevMonth,
  onNextMonth,
  onPrevYear,
  onNextYear,
  currentMonth,
}: Props) => {
  return (
    <div className={styles.wrap}>
      <Label className={styles.monthTitle}>
        {format(currentMonth, "MMMM yyyy")}
      </Label>

      <div className={styles.icons}>
        <Button
          variant="clear"
          onClick={onPrevYear}
          aria-label="Previous year"
          icon={<Icon name={"arrow-left-double-line"} />}
        />
        <Button
          variant="clear"
          onClick={onPrevMonth}
          aria-label="Previous month"
          icon={<Icon name={"arrow-left-s-line"} />}
        />
        <Button
          variant="clear"
          onClick={onNextMonth}
          aria-label="Next month"
          icon={<Icon name={"arrow-right-s-line"} />}
        />
        <Button
          variant="clear"
          onClick={onNextYear}
          aria-label="Next year"
          icon={<Icon name={"arrow-right-double-line"} />}
        />
      </div>
    </div>
  )
}
