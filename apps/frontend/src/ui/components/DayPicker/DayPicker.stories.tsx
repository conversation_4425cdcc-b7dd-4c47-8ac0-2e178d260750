import { Meta, StoryFn } from "@storybook/react-vite"

import { DayPicker, DayPickerProps } from "./DayPicker"

export default {
  title: "Form Components/DayPicker",
  component: DayPicker,
} as Meta

const Template: StoryFn<DayPickerProps> = (args) => {
  return <DayPicker {...args} onDateSelect={(day) => window.alert(day)} />
}

export const DayPickerDefault = Template.bind({})

DayPickerDefault.parameters = {
  layout: "centered",
}
