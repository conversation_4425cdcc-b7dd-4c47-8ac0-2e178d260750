import { Story, <PERSON>a } from "@storybook/react-vite"
import React, { useState } from "react"

import { Checkbox, CheckboxProps } from "./Checkbox"

export default {
  title: "Form Components/Checkbox",
  component: Checkbox,
} as Meta

const Template: Story<CheckboxProps<"div">> = (args) => {
  const [checked, setChecked] = useState<boolean>(false)

  return (
    <Checkbox
      {...args}
      label="Checkbox label"
      checked={checked}
      onChange={({ currentTarget: { checked } }) => setChecked(checked)}
    />
  )
}

export const CheckboxDefault = Template.bind({})
CheckboxDefault.args = {
  title: "Test",
}
CheckboxDefault.parameters = {
  layout: "centered",
}
