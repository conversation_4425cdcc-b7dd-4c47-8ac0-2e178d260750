import { InputHTMLAttributes, ReactNode, useId } from "react"

import { Text } from "ui"

import styles from "./Checkbox.module.css"

export type CheckboxSize = "default" | "large" | "small"

type NativeCheckboxProps = "checked" | "onChange" | "id" | "className"

export type CheckboxProps = {
  label: ReactNode
  size?: CheckboxSize
  disabled?: boolean
  name?: string
  hideLabel?: boolean
} & Pick<InputHTMLAttributes<HTMLInputElement>, NativeCheckboxProps>

export const Checkbox = ({
  id,
  className = "",
  disabled = false,
  label,
  onChange,
  size = "default",
  checked,
  name,
  hideLabel = false,
  ...rest
}: CheckboxProps) => {
  const generatedId = useId()
  return (
    <Text
      className={`${styles.wrapper} ${className}`}
      data-size={size}
      data-disabled={disabled}
      as="label"
      visuallyHidden={hideLabel}
      htmlFor={id || generatedId}
      onClick={(e: React.MouseEvent) => e.stopPropagation()}
      {...rest}
    >
      <input
        id={id || generatedId}
        className={styles.checkbox}
        type="checkbox"
        name={name}
        checked={checked}
        onChange={onChange}
        disabled={disabled}
      />
      <span className={styles.checkmark}></span>
      {label}
    </Text>
  )
}

export default Checkbox
