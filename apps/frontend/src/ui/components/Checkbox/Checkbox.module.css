.wrapper {
  align-items: center;
  cursor: pointer;
  display: flex;
  position: relative;
  padding-left: 28px;
  margin-bottom: 12px;
}

.wrapper[data-disabled="true"] {
  cursor: default;
  pointer-events: none;
  color: var(--color-gray-700);
}

.checkbox {
  appearance: none;
  position: absolute;
}

.checkmark {
  position: absolute;
  left: 0;
  height: 20px;
  width: 20px;
  background-color: var(--color-background-subtle);
  border-radius: 4px;
  border: 1px solid var(--color-border);

  transition:
    background-color 0.1s ease-in-out,
    box-shadow 0.1s ease-in-out,
    border-color 0.1s ease-in-out;
}

/* Create the checkmark/indicator (hidden when not checked) */
.checkmark:after {
  content: "";
  position: absolute;
  display: none;
}

/* On hover, add a background */
.wrapper:hover input ~ .checkmark {
  background-color: var(--color-background-subtle-hover);
  box-shadow: 0px 0px 20px 0px rgba(13, 16, 57, 0.2);
}
/* On active, add a background */
.wrapper input:active ~ .checkmark {
  background-color: var(--color-background-subtle-active);
}

/* When the checkbox is checked, add a background */
.wrapper input:checked ~ .checkmark {
  background-color: var(--color-background-inverted);
}
.wrapper:hover input:checked ~ .checkmark {
  background-color: var(--color-background-inverted-hover);
}
.wrapper:active input:checked ~ .checkmark {
  background-color: var(--color-background-inverted-active);
}

/* When the checkbox is disabled, add other border color */
.wrapper input:disabled ~ .checkmark {
  border-color: var(--color-gray-300);
  background: var(--color-gray-200);
  cursor: not-allowed;
}

/* Show the checkmark when checked */
.wrapper input:checked ~ .checkmark:after {
  display: block;
}

/* Style the checkmark/indicator */
.wrapper .checkmark:after {
  left: 7px;
  top: 3px;
  width: 5px;
  height: 10px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}
