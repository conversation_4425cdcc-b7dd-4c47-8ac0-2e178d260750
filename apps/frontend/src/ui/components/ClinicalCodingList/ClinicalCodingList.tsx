import { useState } from "react"
import { useTranslation } from "react-i18next"

import JournalBlockLink from "features/subject-journal/components/JournalBlockLink/JournalBlockLink"
import { Button } from "ui"

import {
  ClinicalCodingCriticalType,
  useClinicalCodingListQuery,
} from "generated/graphql"

import ClinicalCodingCard from "../ClinicalCodingCard/ClinicalCodingCard"
import styles from "./ClinicalCodingList.module.css"

type ClinicalCodingListProps = {
  subjectId: string
  onClick?: () => void
}

export default function ClinicalCodingList({
  subjectId,
  onClick,
}: ClinicalCodingListProps) {
  const { data } = useClinicalCodingListQuery({
    variables: { id: subjectId },
  })

  const [tRoutes] = useTranslation("routes", { keyPrefix: "subjectSummary" })

  const [showClosed, setShowClosed] = useState(false)

  if (!data) return null

  // sort by CriticalType High > Normal > null - and finally by closedAt if showClosed is true
  const sortedCCs = [...data.subject.clinicalCodings]
    .filter(({ closedAt }) => (showClosed ? true : !closedAt))
    .sort((a, b) => {
      if (a.closedAt && !b.closedAt) return 1
      if (!a.closedAt && b.closedAt) return -1

      // If both are either closed or not closed, sort by criticalType
      if (a.criticalType === b.criticalType) return 0
      if (a.criticalType && a.criticalType === ClinicalCodingCriticalType.High)
        return 1
      if (
        b.criticalType &&
        b.criticalType === ClinicalCodingCriticalType.Normal
      )
        return -1

      return 0
    })

  const hasClosedItems = data.subject.clinicalCodings.some(
    ({ closedAt }) => !!closedAt
  )

  return (
    <div className={styles.wrap}>
      <ul>
        {sortedCCs.map((clinicalCoding) => {
          const journalEntry =
            clinicalCoding.entry.__typename === "JournalEntry"
              ? clinicalCoding.entry
              : null
          const sections = journalEntry?.sections || []
          const { id } = clinicalCoding
          const lastSectionId = sections.length
            ? sections[sections.length - 1].id
            : null

          return (
            <li key={id}>
              <JournalBlockLink
                encounterId={null}
                sectionId={lastSectionId}
                subjectId={subjectId}
                onClick={onClick}
              >
                <ClinicalCodingCard
                  {...clinicalCoding}
                  isExpanded
                  resizable={false}
                  showDescription
                  editableDescription={false}
                />
              </JournalBlockLink>
            </li>
          )
        })}
      </ul>

      {/* COMEBACK remove when we have proper SJ filters where all codes are listed  */}
      {hasClosedItems && (
        <Button
          onClick={() => setShowClosed(!showClosed)}
          variant="clear"
          className={styles.showClosedCodesButton}
        >
          {showClosed ? tRoutes("hideClosedCodes") : tRoutes("showClosedCodes")}
        </Button>
      )}
    </div>
  )
}
