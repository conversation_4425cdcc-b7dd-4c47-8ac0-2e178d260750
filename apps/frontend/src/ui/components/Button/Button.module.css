@layer components {
  .button {
    composes: default from "../typography/ButtonText/ButtonText.module.css";

    border-radius: var(--radius-button);
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition:
      color 100ms,
      background-color 100ms,
      border 100ms,
      box-shadow 100ms;

    border: 1px solid var(--color-button-border);
    background-color: var(--color-button-background);
    color: var(--color-button-text-interactive);
    cursor: pointer;
  }

  .block {
    width: 100%;
  }

  /* Icons */

  .icon,
  .iconEnd {
    display: block;
    height: 1em;
    width: 1em;
    line-height: 1;
  }

  .icon {
    margin-left: -6px;
  }

  .iconEnd {
    margin-right: -6px;
  }

  /* Sizes */

  .button {
    padding: 5px 17px;
  }

  .button:where([data-icon-only="true"]) {
    padding: 9px;
  }

  .large {
    composes: large from "../typography/ButtonText/ButtonText.module.css";

    padding: 8px 19px;
    font-size: 18px;
  }

  .large[data-icon-only="true"] {
    padding: 8px;
  }

  .small {
    composes: small from "../typography/ButtonText/ButtonText.module.css";

    line-height: 1.142857143;
    padding: 3px 13px;
  }

  .small[data-icon-only="true"] {
    padding: 4px 8px;
  }

  .small .icon {
    margin-left: -4px;
  }

  .small .iconEnd {
    margin-right: -4px;
  }

  .button:where([data-icon-only="true"]) .icon,
  .button:where([data-icon-only="true"]) .iconEnd {
    margin: 0;
  }

  .button:where([data-read-only="true"]) {
    pointer-events: none;
  }

  /* Variants */
  /* Clear + outline */
  .button:where([data-variant="clear"]),
  .button[data-variant="outline"] {
    --color-button-background: transparent;
    --color-button-background-hover: var(--color-background-hover);
    --color-button-background-active: var(--color-background-active);
    --color-button-text-interactive: var(--color-text-interactive);
    --color-button-border: transparent;
  }

  /* Outline border */
  .button[data-variant="outline"] {
    --color-button-background: var(--color-background);
    --color-button-border: var(--color-border);
  }

  /* Filled dark */
  .button:where([data-variant="filled"]),
  .button:is(
      [data-variant="filled-light"]:is(
          [aria-selected="true"],
          [aria-current="page"]
        ),
      [data-variant="outline"]:is(
          [aria-selected="true"],
          [aria-current="page"]
        ),
      [data-variant="clear"]:is([aria-selected="true"], [aria-current="page"]),

    ) {
    --color-button-background: var(--color-background-inverted);
    --color-button-background-hover: var(--color-background-inverted-hover);
    --color-button-background-active: var(--color-background-inverted-active);
    --color-button-text-interactive: var(--color-text-inverted);
    --color-button-border: transparent;
  }

  /* Filled light */
  .button:where([data-variant="filled-light"]),
  .button:where([data-variant="filled"][aria-selected="true"]),
  .button:where([data-variant="filled"][aria-current="page"]) {
    --color-button-background: var(--color-background-subtle);
    --color-button-background-hover: var(--color-background-subtle-hover);
    --color-button-background-active: var(--color-background-subtle-active);
    --color-button-text-interactive: var(--color-text-interactive);
    --color-button-border: transparent;
  }

  .button:hover {
    background: var(--color-button-background-hover);
  }

  .button:active,
  .button[aria-expanded="true"] {
    background: var(--color-button-background-active);
  }

  /* Disabled */

  .button:not([data-is-loading="true"]):disabled,
  .button:hover:not([data-is-loading="true"]):disabled,
  .button:active:not([data-is-loading="true"]):disabled {
    background-color: var(--color-neutral-200);
    border-color: var(--color-neutral-500);
    color: var(--color-neutral-500);
    cursor: default;
  }

  .button:not([data-variant="clear"]):disabled {
    box-shadow: inset 0 0 0 1px var(--color-disabled-main);
  }
}
