import { StoryFn, Meta } from "@storybook/react-vite"

import Icon from "components/Icon/Icon"

import Button, { ButtonProps } from "../Button/Button"

export default {
  title: "Components/Button",
  component: Button,
  args: {
    children: "Button",
  },
  argTypes: {
    size: {
      control: {
        type: "radio",
        options: ["small", "default", "large"],
      },
    },
  },
} as Meta<ButtonProps<"button">>

const Template: StoryFn<ButtonProps<"button">> = (args) => {
  return <Button {...args} />
}

export const ButtonBasic = Template.bind({})

export const ButtonWithIcon = Template.bind({})
ButtonWithIcon.args = {
  icon: <Icon name="settings-line" />,
} as ButtonProps<"button">

export const ButtonWithIconEnd = Template.bind({})
ButtonWithIconEnd.args = {
  iconEnd: <Icon name="arrow-down-s-line" />,
} as ButtonProps<"button">
