import c from "classnames"
import { ElementType, ReactNode } from "react"

import { color } from "styles/colors"
import {
  createPolymorphicComponent,
  PolymorphicRef,
  PolymorphicRenderProps,
} from "utils/polymorphicTypes"

import { useButtonGroup } from "../ButtonGroup/ButtonGroup"
import styles from "./Button.module.css"

const sizeClassMap = {
  default: "",
  large: styles.large,
  small: styles.small,
}
const statusClassMap = {
  default: "",
  success: color.success,
  error: color.critical,
  warn: color.warning,
}
const defaultElement = "button"
export type ButtonOwnProps = {
  icon?: ReactNode
  iconEnd?: ReactNode
  loading?: boolean
  status?: keyof typeof statusClassMap
  variant?: "outline" | "clear" | "filled" | "filled-light"
  size?: keyof typeof sizeClassMap
  className?: string
  block?: boolean
  readOnly?: boolean
  children?: ReactNode
}

export type ButtonProps<C extends ElementType = typeof defaultElement> =
  PolymorphicRenderProps<C, ButtonOwnProps>

const ButtonInner = <C extends ElementType = typeof defaultElement>(
  {
    as,
    icon = null,
    iconEnd = null,
    loading,
    status = "default",
    variant: variantProp,
    size: sizeProp,
    className = "",
    children = null,
    block,
    readOnly,
    ...rest
  }: ButtonProps<C>,
  ref: PolymorphicRef<C>
) => {
  const { size: sizeContext, variant: variantContext } = useButtonGroup()
  const size = sizeProp || sizeContext || ("default" as const)
  const variant = variantProp || variantContext || ("outline" as const)

  const ComponentToRender = as || defaultElement

  return (
    <ComponentToRender
      className={c(
        styles.button,
        className,
        sizeClassMap[size],
        statusClassMap[status],
        block && styles.block
      )}
      data-size={size}
      data-variant={variant}
      data-read-only={readOnly}
      data-icon-only={!children}
      data-is-loading={loading}
      type={!as || as === "button" ? rest.type || "button" : undefined}
      {...rest}
      disabled={loading || rest.disabled}
      size={size}
      ref={ref}
    >
      {icon && (
        <span className={styles.icon} aria-hidden>
          {icon}
        </span>
      )}
      {children}
      {iconEnd && (
        <span className={styles.iconEnd} aria-hidden>
          {iconEnd}
        </span>
      )}
    </ComponentToRender>
  )
}

export const Button = createPolymorphicComponent<
  typeof defaultElement,
  ButtonOwnProps
>(ButtonInner, { displayName: "Button" })

export default Button
