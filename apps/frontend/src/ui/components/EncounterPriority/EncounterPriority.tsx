import c from "classnames"

import { Tooltip } from "components/Ariakit/Tooltip/Tooltip"
import isDefined from "utils/isDefined"

import { useUpdateEncounterMutation } from "generated/graphql"

import CountCircled from "../CountCircled/CountCircled"
import { InlineTextForm } from "../InlineTextForm/InlineTextForm"
import styles from "./EncounterPriority.module.css"

type EncounterPriorityProps = {
  id: string
  priority?: number
  className?: string
  showForm: boolean
  setShowForm: (showForm: boolean) => void
  size?: "default" | "small"
}

export const EncounterPriority = ({
  id,
  priority,
  className,
  showForm,
  setShowForm,
  size = "default",
}: EncounterPriorityProps) => {
  const [updateEncounter] = useUpdateEncounterMutation()
  return (
    <span
      className={c(styles.wrap, className, size === "small" && styles.small)}
    >
      {showForm ? (
        <InlineTextForm
          value={priority || ""}
          label="Update priority"
          type="number"
          onSubmit={({ value }) => {
            updateEncounter({
              variables: {
                id,
                input: {
                  priority: {
                    set: value === "" ? null : parseInt(value) || 0,
                  },
                },
              },
            })
            setShowForm(false)
          }}
          onCancel={() => {
            setShowForm(false)
          }}
          size={size}
          actionIcons
          maxNumber={100}
          minNumber={0}
        />
      ) : (
        <Tooltip tooltipContent="Click to edit priority" placement="bottom">
          <button
            className={styles.priority}
            onClick={() => {
              setShowForm(true)
            }}
          >
            <CountCircled className={styles.count}>
              {isDefined(priority) ? priority : "-"}
            </CountCircled>
          </button>
        </Tooltip>
      )}
    </span>
  )
}
