import { <PERSON>, <PERSON>a } from "@storybook/react-vite"

import { IconButton, IconButtonProps } from "./IconButton"

export default {
  title: "UI Library/ui/Icon Button",
  argTypes: {
    size: {
      control: {
        type: "select",
        options: ["default", "small", "large"],
      },
      defaultValue: "default",
    },
  },
} as Meta

const Template: Story<IconButtonProps> = (args) => (
  <IconButton {...args} iconName="Bell" />
)

export const Default = Template.bind({})
Default.args = {
  size: "default",
}

export const Small = Template.bind({})

Small.args = {
  size: "small",
}

export const Large = Template.bind({})

Large.args = {
  size: "large",
}

export const Disabled = Template.bind({})

Disabled.args = {
  readOnly: true,
}
