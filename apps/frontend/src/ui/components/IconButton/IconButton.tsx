import { forwardRef } from "react"

import { IconName } from "@leviosa/assets"

import Icon from "components/Icon/Icon"

import styles from "./IconButton.module.css"

export type IconButtonOwnProps = {
  iconName: IconName
  variant?: "clear" | "filled"
  size?: "default" | "large" | "small" | "xlarge"
  readOnly?: boolean
}

export type IconButtonProps = IconButtonOwnProps &
  React.ButtonHTMLAttributes<HTMLButtonElement>

export const IconButton = forwardRef<HTMLButtonElement, IconButtonProps>(
  (
    {
      iconName,
      variant = "clear",
      size = "default",
      className = "",
      type = "button",
      readOnly,
      ...rest
    },
    ref
  ) => {
    return (
      <button
        type={type}
        className={`${styles.button} ${className}`}
        data-size={size}
        data-variant={variant}
        data-read-only={readOnly}
        disabled={readOnly}
        {...rest}
        ref={ref}
      >
        <Icon name={iconName} />
      </button>
    )
  }
)
