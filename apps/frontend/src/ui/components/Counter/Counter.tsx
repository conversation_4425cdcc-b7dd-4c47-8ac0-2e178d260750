import c from "classnames"

import { Text } from "../typography/Text/Text"
import styles from "./Counter.module.css"

// Letting className override default bg/fg colors instead of introducing hardcoded "variants".
// A size prop may be introduced later as needed
type CounterProps = {
  // accepting number only (not children) to guarantee correct styling
  number: number
  className?: string
}

export function Counter({ number, className }: CounterProps): JSX.Element {
  return (
    <Text weight="bold" className={c(styles.wrapper, className)}>
      {number}
    </Text>
  )
}
