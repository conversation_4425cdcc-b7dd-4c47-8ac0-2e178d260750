import { useNavigate } from "@remix-run/react"
import { useTranslation } from "react-i18next"

import ComputerIllustration from "@leviosa/assets/illustrations/computer.svg?react"
import { But<PERSON> } from "@leviosa/components"
import { useTimeout } from "@leviosa/utils"

import styles from "./NoAppointment.module.css"

const NoAppointment = () => {
  const navigate = useNavigate()
  const { t } = useTranslation()

  // Navigate back to checkin screen after 20 seconds
  useTimeout(() => {
    navigate(-1)
  }, 20000)

  return (
    <div className={styles.container}>
      <ComputerIllustration />
      <h2 className={styles.heading}>{t("noAppointmentFound")}</h2>
      <p>{t("pleaseContactReception")}</p>
      <Button onClick={() => navigate("/checkin")}>
        {t("goBackToMainScreen")}
      </Button>
    </div>
  )
}

export default NoAppointment
