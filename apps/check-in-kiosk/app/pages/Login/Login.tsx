import { Form } from "@remix-run/react"
import { useTranslation } from "react-i18next"
import <PERSON>tie from "react-lottie-player"

import SignatureIllustration from "@leviosa/assets/illustrations/signature.svg?react"
//@ts-expect-error fix later
import <PERSON><PERSON><PERSON><PERSON> from "@leviosa/assets/leviosa/merki_hreyfing.json"
import { LanguageSwitcher, Icon } from "@leviosa/components"

import ErrorMessage from "app/components/ErrorMessage/ErrorMessage"
import { availableLanguages } from "app/utils/availableLanguages"

import Numpad from "../../components/Numpad/Numpad"
import styles from "./Login.module.css"

type LoginProps = {
  loading: boolean
  validationError?: string
  error?: string
  onFormUpdate?: () => void
}

const Login = ({
  validationError,
  error,
  loading,
  onFormUpdate,
}: LoginProps) => {
  const { i18n, t } = useTranslation()

  const currentLanguage = i18n.language

  const changeLanguage = () => {
    if (currentLanguage === availableLanguages[0].value) {
      i18n.changeLanguage(availableLanguages[1].value)
    } else {
      i18n.changeLanguage(availableLanguages[0].value)
    }
  }

  return (
    <div className={styles.container}>
      {loading && (
        <div className={styles.loading}>
          <Icon name="loader-4-line" spin fontSize={40} />
          <p
            dangerouslySetInnerHTML={{
              __html: t("sendingAuthenticationRequest"),
            }}
          />
        </div>
      )}
      <div className={styles.loginLeftContainer}>
        <Lottie
          className={styles.logo}
          play
          loop
          animationData={LeviosaLogo}
        ></Lottie>
        <h1 className={styles.heading}>
          Healthtech <br /> solutions built by <br /> and for healthcare <br />
          providers
          <SignatureIllustration className={styles.signature} />
        </h1>
      </div>
      <div className={styles.loginRightContainer}>
        <div className={styles.languageSwitcher}>
          <LanguageSwitcher
            availableLanguages={availableLanguages}
            currentLanguage={currentLanguage}
            changeLanguage={changeLanguage}
          />
        </div>
        {error ? (
          <ErrorMessage message={t("loginErrorMessage")} />
        ) : (
          <>
            <h2 className={styles.loginHeading}>{t("providerLogin")}</h2>
            <p className={styles.loginInformation}>
              {t("providerLoginDescription")}
            </p>
            <Form method="post">
              <Numpad
                validationError={validationError}
                placeholder="000-0000"
                submitLabel="login"
                onFormUpdate={onFormUpdate}
              />
            </Form>
          </>
        )}
      </div>
    </div>
  )
}

export default Login
