import { startOfMonth, addMonths, getDay, addDays } from "date-fns"

// Function to get the first Monday of the next month
export default function getFirstMondayOfNextMonth(currentDate = new Date()) {
  // Find the first day of the next month
  const firstDayNextMonth = startOfMonth(addMonths(currentDate, 1))

  // Determine the day of the week for the first day of the next month (0 = Sunday, 1 = Monday, ...)
  const dayOfWeek = getDay(firstDayNextMonth)

  // Calculate how many days to add to get to the next Monday
  // If it's already Monday (dayOfWeek === 1), no days need to be added.
  // If dayOfWeek is 0 (Sunday), we add 1 day to get to Monday.
  // Otherwise, we add the necessary days to wrap around to the next Monday.
  const daysToAdd = dayOfWeek === 0 ? 1 : (8 - dayOfWeek) % 7

  // Get the first Monday of the next month by adding the calculated days
  return addDays(firstDayNextMonth, daysToAdd)
}
