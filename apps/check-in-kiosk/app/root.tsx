import {
  isRouteErrorResponse,
  json,
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useRevalidator,
  useRouteError,
} from "@remix-run/react"
import "@reykjavik/webtools/fixIcelandicLocale"
import { captureRemixErrorBoundaryError } from "@sentry/remix"
import { I18nextProvider } from "react-i18next"

import "@leviosa/assets/styles/000_default.css"
import "@leviosa/assets/styles/reset.css"
import "@leviosa/assets/styles/variables.css"
import { useInterval } from "@leviosa/utils"

import ErrorPage from "app/pages/Error/Error"
import { version } from "app/utils/version"

import { i18nInstance } from "./i18n"
import "./root.css"

export function loader() {
  return json({ version })
}

const hour = 60 * 60 * 1000
let prevVersion = ""

export function Layout({ children }: { children: React.ReactNode }) {
  return (
    <html lang="en" className="new-branding">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <link rel="icon" href="/favicon.ico" sizes="32x32" />
        <link rel="icon" href="/icon.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/apple-touch-icon.png" />
        <link rel="manifest" href="/manifest.webmanifest" />
        <link rel="stylesheet" href="/fonts/fonts.css" />
        <Meta />
        <Links />
      </head>
      <body>
        <I18nextProvider i18n={i18nInstance}>
          {children}
          <ScrollRestoration />
          <Scripts />
        </I18nextProvider>
      </body>
    </html>
  )
}
export default function App() {
  const data = useLoaderData<typeof loader>()
  const revalidator = useRevalidator()

  useInterval(() => {
    revalidator.revalidate()
  }, hour)

  if (!prevVersion) {
    prevVersion = data.version
  }

  if (data.version !== prevVersion) {
    window.location.reload()
  }
  return <Outlet />
}

export function ErrorBoundary() {
  const error = useRouteError()

  if (!(error instanceof Error)) {
    // Capture non-Error objects as a string since Sentry only accepts Error objects
    captureRemixErrorBoundaryError(
      Error(JSON.stringify(error) || "Unknown error")
    )
  } else {
    captureRemixErrorBoundaryError(error)
  }

  if (isRouteErrorResponse(error)) {
    return <ErrorPage message={error.data} />
  }

  if (error instanceof Error) {
    return <ErrorPage message={error.message} />
  }

  return <ErrorPage />
}
