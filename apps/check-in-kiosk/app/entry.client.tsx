/**
 * By default, <PERSON> will handle hydrating your app on the client for you.
 * You are free to delete this file if you'd like to, but if you ever want it revealed again, you can run `npx remix reveal` ✨
 * For more information, see https://remix.run/file-conventions/entry.client
 */
import { RemixBrowser } from "@remix-run/react"
import * as Sentry from "@sentry/remix"
import { startTransition, StrictMode } from "react"
import { hydrateRoot } from "react-dom/client"

Sentry.init({
  dsn: import.meta.env.VITE_SENTRY_DSN,
  environment: import.meta.env.VITE_SENTRY_ENVIRONMENT || "development",
  tracesSampleRate: Number(import.meta.env.VITE_SENTRY_TRACES_SAMPLE_RATE) || 1,
  sampleRate: Number(import.meta.env.VITE_SENTRY_SAMPLE_RATE) || 0.05,
  profilesSampleRate:
    Number(import.meta.env.VITE_SENTRY_PROFILES_SAMPLE_RATE) || 0.05,
  autoInstrumentRemix: true,
})

startTransition(() => {
  hydrateRoot(
    document,
    <StrictMode>
      <RemixBrowser />
    </StrictMode>
  )
})
