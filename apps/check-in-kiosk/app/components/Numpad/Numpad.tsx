import c from "classnames"
import { useState } from "react"
import { useTranslation } from "react-i18next"

import { Button, Icon, Input } from "@leviosa/components"

import styles from "./Numpad.module.css"

const NumberButton = ({
  number,
  onClick,
}: {
  number: string
  onClick: (n: string) => void
}) => (
  <button
    data-testid={`number-button-${number}`}
    className={c(styles.button, styles.numberButton)}
    onClick={() => onClick(number)}
    type="button"
  >
    {number}
  </button>
)

type NumpadProps = {
  validationError?: string
  placeholder?: string
  submitLabel?: string
  onFormUpdate?: () => void
}

const Numpad = ({
  validationError,
  placeholder,
  submitLabel = "login",
  onFormUpdate,
}: NumpadProps) => {
  const { t } = useTranslation()
  const [currentNumber, setCurrentNumber] = useState("")

  const handleNumberClick = (number: string) => {
    setCurrentNumber(currentNumber + number)
    onFormUpdate?.()
  }

  const handleBackspace = () => {
    if (currentNumber.length > 0) {
      setCurrentNumber(currentNumber.slice(0, -1))
      onFormUpdate?.()
    }
  }

  return (
    <div className={styles.container}>
      <div className={styles.inputContainer}>
        <Input
          type="text"
          name="input"
          data-testid="numpad-input"
          autoFocus
          readOnly
          value={currentNumber}
          placeholder={placeholder}
          validationError={validationError ? t(validationError) : ""}
          inputMode="none"
          onPaste={(e) => {
            // Allow pasting into the input for debugging purposes
            e.preventDefault()
            const pastedData = e.clipboardData.getData("text/plain")
            handleNumberClick(pastedData)
          }}
        />
      </div>
      <div className={styles.numpadContainer}>
        <div className={styles.row}>
          <NumberButton number="1" onClick={handleNumberClick} />
          <NumberButton number="2" onClick={handleNumberClick} />
          <NumberButton number="3" onClick={handleNumberClick} />
        </div>
        <div className={styles.row}>
          <NumberButton number="4" onClick={handleNumberClick} />
          <NumberButton number="5" onClick={handleNumberClick} />
          <NumberButton number="6" onClick={handleNumberClick} />
        </div>
        <div className={styles.row}>
          <NumberButton number="7" onClick={handleNumberClick} />
          <NumberButton number="8" onClick={handleNumberClick} />
          <NumberButton number="9" onClick={handleNumberClick} />
        </div>
        <div className={styles.row}>
          <button
            className={c(styles.button, styles.numberButton)}
            onClick={handleBackspace}
            data-testid="backspace-button"
            type="button"
          >
            <Icon name="delete-back-2-fill" />
          </button>
          <NumberButton number="0" onClick={handleNumberClick} />
          <div className={styles.actionButton}>
            <Button type="submit" data-testid="checkin-button">
              {t(submitLabel)}
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Numpad
