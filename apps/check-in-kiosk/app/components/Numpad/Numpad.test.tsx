import "@testing-library/jest-dom"
import { render, fireEvent, screen } from "@testing-library/react"

import Numpad from "./Numpad"

describe("Numpad", () => {
  it("renders the input field with the provided placeholder", () => {
    const placeholder = "Enter a number"
    render(<Numpad placeholder={placeholder} />)

    const inputElement = screen.getByPlaceholderText(placeholder)
    expect(inputElement).toBeInTheDocument()
  })

  it("updates the input field when a number button is clicked", () => {
    render(<Numpad />)

    const numberButton = screen.getByTestId("number-button-1")
    fireEvent.click(numberButton)

    const inputElement = screen.getByTestId("numpad-input")
    expect(inputElement).toHaveValue("1")
  })

  it("removes one character from input field when the backspace button is clicked", () => {
    render(<Numpad />)

    const number1Button = screen.getByText("1")
    fireEvent.click(number1Button)

    const number2Button = screen.getByText("2")
    fireEvent.click(number2Button)

    const number3Button = screen.getByText("3")
    fireEvent.click(number3Button)

    const backspaceButton = screen.getByTestId("backspace-button")
    fireEvent.click(backspaceButton)

    const inputElement = screen.getByRole("textbox")
    expect(inputElement).toHaveValue("12")
  })

  it("renders the input field with the provided placeholder", () => {
    const placeholder = "Enter a number"
    render(<Numpad placeholder={placeholder} />)

    const inputElement = screen.getByPlaceholderText(placeholder)
    expect(inputElement).toBeInTheDocument()
  })

  it("displays the validation error message when validationError prop is provided", () => {
    const validationError = "Invalid number"
    render(<Numpad validationError={validationError} />)

    const validationErrorMessage = screen.getByText(validationError)
    expect(validationErrorMessage).toBeInTheDocument()
  })
})
