import { useState, useEffect, useMemo, useRef } from "react"
import { useTranslation } from "react-i18next"

import styles from "./Clock.module.css"

const Clock = () => {
  const { i18n } = useTranslation()
  const [dateTime, setDateTime] = useState(new Date())

  const intervalRef = useRef<ReturnType<typeof setInterval>>()

  useEffect(() => {
    intervalRef.current = setInterval(() => {
      setDateTime(new Date())
    }, 1000)

    // Clear the interval on component unmount
    return () => clearInterval(intervalRef.current)
  }, [])

  // Memoize the formatted date and time
  const formattedDate = useMemo(() => {
    const formatLocale = i18n.language === "IS" ? "is-IS" : "en-GB"

    const dateFormatter = new Intl.DateTimeFormat(formatLocale, {
      dateStyle: "full",
      timeStyle: undefined,
    })

    let parts = dateFormatter.formatToParts(dateTime)

    // filter out the "inn" from days in Icelandic
    if (i18n.language === "IS") {
      parts = parts.map((part) => {
        if (part.type === "literal" && part.value.includes("inn")) {
          part.value = part.value.replace("inn", ", ")
          return part
        }

        return part
      })
    }

    // Concatenate the remaining parts into the desired format
    const formattedDate = parts.map((part) => part.value).join("")

    const timeFormatter = new Intl.DateTimeFormat(formatLocale, {
      timeStyle: "short",
    })

    const formattedTime = timeFormatter.format(dateTime)

    return {
      date: formattedDate,
      time: formattedTime,
    }
  }, [dateTime, i18n.language])

  return (
    <div className={styles.container}>
      <p className={styles.date}>{formattedDate.date}</p>
      <p className={styles.time}>{formattedDate.time}</p>
    </div>
  )
}

export default Clock
