import { useState } from "react"
import { useTranslation } from "react-i18next"
import <PERSON>tie from "react-lottie-player"

//@ts-expect-error fix later
import <PERSON><PERSON><PERSON><PERSON> from "@leviosa/assets/leviosa/merki_hreyfing.json"
import { LanguageSwitcher } from "@leviosa/components"

import { availableLanguages } from "app/utils/availableLanguages"

import Clock from "../Clock/Clock"
import styles from "./Header.module.css"

const Header = ({ logoUrl }: { logoUrl?: string }) => {
  const { i18n } = useTranslation()
  const [imgError, setImgError] = useState(false)

  const currentLanguage = i18n.language

  const changeLanguage = () => {
    if (currentLanguage === availableLanguages[0].value) {
      i18n.changeLanguage(availableLanguages[1].value)
    } else {
      i18n.changeLanguage(availableLanguages[0].value)
    }
  }

  return (
    <header className={styles.container}>
      {logoUrl && !imgError ? (
        <img
          className={styles.logo}
          src={logoUrl}
          alt="organisation-logo"
          onError={() => setImgError(true)}
        />
      ) : (
        <div />
      )}
      <Clock />
      <div className={styles.rightContainer}>
        <LanguageSwitcher
          availableLanguages={availableLanguages}
          currentLanguage={currentLanguage}
          changeLanguage={changeLanguage}
        />
        <Lottie
          className={styles.logo}
          play
          loop
          animationData={LeviosaLogo}
        ></Lottie>
      </div>
    </header>
  )
}

export default Header
